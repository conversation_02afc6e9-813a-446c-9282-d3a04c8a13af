import request from '@/utils/request'
// 库存查询
export function groupByProduct(data) {
    return request({
        url: '/mesapp-service/warehouse/groupByProduct',
        method: 'post',
        data: data
    })
}
// 库存分布
export function selectByProduct(data) {
    return request({
        url: '/mesapp-service/warehouse/selectByProduct',
        method: 'post',
        data: data
    })
}
// 库存导出
export function groupByProductExport(data) {
    return request({
        url: '/mesapp-service/warehouse/groupByProduct/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 入库记录
export function inventoryPage(data) {
    return request({
        url: '/mesapp-service/inventory/page',
        method: 'post',
        data: data
    })
}
// 入库记录 -- 详情
export function inventoryInfo(data) {
    return request({
        url: '/mesapp-service/inventory/info',
        method: 'post',
        data: data
    })
}
// 扫码明细
export function scanRecord(data) {
    return request({
        url: '/mesapp-service/inventory/selectScanRecordByProduct',
        method: 'post',
        data: data
    })
}
// 按单据导出
export function inventoryExport(data) {
    return request({
        url: '/mesapp-service/inventory/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 入库记录 ---明细
export function pageProduct(data) {
    return request({
        url: '/mesapp-service/inventory/pageProduct',
        method: 'post',
        data: data
    })
}
// 入库记录 --- 按明细
export function pageProductExport(data) {
    return request({
        url: '/mesapp-service/inventory/pageProduct/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
//  新增出入库任务
export function inventoryAdd(data) {
    return request({
        url: '/mesapp-service/inventory/add',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 新增
export function inventoryCheckAdd(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/add',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 新增 - 产品列表
export function inventoryCheckDetailPage(data) {
    return request({
        url: '/mesapp-service/warehouse/detail/page',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 列表
export function inventoryCheckPage(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/page',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 编辑
export function inventoryCheckUpdate(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/update',
        method: 'post',
        data: data
    })
}
// 盘点记录 - 分页 - 导出
export function inventoryCheckExport(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 盘点任务 - 详情
export function inventoryCheckInfo(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/info',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 关闭
export function inventoryCheckClose(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/close',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 开启
export function inventoryCheckOpen(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/open',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 删除
export function inventoryCheckDelete(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/delete',
        method: 'post',
        data: data
    })
}
// 盘点任务 - 审核
export function inventoryCheckAudit(data) {
    return request({
        url: '/mesapp-service/inventoryCheck/audit',
        method: 'post',
        data: data
    })
}

// 盘点任务 - 仓库管理员列表
export function selectManager(data) {
    return request({
        url: '/mesapp-service/warehouse/selectManager',
        method: 'post',
        data: data
    })
}


