import request from '@/utils/request'
// 入厂统计
export function checkInDetail(data) {
    return request({
        url: 'mesapp-service/checkInDetail/statistics',
        method: 'post',
        data: data
    })
}
//屠宰统计
export function butcherWeight(data) {
    return request({
        url: 'mesapp-service/butcherWeight/statistics',
        method: 'post',
        data: data
    })
}
//入厂导出
export function checkInDetailExport(data) {
    return request({
        url: 'mesapp-service/checkInDetail/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
//屠宰导出
export function butcherWeightExport(data) {
    return request({
        url: 'mesapp-service/butcherWeight/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
//排酸统计
export function acidTask(data) {
    return request({
        url: 'mesapp-service/acidTask/statistics',
        method: 'post',
        data: data,
    })
}
//排酸统计汇总
export function acidTaskTotal(data) {
    return request({
        url: 'mesapp-service/acidTask/statistics/total',
        method: 'post',
        data: data,
    })
}

//排酸统计导出
export function acidTaskExport(data) {
    return request({
        url: 'mesapp-service/acidTask/statistics/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

// 导出数据
export function settlementExport(data) {
    return request({
        url: 'mesapp-service/settlement/statistics/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 生产报表
export function reportProduceList(data) {
    return request({
        url: 'mesapp-service/reportProduce/page',
        method: 'post',
        data: data
    })
}
// 生产报表 --- 新增前查询
export function infoForAdd(data) {
    return request({
        url: 'mesapp-service/reportProduce/infoForAdd',
        method: 'post',
        data: data
    })
}
// 生产报表 --- 新增
export function reportProduceAdd(data) {
    return request({
        url: 'mesapp-service/reportProduce/add',
        method: 'post',
        data: data
    })
}
// 生产报表 --- 编辑
export function reportProduceupdate(data) {
    return request({
        url: 'mesapp-service/reportProduce/update',
        method: 'post',
        data: data
    })
}
// 生产报表 --- 详情
export function reportProduceInfo(data) {
    return request({
        url: 'mesapp-service/reportProduce/info',
        method: 'post',
        data: data
    })
}
// 生产报表 --- 删除
export function reportProduceDelete(data) {
    return request({
        url: 'mesapp-service/reportProduce/delete',
        method: 'post',
        data: data
    })
}
// 导出数据
export function reportProduceExport(data) {
    return request({
        url: 'mesapp-service/reportProduce/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 生产统计
export function productStatistics(data) {
    return request({
        url: 'mesapp-service/product/statistics',
        method: 'post',
        data: data
    })
}
// 生产统计导出
export function productStatisticsExport(data) {
    return request({
        url: 'mesapp-service/product/statistics/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 屠宰统计
export function butcherWeightPage(data) {
    return request({
        url: '/mesapp-service/butcherWeight/page',
        method: 'post',
        data: data
    })
}
// 屠宰统计
export function butcherWeightPageExport(data) {
    return request({
        url: '/mesapp-service/butcherWeight/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 出排酸检斤, 二次检斤
export function carcassWeightPage(data) {
    return request({
        url: '/mesapp-service/carcassWeight/page',
        method: 'post',
        data: data
    })
}
export function carcassWeightPageExport(data) {
    return request({
        url: '/mesapp-service/carcassWeight/page/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 出肉率
export function meatYield(data) {
    return request({
        url: '/mesapp-service/butcherWeight/meatYieldPage',
        method: 'post',
        data: data
    })
}
// 出肉率导出
export function meatYieldExport(data) {
    return request({
        url: '/mesapp-service/butcherWeight/meatYield/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
