import request from '@/utils/request'
// 申请溯源
export function applyAdd(data) {
    return request({
        url: 'mesapp-service/traceCode/apply/add',
        method: 'post',
        data: data
    })
}
// 溯源码列表
export function applyList(data) {
    return request({
        url: 'mesapp-service/traceCode/apply/page',
        method: 'post',
        data: data
    })
}
// 溯源码列表--- 激活列表
export function traceCodeList(data) {
    return request({
        url: 'mesapp-service/traceCode/page',
        method: 'post',
        data: data
    })
}
// 溯源码 导出
export function applyExport(data) {
    return request({
        url: '/mesapp-service/traceCode/apply/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 溯源码 下载
export function traceCodeExport(data) {
    return request({
        url: 'mesapp-service/traceCode/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 溯源码 生成
export function traceCodeCreate(data) {
    return request({
        url: 'mesapp-service/traceCode/create',
        method: 'post',
        data: data
    })
}
// 溯源模版分页
export function traceTemplateList(data) {
    return request({
        url: 'mesapp-service/traceTemplate/page',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 新增 -- 默认模版返显
export function selectDefault(data) {
    return request({
        url: 'mesapp-service/traceTemplate/selectDefault',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 系统模版详情
export function selectSystem(data) {
    return request({
        url: 'mesapp-service/traceTemplate/selectSystem',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 详情
export function traceTemplateInfo(data) {
    return request({
        url: 'mesapp-service/traceTemplate/info',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 新增
export function traceTemplateAdd(data) {
    return request({
        url: 'mesapp-service/traceTemplate/add',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 编辑
export function traceTemplateUpdate(data) {
    return request({
        url: 'mesapp-service/traceTemplate/update',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 删除
export function traceTemplateDelete(data) {
    return request({
        url: 'mesapp-service/traceTemplate/delete',
        method: 'post',
        data: data
    })
}
// 溯源模版 -- 设为默认
export function traceTemplateSetDefault(data) {
    return request({
        url: 'mesapp-service/traceTemplate/setDefault',
        method: 'post',
        data: data
    })
}
// 打码模版列表
export function codingTemplateList(data) {
    return request({
        url: 'mesapp-service/codingTemplate/page',
        method: 'post',
        data: data
    })
}
// 打码模版新增
export function codingTemplateAdd(data) {
    return request({
        url: 'mesapp-service/codingTemplate/add',
        method: 'post',
        data: data
    })
}
// 打码模版详情
export function codingTemplateInfo(data) {
    return request({
        url: 'mesapp-service/codingTemplate/info',
        method: 'post',
        data: data
    })
}
// 打码模版删除
export function codingTemplateDelete(data) {
    return request({
        url: 'mesapp-service/codingTemplate/delete',
        method: 'post',
        data: data
    })
}
// 打码模版编辑
export function codingTemplateUpdate(data) {
    return request({
        url: 'mesapp-service/codingTemplate/update',
        method: 'post',
        data: data
    })
}
// 溯源明细详情
export function codingTraceInfo(data) {
    return request({
        url: 'mesapp-service/codingTrace/info',
        method: 'post',
        data: data
    })
}
// 溯源明细 - 溯源码
export function pageTraceCodeList(data) {
    return request({
        url: 'mesapp-service/codingTrace/pageTraceCode',
        method: 'post',
        data: data
    })
}
// 溯源明细列表
export function codingTraceList(data) {
    return request({
        url: 'mesapp-service/codingTrace/page',
        method: 'post',
        data: data
    })
}
// 溯源明细 - 批量编辑
export function batchUpdate(data) {
    return request({
        url: 'mesapp-service/codingTrace/batchUpdate',
        method: 'post',
        data: data
    })
}
// 溯源明细 - 导出
export function codingTraceExport(data) {
    return request({
        url: 'mesapp-service/codingTrace/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}

// 企业列表
export function enterpriseList(data) {
    return request({
        url: 'admin-service/system/enterprise/list',
        method: 'get',
        params: data
    })
}
// 溯源管理 - 查询可用数量
export function availableNums(data) {
    return request({
        url: 'mesapp-service/traceCode/availableNums',
        method: 'post',
        data: data
    })
}
