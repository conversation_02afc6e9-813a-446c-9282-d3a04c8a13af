import request from '@/utils/request'

// 下货任务分页列表
export function offalTaskPage(data) {
    return request({
        url: '/mesapp-service/offalTask/page',
        method: 'post',
        data: data
    })
}

// 下货任务详情
export function offalTaskInfo(data) {
    return request({
        url: '/mesapp-service/offalTask/info',
        method: 'post',
        data: data
    })
}

// 下货产品列表
export function offalProductList(data) {
    return request({
        url: '/mesapp-service/product/offal/list',
        method: 'post',
        data: data
    })
}
// 下货称重列表
export function offalWeightPage(data) {
    return request({
        url: '/mesapp-service/offalTask/weight/page',
        method: 'post',
        data: data
    })
}


// 客户列表
export function customerPage(data) {
    return request({
        url: '/mesapp-service/offalCustomer/page',
        method: 'post',
        data: data
    })
}

// 客户详情
export function customerInfo(data) {
    return request({
        url: '/mesapp-service/offalCustomer/info',
        method: 'post',
        data: data
    })
}

// 新增客户
export function customerAdd(data) {
    return request({
        url: '/mesapp-service/offalCustomer/add',
        method: 'post',
        data: data
    })
}

// 编辑客户
export function customerUpdate(data) {
    return request({
        url: '/mesapp-service/offalCustomer/update',
        method: 'post',
        data: data
    })
}

// 客户列表-不分页
export function customerList(data) {
    return request({
        url: '/mesapp-service/offalCustomer/list',
        method: 'post',
        data: data
    })
}

// 删除客户
export function customerDelete(data) {
    return request({
        url: '/mesapp-service/offalCustomer/delete',
        method: 'post',
        data: data
    })
}