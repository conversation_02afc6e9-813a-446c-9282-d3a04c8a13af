import request from '@/utils/request'
// 采购计划列表
export function purchasePlanList(data) {
    return request({
        url: 'mesapp-service/purchasePlan/page',
        method: 'post',
        data: data
    })
}
// 采购计划列表 -- 按原料分组列表
export function groupByMaterials(data) {
    return request({
        url: 'mesapp-service/purchaseStandard/groupByMaterials',
        method: 'post',
        data: data
    })
}
// 采购计划详情
export function purchasePlanInfo(data) {
    return request({
        url: 'mesapp-service/purchasePlan/info',
        method: 'post',
        data: data
    })
}
// 采购计划禁用
export function purchasePlanClose(data) {
    return request({
        url: 'mesapp-service/purchasePlan/close',
        method: 'post',
        data: data
    })
}
// 采购计划 同步数据到畜牧帮
export function publicFlag(data) {
    return request({
        url: 'mesapp-service/purchasePlan/publicFlag',
        method: 'post',
        data: data
    })
}
// 原料名称
export function materialsList(data) {
    return request({
        url: 'mesapp-service/materials/page',
        method: 'post',
        data: data
    })
}
//原料等级
export function materialsLevelList(data) {
    return request({
        url: 'mesapp-service/materialsLevel/page',
        method: 'post',
        data: data
    })
}
//规格新增
export function purchaseStandardAdd(data) {
    return request({
        url: 'mesapp-service/purchaseStandard/add',
        method: 'post',
        data: data
    })
}
//规格查询
export function purchaseStandardList(data) {
    return request({
        url: 'mesapp-service/purchaseStandard/page',
        method: 'post',
        data: data
    })
}
//价格维护
export function purchasePlanadd(data) {
    return request({
        url: 'mesapp-service/purchasePlan/add',
        method: 'post',
        data: data
    })
}
// 采购计划   新增校验
export function purchasePlanCheck(data) {
    return request({
        url: 'mesapp-service/purchasePlan/check',
        method: 'post',
        data: data
    })
}

//价格审核
export function purchasePlanAudit(data) {
    return request({
        url: 'mesapp-service/purchasePlan/audit',
        method: 'post',
        data: data
    })
}
//导出
export function purchasePlanExport(data) {
    return request({
        url: 'mesapp-service/purchasePlan/export',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}
// 预约单管理列表
export function appointList(data) {
    return request({
        url: 'mesapp-service/checkInAppointment/page',
        method: 'post',
        data: data
    })
}
// 预约单管理详情
export function appointInfo(data) {
    return request({
        url: 'mesapp-service/checkIn/info',
        method: 'post',
        data: data
    })
}
// 入厂登记 -- 列表
export function checkInList(data) {
    return request({
        url: 'mesapp-service/checkIn/page',
        method: 'post',
        data: data
    })
}
// 入厂登记 -- 导出
export function checkInExport(data) {
    return request({
        url: 'mesapp-service/checkIn/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 入厂登记 -- 新增
export function checkInAdd(data) {
    return request({
        url: 'mesapp-service/checkIn/add',
        method: 'post',
        data: data
    })
}
// 入厂登记 -- 确认入厂
export function checkInUpdate(data) {
    return request({
        url: 'mesapp-service/checkIn/update',
        method: 'post',
        data: data
    })
}
// 入厂登记 -- 详情
export function checkInInfo(data) {
    return request({
        url: 'mesapp-service/checkIn/info',
        method: 'post',
        data: data
    })
}
// 入厂登记 -- 取消
export function checkInClose(data) {
    return request({
        url: 'mesapp-service/checkIn/close',
        method: 'post',
        data: data
    })
}
// 屠宰分配
export function butcherUpdate(data) {
    return request({
        url: 'mesapp-service/butcher/update',
        method: 'post',
        data: data
    })
}
// 屠宰取消
export function butcherClose(data) {
    return request({
        url: 'mesapp-service/butcher/close',
        method: 'post',
        data: data
    })
}
// 宰前检疫
export function checkInCert(data) {
    return request({
        url: 'mesapp-service/checkIn/fillQuarantineCert',
        method: 'post',
        data: data
    })
}
// 宰后检疫
export function butcherCert(data) {
    return request({
        url: 'mesapp-service/butcher/fillQuarantineCert',
        method: 'post',
        data: data
    })
}