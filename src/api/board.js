import request from '@/utils/request'

// 库存安全监控列表
export function listOfInventoryLevel(data) {
    return request({
        url: '/mesapp-service/warehouse/listOfInventoryLevel',
        method: 'post',
        data: data
    })
}

// 库存总量
export function selectTotalNum(data) {
    return request({
        url: '/mesapp-service/inventory/view/selectTotalNum',
        method: 'post',
        data: data
    })
}

// 批次分布
export function selectInventoryList(data) {
    return request({
        url: '/mesapp-service/inventory/view/selectBatchNum',
        method: 'post',
        data: data
    })
}

// 库存列表
export function inventoryPage2(data) {
    return request({
        url: '/mesapp-service/inventory/page2',
        method: 'post',
        data: data
    })
}