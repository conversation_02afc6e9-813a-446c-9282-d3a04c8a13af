import request from '@/utils/request'
let materialsType=2
// 活畜屠宰-头部统计
export function singleCount(data) {
    return request({
        url: 'mesapp-service/showScreen/produce/singleCount',
        method: 'post',
        data: data
    })
}



// 活畜屠宰-活畜收购交易统计
//活畜屠宰详情-收购数量与结算金额统计
export function trendOfButcher(data) {
	data.materialsType=materialsType
    return request({
        url: 'mesapp-service/showScreen/trendOfButcher',
        method: 'post',
        data: data
    })
}

// 活畜屠宰-屠宰加工企业交易额排行
export function companyList(data) {
    return request({
        url: 'mesapp-service/showScreen/companyRank',
        method: 'post',
        data: data
    })
}
// 活畜屠宰-加工企业当前库存统计
export function trendOfInventory(data) {
    return request({
        url: 'mesapp-service/showScreen/trendOfInventory',
        method: 'post',
        data: data
    })
}

// 活畜屠宰-企业生产统计
export function companyRankInFreeze(data) {
    return request({
        url: 'mesapp-service/showScreen/companyRankInFreeze',
        method: 'post',
        data: data
    })
}


// 活畜屠宰详情-企业信息
export function queryEnterpriseAdmin(data) {
    return request({
        url: 'mesapp-service/enterprise/queryEnterpriseAdmin',
        method: 'post',
        data: data
    })
}
// 活畜屠宰详情-屠宰收购活畜类型
export function materialsPercent(data) {
	data.materialsType=materialsType
    return request({
        url: 'mesapp-service/showScreen/materialsPercent',
        method: 'post',
        data: data
    })
}
// 活畜屠宰详情-实时屠宰入场动态
export function butcherDynamic(data) {
	data.materialsType=materialsType
    return request({
        url: 'mesapp-service/showScreen/butcherDynamic',
        method: 'post',
        data: data
    })
}
// // 活畜屠宰详情-收购数量与结算金额统计
// export function trendOfButcher(data) {
// 	data.materialsType=materialsType
//     return request({
//         url: 'mesapp-service/showScreen/trendOfButcher',
//         method: 'post',
//         data: data
//     })
// }
// 活畜屠宰详情-生产排行TOP10
export function inFreezeTop10(data) {
	// data.materialsType=materialsType
    return request({
        url: 'mesapp-service/showScreen/inFreezeTop10',
        method: 'post',
        data: data
    })
}