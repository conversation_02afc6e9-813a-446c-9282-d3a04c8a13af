import request from '@/utils/request'

// 
// 质量溯源	-消费者查询产品排行TOP10
export function checkProductTop10(data) {
	data.materialsType=2
    return request({
        url: 'mesapp-service/showScreen/checkProductTop10',
        method: 'post',
        data: data
    })
}

// 质量溯源	-企业赋码统计
export function companyTraceCodeNum(data) {
    return request({
        url: 'mesapp-service/showScreen/companyTraceCodeNum',
        method: 'post',
        data: data
    })
}
// 质量溯源	-企业扫描次数统计
export function companyScanNum(data) {
    return request({
        url: 'mesapp-service/showScreen/companyScanNum',
        method: 'post',
        data: data
    })
}
// 质量溯源	-消费者扫码热力图
export function traceCodeScanHeatMap(data) {
    return request({
        url: 'mesapp-service/showScreen/traceCodeScanHeatMap',
        method: 'post',
        data: data
    })
}
// 质量溯源	-溯源码单项统计
export function traceCodeSingleCount(data) {
    return request({
        url: 'mesapp-service/showScreen/traceCodeSingleCount',
        method: 'post',
        data: data
    })
}
// 质量溯源	-消费者扫码实时动态
export function traceCodeScanRecord(data) {
    return request({
        url: 'mesapp-service/showScreen/traceCodeScanRecord',
        method: 'post',
        data: data
    })
}

// 质量溯源	-溯源搜索
export function searchTraceCodeScan(data) {
    return request({
        url: 'mesapp-service/showScreen/traceCodeInfo',
        method: 'post',
        data: data
    })
}


// 质量溯源	-合格证
export function byTraceCode(data) {
    return request({
        url: 'mesapp-service/mesCertificate/info/byTraceCode',
        method: 'post',
        data: data
    })
}
