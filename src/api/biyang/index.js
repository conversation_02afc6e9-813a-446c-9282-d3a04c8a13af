import request from '@/utils/request'
// 登录
export function login(data) {
    return request({
        url: '/mesapp-service/user/login',
		headers: {
		    isToken: false
		},
        method: 'post',
        data: data
    })
}

// 获取字典
export function getDict(data) {
    return request({
        url: 'xmbapp-service/dict/dictDataByType',
        method: 'post',
        data: data
    })
}


// 获取公共参数
export function getConfigByKey(data) {
	
	data={"configKey":"biyang.county.id"}
    return request({
        url: 'mesapp-service/livestockCount/selectConfigByKey',
        method: 'post',
        data: data
    })
}
