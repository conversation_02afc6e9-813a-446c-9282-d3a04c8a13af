import request from '@/utils/request'
let countyId='367795244253450253'
// 
// 首页	-活畜养殖
export function livestockCount(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectLivestockCountInfo',
        method: 'post',
        data: data
    })
}

// 首页	-存栏品类分布
export function livestockCate(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/getLivestockCountInfoByCategoryId',
        method: 'post',
        data: data
    })
}

// 首页	-出入栏统计
export function livestockCountOutAndIn(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectCountOutAndIn',
        method: 'post',
        data: data
    })
}

// 首页-所有用户-视频列表
export function hardwareList(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectAllEzvizList',
        method: 'post',
        data: data
    })
}
// 获取视频token
export function getPlayUrl(data) {
    return request({
        url: 'ffsapp-service/ezviz/getPlayUrl',
        method: 'post',
        data: data
    })
}


// 首页-企业列表
export function enterpriseList(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectEnterpriseListByCountyId',
        method: 'post',
        data: data
    })
}



// 首页-生产加工
export function singleCount(data) {
    return request({
        url: 'mesapp-service/showScreen/home/<USER>',
        method: 'post',
        data: data
    })
}
// 首页-牛只收购价格行情
export function trendOfPrice(data) {
    return request({
        url: 'mesapp-service/showScreen/trendOfPrice',
        method: 'post',
        data: data
    })
}
// 首页-活畜屠宰交易分布
export function trendOfButcher(data) {
    return request({
        url: 'mesapp-service/showScreen/trendOfButcher',
        method: 'post',
        data: data
    })
}
