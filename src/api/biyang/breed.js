

import request from '@/utils/request'
let countyId='367795244253450253'
// 活畜养殖-养殖户排行与查询
export function breedList(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/farmerList',
        method: 'post',
        data: data
    })
}

// 活畜养殖-养殖规模分布（暂时不用）
export function farmerList1(data) {
	
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectAllEnterpriseLivestocks',
        method: 'post',
        data: data
    })
}

// 活畜养殖-物联设备概况
export function deviceCount(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectDeviceCount',
        method: 'post',
        data: data
    })
}
// 活畜养殖-养殖区域分布
export function farmerListGroupByAddress(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/farmerListGroupByAddress',
        method: 'post',
        data: data
    })
}



// 养殖户详情-用户信息
export function userInfo(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectUserById',
        method: 'post',
        data: data
    })
}


// 养殖户详情-存栏活畜
export function userLivestockInfo(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectUserLivestockInfo',
        method: 'post',
        data: data
    })
}

// 养殖户详情-活畜列表
export function userLivestockList(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/list/livestock',
        method: 'post',
        data: data
    })
}
// 养殖户详情-牧场实时监控
export function weatherInfo(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/weather',
        method: 'post',
        data: data
    })
}

// 养殖户详情-养殖户视频监控信息
export function userDeviceList(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectEzvizListByUserId',
        method: 'post',
        data: data
    })
}




// 活畜档案-活畜档案
export function livestockDetail(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/listAllDetail',
        method: 'post',
        data: data
    })
}
// 活畜档案-活畜基本信息
export function livestockInfo(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/selectLivestock',
        method: 'post',
        data: data
    })
}
// 活畜档案-活畜运动量 体温  电子围栏  定位
export function livestockAssembly(data) {
	data.countyId=countyId
    return request({
        url: 'mesapp-service/livestockCount/assembly',
        method: 'post',
        data: data
    })
}