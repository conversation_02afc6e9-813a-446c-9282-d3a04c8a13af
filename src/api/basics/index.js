import request from '@/utils/request'
// 圈舍列表
export function livestockHouseList(data) {
    return request({
        url: 'mesapp-service/livestockHouse/page',
        method: 'post',
        data: data
    })
}
// 圈舍新增
export function livestockHouseAdd(data) {
    return request({
        url: 'mesapp-service/livestockHouse/add',
        method: 'post',
        data: data
    })
}
// 圈舍编辑
export function livestockHouseUpdate(data) {
    return request({
        url: 'mesapp-service/livestockHouse/update',
        method: 'post',
        data: data
    })
}
// 圈舍删除
export function livestockHouseDelete(data) {
    return request({
        url: 'mesapp-service/livestockHouse/delete',
        method: 'post',
        data: data
    })
}
// 生产线列表
export function productLineList(data) {
    return request({
        url: 'mesapp-service/productLine/page',
        method: 'post',
        data: data
    })
}
// 生产线新增
export function productLineAdd(data) {
    return request({
        url: 'mesapp-service/productLine/add',
        method: 'post',
        data: data
    })
}
// 生产线编辑
export function productLineUpdate(data) {
    return request({
        url: 'mesapp-service/productLine/update',
        method: 'post',
        data: data
    })
}
// 生产线删除
export function productLineDelete(data) {
    return request({
        url: 'mesapp-service/productLine/delete',
        method: 'post',
        data: data
    })
}
// 采购原料列表
export function materialsList(data) {
    return request({
        url: 'mesapp-service/materials/page',
        method: 'post',
        data: data
    })
}
// 采购原料新增
export function materialsAdd(data) {
    return request({
        url: 'mesapp-service/materials/add',
        method: 'post',
        data: data
    })
}
// 采购原料编辑
export function materialsUpdate(data) {
    return request({
        url: 'mesapp-service/materials/update',
        method: 'post',
        data: data
    })
}
// 采购原料删除
export function materialsDelete(data) {
    return request({
        url: 'mesapp-service/materials/delete',
        method: 'post',
        data: data
    })
}
// 原料等级列表
export function materialsLevelList(data) {
    return request({
        url: 'mesapp-service/materialsLevel/page',
        method: 'post',
        data: data
    })
}
// 原料等级新增
export function materialsLevelAdd(data) {
    return request({
        url: 'mesapp-service/materialsLevel/add',
        method: 'post',
        data: data
    })
}
// 原料等级编辑
export function materialsLevelUpdate(data) {
    return request({
        url: 'mesapp-service/materialsLevel/update',
        method: 'post',
        data: data
    })
}
// 原料等级删除
export function materialsLevelDelete(data) {
    return request({
        url: 'mesapp-service/materialsLevel/delete',
        method: 'post',
        data: data
    })
}
// 仓库列表
export function warehouseList(data) {
    return request({
        url: 'mesapp-service/warehouse/page',
        method: 'post',
        data: data
    })
}
// 仓库新增
export function warehouseAdd(data) {
    return request({
        url: 'mesapp-service/warehouse/add',
        method: 'post',
        data: data
    })
}
// 仓库编辑
export function warehouseUpdate(data) {
    return request({
        url: 'mesapp-service/warehouse/update',
        method: 'post',
        data: data
    })
}
// 仓库删除
export function warehouseDelete(data) {
    return request({
        url: 'mesapp-service/warehouse/delete',
        method: 'post',
        data: data
    })
}
// 计量单位列表
export function productUnitList(data) {
    return request({
        url: 'mesapp-service/productUnit/page',
        method: 'post',
        data: data
    })
}
// 计量单位新增
export function productUnitAdd(data) {
    return request({
        url: 'mesapp-service/productUnit/add',
        method: 'post',
        data: data
    })
}
// 计量单位编辑
export function productUnitUpdate(data) {
    return request({
        url: 'mesapp-service/productUnit/update',
        method: 'post',
        data: data
    })
}
// 计量单位删除
export function productUnitDelete(data) {
    return request({
        url: 'mesapp-service/productUnit/delete',
        method: 'post',
        data: data
    })
}
// 产品类型列表
export function productTypeList(data) {
    return request({
        url: 'mesapp-service/productType/page',
        method: 'post',
        data: data
    })
}
// 产品类型新增
export function productTypeAdd(data) {
    return request({
        url: 'mesapp-service/productType/add',
        method: 'post',
        data: data
    })
}
// 产品类型编辑
export function productTypeUpdate(data) {
    return request({
        url: 'mesapp-service/productType/update',
        method: 'post',
        data: data
    })
}
// 产品类型删除
export function productTypeDelete(data) {
    return request({
        url: 'mesapp-service/productType/delete',
        method: 'post',
        data: data
    })
}
// 产品列表
export function productList(data) {
    return request({
        url: 'mesapp-service/product/page',
        method: 'post',
        data: data
    })
}
// 产品新增
export function productAdd(data) {
    return request({
        url: 'mesapp-service/product/add',
        method: 'post',
        data: data
    })
}
// 产品编辑
export function productUpdate(data) {
    return request({
        url: 'mesapp-service/product/update',
        method: 'post',
        data: data
    })
}
// 产品删除
export function productDelete(data) {
    return request({
        url: 'mesapp-service/product/delete',
        method: 'post',
        data: data
    })
}
// 产品导出
export function exportData(data) {
    return request({
        url: 'mesapp-service/product/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
// 质检报告列表
export function qaReportList(data) {
    return request({
        url: 'mesapp-service/qaReport/page',
        method: 'post',
        data: data
    })
}
// 质检报告- 新增
export function qaReportAdd(data) {
    return request({
        url: 'mesapp-service/qaReport/add',
        method: 'post',
        data: data
    })
}
// 质检报告- 查看
export function qaReportInfo(data) {
    return request({
        url: 'mesapp-service/qaReport/info',
        method: 'post',
        data: data
    })
}
// 质检报告- 编辑
export function qaReportEdit(data) {
    return request({
        url: 'mesapp-service/qaReport/update',
        method: 'post',
        data: data
    })
}
// 质检报告- 删除
export function qaReportDelete(data) {
    return request({
        url: 'mesapp-service/qaReport/delete',
        method: 'post',
        data: data
    })
}
// 产品批量修改状态
export function batchUpdate(data) {
    return request({
        url: 'mesapp-service/product/batchUpdate',
        method: 'post',
        data: data
    })
}


// 合格证列表
export function certificateList(data) {
    return request({
        url: 'mesapp-service/mesCertificate/page',
        method: 'post',
        data: data
    })
}

// 合格证新增
export function certificateAdd(data) {
    return request({
        url: 'mesapp-service/mesCertificate/add',
        method: 'post',
        data: data
    })
}
// 合格证详情
export function certificateInfo(data) {
    return request({
        url: 'mesapp-service/mesCertificate/info',
        method: 'post',
        data: data
    })
}
// 合格证更新
export function certificateUpdate(data) {
    return request({
        url: 'mesapp-service/mesCertificate/update',
        method: 'post',
        data: data
    })
}
// 合格证删除
export function certificateDelete(data) {
    return request({
        url: 'mesapp-service/mesCertificate/delete',
        method: 'post',
        data: data
    })
}

// 设备安装地址-列表
export function equipmentPlaceList(data) {
    return request({
        url: 'mesapp-service/equipmentPlace/list',
        method: 'post',
        data: data
    })
}

// 设备安装地址-新增
export function equipmentPlaceAdd(data) {
    return request({
        url: 'mesapp-service/equipmentPlace/add',
        method: 'post',
        data: data
    })
}

// 设备列表
export function equipmentList(data) {
    return request({
        url: 'mesapp-service/equipment/page',
        method: 'post',
        data: data
    })
}

// 设备列表-新增
export function equipmentAdd(data) {
    return request({
        url: 'mesapp-service/equipment/add',
        method: 'post',
        data: data
    })
}
// 设备列表-编辑
export function equipmentUpdate(data) {
    return request({
        url: 'mesapp-service/equipment/update',
        method: 'post',
        data: data
    })
}
// 设备列表-详情
export function equipmentInfo(data) {
    return request({
        url: 'mesapp-service/equipment/info',
        method: 'post',
        data: data
    })
}
// 设备列表-删除
export function equipmentDelete(data) {
    return request({
        url: 'mesapp-service/equipment/delete',
        method: 'post',
        data: data
    })
}
// 萤石云列表
export function getEzvizList(data) {
    data.appId = '2105'
    return request({
        url: 'ffsapp-service/ezviz/list',
        method: 'post',
        data: data
    })
}


//获取地址播放视频
export function getPlayUrl(params) {
    return request({
        url: 'ffsapp-service/pasture/getPlayUrl',
        method: 'post',
        data: params
    })
}
// 获取视频播放地址
export function getVideoUrl(params) {
    return request({
        url: 'ffsapp-service/pasture/getLiveVideoUrl',
        method: 'post',
        data: params
    })
}

//获取萤石云的播放地址
export function ezvizUrl(params) {
    return request({
        url: 'ffsapp-service/ezviz/getPlayUrl',
        method: 'post',
        data: params
    })
}


// 导入数据 mesapp-service/excel/import/product，file 是 string (binary)
export function importData(data) {
    return request({
        url: 'mesapp-service/excel/import/product',
        method: 'post',
        data: data,
    })
}