import request from '@/utils/request'
// 查询用户列表
export function listUser(data) {
  return request({
    url: `/mesapp-service/enterprise/getBindUser`,
    method: 'post',
    data: data
  })
}
// 分割任务列表
export function carcassDivisionTaskList(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/page`,
      method: 'post',
      data: data
    })
}
//新增分割任务
export function carcassDivisionTaskAdd(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/add`,
      method: 'post',
      data: data
    })
}
//新增分割任务校验
export function carcassDivisionTaskParam(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/checkTaskParam`,
      method: 'post',
      data: data
    })
}
// 编辑分割任务
export function carcassDivisionTaskEdit(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/update`,
      method: 'post',
      data: data
    })
}
// 分割任务详情
export function carcassDivisionTaskInfo(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/info`,
      method: 'post',
      data: data
    })
}
// 修改分割任务状态
export function updateCloseTimeOrStatus(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/updateCloseTimeOrStatus`,
      method: 'post',
      data: data
    })
}
// 删除分割任务
export function carcassDivisionTaskDelete(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/delete`,
      method: 'post',
      data: data
    })
}
// 导出分割任务
export function carcassDivisionTaskExport(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/export`,
      method: 'post',
      data: data,
      responseType: 'blob'
    })
}
// 领料单记录列表
export function productMaterialsCollectList(data) {
    return request({
      url: `/mesapp-service/productMaterialsCollect/page`,
      method: 'post',
      data: data,
    })
}
// 领料单记录列表
export function materialsCollectInfoList(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/info/materialsCollect`,
      method: 'post',
      data: data,
    })
}
// 新增领料单
export function productMaterialsCollectAdd(data) {
    return request({
      url: `/mesapp-service/productMaterialsCollect/add`,
      method: 'post',
      data: data,
    })
}
// 领料单详情
export function productMaterialsCollectInfo(data) {
    return request({
      url: `/mesapp-service/productMaterialsCollect/info`,
      method: 'post',
      data: data,
    })
}
// 排酸列表
export function acidTaskList(data) {
    return request({
      url: `/mesapp-service/acidTask/page`,
      method: 'post',
      data: data,
    })
}
// 排酸详情
export function acidTaskInfo(data) {
    return request({
      url: `/mesapp-service/acidTask/info`,
      method: 'post',
      data: data,
    })
}
// 排酸详情
export function acidTaskExport(data) {
  return request({
    url: `/mesapp-service/acidTask/export`,
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
// 生产入库记录
export function inventoryStatistics(data) {
    return request({
      url: `/mesapp-service/carcassDivisionTask/inventoryStatistics`,
      method: 'post',
      data: data
    })
  }
  // 生产入库记录 -- 详情
  export function inventoryByProduct(data) {
      return request({
        url: `/mesapp-service/carcassDivisionTask/inventoryByProduct`,
        method: 'post',
        data: data
      })
  }
  // 生产任务  二次精加工   添加物料列表
  export function warehouseDetailList(data) {
      return request({
        url: `/mesapp-service/warehouse/detail/page`,
        method: 'post',
        data: data
      })
  }
  // 生产任务  二次精加工   添加物料列表
  export function collectRecordForType3(data) {
      return request({
        url: `/mesapp-service/carcassDivisionTask/collectRecordForType3`,
        method: 'post',
        data: data
      })
  }
  // 生产任务 - 领料记录 - 导出
  export function collectRecordExport(data) {
      return request({
        url: `/mesapp-service/carcassDivisionTask/collectRecord/export`,
        method: 'post',
        data: data,
        responseType: 'blob'
      })
  }
  // 自动创建任务规则
  export function autoConf(data) {
      return request({
        url: `/mesapp-service/carcassDivisionTask/autoConf`,
        method: 'post',
        data: data
      })
  }