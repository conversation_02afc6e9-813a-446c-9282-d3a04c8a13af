import request from '@/utils/request'
// 结算列表
export function settlementList(data) {
    return request({
        url: 'mesapp-service/settlement/page',
        method: 'post',
        data: data
    })
}
// 导出数据
export function settlementExport(data) {
    return request({
        url: 'mesapp-service/settlement/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 详情
export function settlementInfo(data) {
    return request({
        url: 'mesapp-service/settlement/info',
        method: 'post',
        data: data,
    })
}
//结算单明细
export function settlementDetail(data) {
    return request({
        url: 'mesapp-service/settlement/detail',
        method: 'post',
        data: data,
    })
}
//检斤单导出
export function settlementWeightDetailExport(data) {
    return request({
        url: 'mesapp-service/settlement/weight/detail/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

//补扣单明细
export function butcherFeeDetail(data) {
    return request({
        url: 'mesapp-service/settlement/butcherFee/detail',
        method: 'post',
        data: data,
    })
}
//检斤明细
export function weightDetail(data) {
    return request({
        url: 'mesapp-service/settlement/weight/detail',
        method: 'post',
        data: data,
    })
}
//银行卡列表
export function bankCardList(data) {
    return request({
        url: 'mesapp-service/supplier/selectSysUserByPhone',
        method: 'post',
        data: data,
    })
}
//维护收款账户
export function saveBankAccount(data) {
    return request({
        url: 'mesapp-service/settlement/saveSellerBankAccount',
        method: 'post',
        data: data,
    })
}
//补扣明细维护
export function butcherFeeAdd(data) {
    return request({
        url: 'mesapp-service/butcherFee/add',
        method: 'post',
        data: data,
    })
}
//合同管理列表
export function contractPage(data) {
    return request({
        url: 'mesapp-service/settlementContract/page',
        method: 'post',
        data: data,
    })
}
//合同详情
export function contractInfo(data) {
    return request({
        url: 'mesapp-service/settlementContract/info',
        method: 'post',
        data: data,
    })
}
//查看采购合同
export function selectBySettlementCode(data) {
    return request({
        url: 'mesapp-service/settlementContract/selectBySettlementCode',
        method: 'post',
        data: data,
    })
}
//发起合同签署
export function getContractUrl(data) {
    return request({
        url: 'mesapp-service/nybApi/getContractUrl',
        method: 'post',
        data: data,
    })
}
//去支付
export function getPayUrl(data) {
    return request({
        url: 'mesapp-service/nybApi/getPayUrl',
        method: 'post',
        data: data,
    })
}
// 线下支付
export function paySuccess(data) {
    return request({
        url: 'mesapp-service/settlement/paySuccess',
        method: 'post',
        data: data,
    })
}
// 撤回提交结算
export function cancelSettlement(data) {
    return request({
        url: 'mesapp-service/butcher/cancelSettlement',
        method: 'post',
        data: data,
    })
}
//下载合同
export function getContractPdfUrl(data) {
    return request({
        url: 'mesapp-service/nybApi/getContractPdfUrl',
        method: 'post',
        data: data,
    })
}