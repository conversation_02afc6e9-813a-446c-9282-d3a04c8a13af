import request from '@/utils/request'
// 登录方法
export function list(data) {
    return request({
        url: 'mesapp-service/supplier/page',
        method: 'post',
        data: data
    })
}
//添加
export function add(data) {
    return request({
        url: 'mesapp-service/supplier/add',
        method: 'post',
        data: data
    })
}
//查看详情
export function info(data) {
    return request({
        url: 'mesapp-service/supplier/info',
        method: 'post',
        data: data
    })
}
//编辑
export function edit(data) {
    return request({
        url: 'mesapp-service/supplier/update',
        method: 'post',
        data: data
    })
}
//删除
export function del(data) {
    return request({
        url: 'mesapp-service/supplier/delete',
        method: 'post',
        data: data
    })
}
//删除
export function selectSysUser(data) {
    return request({
        url: 'mesapp-service/supplier/selectSysUserByPhone',
        method: 'post',
        data: data
    })
}
//导出数据
export function supplierExport(data) {
    return request({
        url: 'mesapp-service/supplier/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}