import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
import '@/assets/css/flex.scss'
import '@/assets/css/style.scss'
import './public-path.js'
import { download } from '@/utils/request'
import { resetForm } from "@/utils/east-mind.js";
import plugins from './plugins' // plugins
// 字典标签组件
import DictTag from '@/components/DictTag'
// 字典数据组件
import DictData from '@/components/DictData'
// 分页组件
import Pagination from "@/components/Pagination";
// 富文本组件
import Editor from "@/components/Editor"
import draggableTable from "@/components/draggableTable.vue"
import { getDicts } from "@/api/system/dict/data";//字典
// 文件上传组件
import FileUpload from "@/components/FileUpload"
import Print from 'vue-print-nb'

import dataV from '@jiaminghi/data-view';
import * as echarts from 'echarts';
import 'echarts-gl';
import chinaJson from "./assets/map/china.json"



// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {

	

	Vue.use(dataV);
	//引入echart
	Vue.prototype.colors = ['#3AFFFB','#FFC700','#7239EA','#165DFF','#93dbff', '#04FF00', '#00FEFF','#FE982C','#5af3b8','#FFC700','#7239EA','#165DFF','#93dbff','#5af3b8']
	
	echarts.registerMap('china', chinaJson)
	Vue.prototype.$echarts = echarts


}

Vue.use(Element)
Vue.use(Print)
Vue.use(plugins)
Vue.config.productionTip = false
let instance = null;

// 全局挂载组件
Vue.component('Pagination', Pagination)
Vue.component('DictTag', DictTag)
Vue.component('Editor', Editor)
Vue.component('draggableTable', draggableTable)
Vue.component('FileUpload', FileUpload)

DictData.install()

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.resetForm = resetForm;
Vue.prototype.download = download;

function render(props = {}) {
    const { container } = props;
    instance = new Vue({
        router,
        store,
        render: (h) => h(App),
    }).$mount(container ? container.querySelector('#app') : '#app');
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {

	
    render();

}

export async function mount(props) {
    store.commit('SET_USER', props.user)
    render(props);
}
export async function bootstrap() {
    console.log('[vue] vue app bootstraped');
}

export async function unmount() {
    instance.$destroy();
    instance.$el.innerHTML = '';
    instance = null;
}
