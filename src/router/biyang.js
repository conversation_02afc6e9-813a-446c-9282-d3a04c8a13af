// 泌阳项目
export const biyang = [
    {
        path: '/main',
        name: 'main',
        component: () => import('@/views/biyang/index.vue'),
    	children: [
    		{
    		    path: '/index',
    		    name: 'home',
    		    component: () => import('@/views/biyang/home/<USER>')
    		},
    		{
    			path: '/slaughter',
    			name: 'slaughter',
    			meta: {
    				activeNav: 'slaughter',
    			},
    			component: () => import('@/views/biyang/slaughter/index.vue')
    		},
			{
				path: '/slaughter/detail',
				name: 'slaughterDetail',
				meta: {
					activeNav: 'slaughterDetail',
				},
				component: () => import('@/views/biyang/slaughter/detail.vue')
			},
    		 {
    			path: '/breed',
    			name: 'breed',
    			meta: {
    				activeNav: 'breed',
    			},
    			component: () => import('@/views/biyang/breed/index.vue')
    		},
			{
				path: '/breed/detail',
				name: 'breedDetail',
				meta: {
					activeNav: 'breedDetail',
				},
				component: () => import('@/views/biyang/breed/detail.vue')
			},
			{
				path: '/breed/archives',
				name: 'breedArchives',
				meta: {
					activeNav: 'breedArchives',
				},
				component: () => import('@/views/biyang/breed/archives.vue')
			},
			{
				path: '/source',
				name: 'source',
				meta: {
					activeNav: 'source',
				},
				component: () => import('@/views/biyang/source/index.vue')
			},
			
    
    	]
    	
    },
	{
	    path: '/login',
	    name: 'login',
	    component: () => import('@/views/biyang/login.vue')
	},
]