export const acquisition = [
    {
        path: '/supplier/supplierlist',
        component: () => import('@/views/supplier/index'),

    },
    {
        path: '/acquisition/price',
        component: () => import('@/views/acquisition/index'),

    },
    {
        path: '/acquisition/audit',
        component: () => import('@/views/acquisition/audit'),

    },
    //预约单管理
    {
        path: '/acquisition/appoint',
        component: () => import('@/views/acquisition/appoint'),

    },
    //入厂管理
    {
        path: '/acquisition/entranceList',
        component: () => import('@/views/acquisition/entrance'),

    },
    // 结算单管理
    {
        path: '/settleAccounts/bill',
        component: () => import('@/views/settlementManage/bill/index'),

    },
    // 合同管理
    {
        path: '/settleAccounts/agreement',
        component: () => import('@/views/settlementManage/agreement/index'),

    },
    //签署合同
    {
        path: '/settleAccounts/sign',
        component: () => import('@/views/settlementManage/agreement/sign'),

    },
    //入厂统计
    {
        path: '/statistics/entrance',
        component: () => import('@/views/statistics/entrance'),

    },
    //屠宰统计
    {
        path: '/statistics/butcher',
        component: () => import('@/views/statistics/butcher'),

    },
    //结算
    {
        path: '/statistics/settle',
        component: () => import('@/views/statistics/settle'),

    },
    // 排酸统计
    {
        path: '/statistics/acid',
        component: () => import('@/views/statistics/acid'),

    },
    // 生产统计
    {
        path: '/statistics/product',
        component: () => import('@/views/statistics/product'),

    },
    // 生产报表
    {
        path: '/statistics/productReport',
        component: () => import('@/views/statistics/productReport'),

    },
    
]