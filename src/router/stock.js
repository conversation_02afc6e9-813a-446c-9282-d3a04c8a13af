// 生产管理
export const stock = [
    // 库存列表
    {
        path: '/stock/stockList',
        component: () => import('@/views/stock/index'),
    },
    { // 入库记录
        path: '/stock/inWarehouseRecord',
        component: () => import('@/views/stock/inWarehouseRecord'),
    },
    { // 出库管理
        path: '/stock/outWarehouse',
        component: () => import('@/views/stock/outWarehouse'),
    },
    { // 盘点管理
        path: '/stock/inventoryList',
        component: () => import('@/views/stock/inventoryList'),
    },
    // 库存看板
    {
        path: '/stock/inventory',
        component: () => import('@/views/stock/board/index'),
    },

    // 为了配置权限
    // 库存列表
    {
        path: '/stock1/stockList1',
        component: () => import('@/views/stock/index'),
    },
    { // 入库记录
        path: '/stock1/inWarehouseRecord1',
        component: () => import('@/views/stock/inWarehouseRecord'),
    },
    { // 出库管理
        path: '/stock1/outWarehouse1',
        component: () => import('@/views/stock/outWarehouse'),
    },
    { // 盘点管理
        path: '/stock1/inventoryList1',
        component: () => import('@/views/stock/inventoryList'),
    },
]