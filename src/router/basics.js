// 基础管理
export const basics = [
    {
        name: 'basicsEnclosure',
        path: '/basics/enclosure',
        component: () => import('@/views/basics/enclosure/index'),
    },
    {
        name: 'basicsProduction',
        path: '/basics/production',
        component: () => import('@/views/basics/production/index'),
    },
    {
        name: 'basicsMaterial',
        path: '/basics/material',
        component: () => import('@/views/basics/material/index'),
    },
    {
        name: 'basicsMaterialsLevel',
        path: '/basics/materialsLevel',
        component: () => import('@/views/basics/materialsLevel/index'),
    },
    {
        name: 'basicsWarehouse',
        path: '/basics/warehouse',
        component: () => import('@/views/basics/warehouse/index'),
    },
    {
        name: 'basicsProduct',
        path: '/basics/product',
        component: () => import('@/views/basics/product/index'),
    },
    {
        name: 'basicsProductUnit',
        path: '/basics/product/unit',
        component: () => import('@/views/basics/product/unit'),
    },
    {
        name: 'basicsProductType',
        path: '/basics/productType',
        component: () => import('@/views/basics/product/productType'),
    },
    {
        name: 'basicsQualityTest',
        path: '/basics/qualityTest',
        component: () => import('@/views/basics/qualityTest/index'),
    },
	{
	    name: 'basicsCertificate',
	    path: '/basics/certificate',
	    component: () => import('@/views/basics/certificate/index'),
	},
	{
	    name: 'equipment',
	    path: '/basics/equipment',
	    component: () => import('@/views/basics/equipment/index'),
	}
]