import Vue from 'vue'
import VueRouter from 'vue-router'
import { getToken } from "@/utils/auth";

import { basics } from './basics.js'
import { acquisition } from './acquisition.js'
import { production } from './production.js'
import { stock } from './stock.js'
import { source } from './source.js'
import { soldManage } from './soldManage.js'

import { biyang } from './biyang.js'
Vue.use(VueRouter)

const routers = [
    ...acquisition,
    ...basics,
    ...production,
    ...stock,
    ...source,
	...biyang,
  ...soldManage
]
console.log(routers)
const router = new VueRouter({
    base: '/mesapp',
    mode: "history",
    routes: routers
})
const whiteList = ['/login', '/404']
router.beforeEach((to, from, next) => {
    if (getToken()) {
        if (to.path === '/login') {
          next({ path: '/index' })
        
        }else if(to.path === '/'){
        	next({ path: '/index' })
        } else {
          next()
        }
    }else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页

    }
  }
	
	
})
export default router