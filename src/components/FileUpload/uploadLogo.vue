<template>
    <div>
        <div class="uploader_box">
            <div class="uploader3" v-if="objectUrl">
                <img :src="objectUrl" alt="">
            </div>
            <div class="uploader2" v-if="objectUrl" @click="dialogVisible = true">
                <i class="el-icon-view"></i>
                <span>预览</span>
            </div>
            <el-upload
            class="avatar-uploader"
            :action="uploadFileUrl"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleUploadSuccess">
                <div class="uploader1" v-if="objectUrl">
                    <i class="el-icon-upload2"></i>
                    <span>重新上传</span>
                </div>
                <div class="uploader" v-else>
                    <i class="el-icon-upload2"></i>
                    <span>上传图片</span>
                </div>
            </el-upload>
        </div>

        <el-dialog
            :visible.sync="dialogVisible"
            :modal='false'
            width="30%">
                <img :src="objectUrl" class="revImg" alt="">
        </el-dialog>
    </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
    data() {
        return {
            objectUrl: '',
            dialogVisible: false
        }
    },
    props: {
        value: String,
        uploadFileUrl: {
            type: String,
            default:
            process.env.VUE_APP_BASE_API + `/mesapp-service/files/obs/fileUpload`, // 上传的图片服务器地址
        },
        headers: {
            type: Object,
                default: () => {
                return {
                    Authorization: getToken(),
                };
            },
        },
    },
    watch: {
        value() {
            this.objectUrl = this.value
        }
    },
    created() {
        this.objectUrl = this.value
    },
    methods: {
        // 上传成功回调
        handleUploadSuccess(res) {
            this.objectUrl = res.result[0].objectUrl
            this.$emit("input", this.objectUrl);
        },
    }
}
</script>

<style lang="scss" scoped>

.uploader_box{
    display: flex;
    align-items: center;
}
.uploader{
    width: 74px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    border: 1px solid #EEEEEE;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    color: #BBBBBB;
    line-height: 14px;
    i{
        font-size: 20px;
        color: #BBBBBB;
        margin-bottom: 9px;
    }
}
.uploader1{
    font-size: 12px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #5672FA;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    i{
        font-size: 20px;
        color: #5672FA;
        margin-right: 5px;
    }
}
.uploader2{
    font-size: 12px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #12AE63;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    i{
        font-size: 20px;
        color: #12AE63;
        margin-right: 5px;
    }
}
.uploader3{
    width: 74px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 2px 2px 2px 2px;
    margin-right: 23px;
    img{
        width: 74px;
        height: 74px;
    }
}
.revImg{
    width: 100%;
}
</style>