<template>
    <div class="upload-file">
      <el-upload
        :action="uploadFileUrl"
        list-type="picture-card"
        :on-preview="handlePictureCardPreview"
        :before-upload="handleBeforeUpload"
        :file-list="fileList"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleUploadSuccess"
        :headers="headers"
        :before-remove="beforeRemove"
        :on-remove="handleDelete"
        ref="upload"
        :disabled="disabled"
        :data="fromData"
        name="file">
        <div class="upload_">
          <i class="el-icon-plus"></i>
          点击上传图片
        </div>
        <div class="el-upload__tip" slot="tip" v-if="showTip">
          支持格式：
          <template v-if="fileType">
            <b style="color: #f56c6c">{{ fileType.join("/") }}</b>，
          </template>
          <template v-if="fileSize">
            单个文件不能超过
            <b style="color: #f56c6c">{{ fileSize }}MB</b>，
          </template>
          <template v-if="limit">
            最多可上传
            <b style="color: #f56c6c">{{ limit }}</b>
            个文件
          </template>
        </div>
      </el-upload>
      <el-dialog  width="30%" :modal="false" :visible.sync="dialogVisible">
        <img width="100%" style="height: 500px;" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { getToken } from "@/utils/auth";
  export default {
    name: "FileUpload",
    props: {
      // 值
      value: [String, Object, Array],
      // 数量限制
      limit: {
        type: Number,
        default: 5,
      },
      // 大小限制(MB)
      fileSize: {
        type: Number,
        default: 5,
      },
      // 文件类型, 例如['png', 'jpg', 'jpeg']
      fileType: {
        type: Array,
        default: () => ["doc", "xls", "ppt", "txt", "pdf","json"],
      },
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false,
      },
      // 是否显示删除按钮
      delBtn: {
        type: Boolean,
        default: true,
      },
      // 是否显示提示
      isShowTip: {
        type: Boolean,
        default: true,
      },
      // 是否显示文件列表
      showFileLsit: {
        type: Boolean,
        default: true,
      },
  
      uploadFileUrl: {
        type: String,
        default:
          process.env.VUE_APP_BASE_API + `/mesapp-service/files/obs/fileUpload`, // 上传的图片服务器地址
      },
      headers: {
        type: Object,
        default: () => {
          return {
            Authorization: getToken(),
          };
        },
      },
      uploadName: {
        type: String,
        default: "上传文件",
      },
      expandName: {
        type: String,
        default: "附件", //额外的文件名字
      },
  
      fromData: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      return {
        number: 0,
        uploadList: [],
        baseUrl: process.env.VUE_APP_BASE_API,
        fileList: [],
        fileName: [],
        dialogVisible: false,
        dialogImageUrl: ''
      };
    },
    watch: {
      value: {
        handler(val) {
          if (val) {
            let temp = 1;
            // 首先将值转为数组
            const list = Array.isArray(val) ? val : this.value.split(",");
            // 然后将数组转为对象数组
            this.fileList = list.map((item, index) => {
              if (typeof item === "string") {
                item = { name: this.fileName[index], url: item };
              }
              item.uid = item.uid || new Date().getTime() + temp++;
              return item;
            });
          } else {
            this.fileList = [];
            return [];
          }
        },
        deep: true,
        immediate: true,
      },
    },
    computed: {
      // 是否显示提示
      showTip() {
        return this.isShowTip && (this.fileType || this.fileSize);
      },
      getFileName() {
        return (path) => {
          return this.expandName + path.slice(path.lastIndexOf(".") + 1);
        };
      },
     
    },
    methods: {
      // 上传前校检格式和大小
      handleBeforeUpload(file) {
        // 校检文件类型
        if (this.fileType) {
          let fileExtension = "";
          if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
          }
          const isTypeOk = this.fileType.some((type) => {
            if (file.type.indexOf(type) > -1) return true;
            if (fileExtension && fileExtension.indexOf(type) > -1) return true;
            return false;
          });
          if (!isTypeOk) {
            this.$modal.msgError(
              `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
            );
            return false;
          }
        }
        // 校检文件大小
        if (this.fileSize) {
          const isLt = file.size / 1024 / 1024 < this.fileSize;
          if (!isLt) {
            this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
            return false;
          }
        }
        this.fileName.push(file.name);
        this.$modal.loading("正在上传文件，请稍候...");
        this.number++;
  
        return true;
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      // 文件个数超出
      handleExceed() {
        this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
      },
      // 上传失败
      handleUploadError(err) {
        this.$modal.msgError("上传文件失败，请重试");
        this.$modal.closeLoading();
      },
      // 上传成功回调
      handleUploadSuccess(res) {
        this.uploadList.push({
          name: res.result[0].bucketName,
          url: res.result[0].objectUrl,
        });
        if (this.uploadList.length === this.number) {
          this.fileList = this.fileList.concat(this.uploadList);
          this.uploadList = [];
          this.number = 0;
          this.$emit("input", this.listToString(this.fileList));
          this.$modal.closeLoading();
        }
      },

      beforeRemove() {
        this.$modal.loading("正在删除文件，请稍候...");
      },
      // 删除文件
      handleDelete(file, fileList) {
        this.fileList = fileList;
        // this.fileName.splice(index, 1);
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      },
      // 对象转成指定字符串分隔
      listToString(list, separator) {
        let strs = "";
        separator = separator || ",";
        for (let i in list) {
          strs += list[i].url + separator;
        }
        return strs != "" ? strs.substr(0, strs.length - 1) : "";
      },
    },
  };
  </script>
  
  <style scoped lang="scss">
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  
  .upload-file-list .el-upload-list__item {
    display: flex;
    border: 1px solid #e4e7ed;
    margin-bottom: 10px;
    position: relative;
    width: 100%;
    padding: 10px;
  }
  
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
    display: flex;
  }
  
  .el-link {
    margin-right: 20px;
  }
  .fc {
    display: flex;
    align-items: center;
  }
  
  .upload-box {
    border: 1px dashed #e4e7ed;
    border-radius: 10px;
    padding: 30px;
  }
  .upload-box:hover {
    border: 1px dashed #1890ff;
  }
  .el-icon-upload {
    font-size: 30px;
    margin-right: 10px;
    color: #1890ff;
  }
  .el-icon-txt {
    display: block;
    font-size: 16px;
  }
  // 文件列表
  .filelist {
    .filelistbx {
      display: flex;
      padding-left: 10px;
    }
    .ismbt {
      margin-bottom: 20px;
    }
  }
.revImg{
    width:300px;
}
.el-upload--picture-card{
  background: #EEEEEE;
}
.upload_{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  line-height: 1;
  font-size: 12px;
  font-family: PingFangSC-Regular-Regular, PingFangSC-Regular;
  font-weight: 400;
  color: rgba(0,0,0,0.4);
  line-height: 20px;
  width: 120px;
  height: 120px;
  background: #EEEEEE;
  border: 1px dashed #DCDCDC;
  i{
    font-size: 14px;
    color: rgba(0,0,0,0.9);
    margin-bottom: 15px;
  }
}
  </style>
  <style>
  .el-upload--picture-card{
    width: 120px;
    height: 120px;
    border: none;
  }
  .el-upload-list--picture-card .el-upload-list__item{
    width: 120px;
    height: 120px;
  }
  </style>
  