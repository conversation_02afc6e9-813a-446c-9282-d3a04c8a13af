<template>
  <div class="dialog_box">
    <el-dialog
        :visible.sync="dialogVisible"
        width="50%">
        <div slot="title">
            <h3>自定义显示列项</h3>
            <p>列项显示不得少于5项，最多支持自定义10个列项</p>
        </div>
        <div>
            <div class="tabel_header">
                <span>显示</span>
                <p>列名</p>
                <span>拖动调整顺序</span>
            </div>
            <draggable  :list="dataList">
                <transition-group>
                    <div class="list-group-item" v-for="(item, index) in list" :key="item.prop">
                        <span><el-checkbox v-model="item.check" @change="changeCheck(item, index)"></el-checkbox></span>
                        <p>{{ item.label }}</p>
                        <span><i class="el-icon-s-fold"></i></span>
                    </div>
                </transition-group>
            </draggable>
        </div>
        <div slot="footer" class="footer">
            <el-button type="text" class="text_btn" @click="reset">恢复默认</el-button>
            <span class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button size="mini" type="primary" @click="submit">确 定</el-button>
            </span>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from "vuedraggable";
import { menuAdd } from "@/api/system/customMenu/data.js";
export default {
    data() {
        return {
            dialogVisible: false,
            dataList: [],
        }
    },
    components:{
        draggable
    },
    props: {
        list: Array,
        id: String,
        defaultList: Array
    },
    watch: {
        list() {
            this.dataList = this.list
        }
    },
    created() {
        this.dataList = this.list
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
        },
        submit() {
            this.setMenu(this.dataList)
        },
        reset() {
            this.setMenu(this.defaultList)
        },
        setMenu(list) {
            const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
            menuAdd({
                appid: 2288,
                userId: userInfo.userId,
                businessType: this.id,
                businessData: JSON.stringify(list)
            }).then((res) => {
                this.$emit('changeTable', list)
                this.dialogVisible = false;
            })
        },
        changeCheck(item, index) {
            let count = 0;
            this.dataList.forEach(i => {
                if(i.check) {
                    count++
                }
            })
            if (count < 5) {
                item.check = true;
                this.dataList.splice(index, 1, item)
            }
            if (count > 10) {
                item.check = true;
                this.dataList.splice(index, 1, item)
            }
        }
    }
}
</script>

<style>
.tabel_header, .list-group-item{
    display: flex;
}
.list-group-item{
    padding:  0;
}
.tabel_header span, .list-group-item span{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
.tabel_header p, .list-group-item p{
    flex: 2;
    text-align: center;
}
.footer{
    display: flex;
    justify-content: space-between;
}
</style>