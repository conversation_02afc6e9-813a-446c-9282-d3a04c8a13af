// 颜色
$colors: (
  "primary": #db9e3f,
  "info-1": #4394e4,
  "info": #4b67af,
  "white": #ffffff,
  "light": #f9f9f9,
  "grey-1": #999999,
  "grey": #666666,
  "dark-1": #5f5f5f,
  "dark": #222222,
  "black-1": #171823,
  "black": #000000,
);

// 字体大小
$base-font-size: 0.2rem;
$font-sizes: (
  xxs: 0.1,
  //8px
    xs: 0.125,
  //10px
    sm: 0.2875,
  //12px
    md: 0.1625,
  //13px
    lg: 0.175,
  //14px
    xl: 0.2,
  //16px
    xxl: 0.225,
  //18px
    xxxl: 0.25 //20px,,,,
);

// 宽高
.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}

//flex
.d-flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
$flex-jc: (
  start: flex-start,
  end: flex-end,
  center: center,
  between: space-between,
  around: space-around,
  evenly: space-evenly,
);

$flex-ai: (
  start: flex-start,
  end: flex-end,
  center: center,
  stretch: stretch,
);

.flex-1 {
  flex: 1;
}

//.mt-1 => margin top
//spacing
$spacing-types: (
  m: margin,
  p: padding,
);
$spacing-directions: (
  t: top,
  r: right,
  b: bottom,
  l: left,
);
$spacing-base-size: 0.2rem;
$spacing-sizes: (
  0: 0,
  1: 0.25,
  2: 0.5,
  3: 1,
  4: 1.5,
  5: 3,
);

* {
  margin: 0;
  padding: 0;
  list-style-type: none;
  box-sizing: border-box;
  outline: none;
}
/* 定义了滚动条整体的样式*/
::-webkit-scrollbar {
  width: 1px;
  height: 1px;
}

/* 定义了滚动条滑块部分*/
::-webkit-scrollbar-track {
  background: transparent;
}

/* 定义了滚动条轨道部分*/
::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, .1);
}
html {
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.2em;
  margin: 0;
  padding: 0;
}

a {
  color: #343440;
  text-decoration: none;
}
.clearfix {
  &::after {
    content: "";
    display: table;
    height: 0;
    line-height: 0;
    visibility: hidden;
    clear: both;
  }
}

//浮动
.float-r {
  float: right;
}

//浮动
.float-l {
  float: left;
}

// 字体加粗
.fw-b {
  font-weight: bold;
}

//文章一行显示，多余省略号显示
.title-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bg-color-black {
  background-color: rgba(19, 25, 47, 0.6);
}

.bg-color-blue {
  background-color: #1a5cd7;
}

.colorBlack {
  color: #272727 !important;

  &:hover {
    color: #272727 !important;
  }
}

.colorGrass {
  color: #33cea0;

  &:hover {
    color: #33cea0 !important;
  }
}

.colorRed {
  color: #ff5722;

  &:hover {
    color: #ff5722 !important;
  }
}

.colorText {
  color: #d3d6dd !important;

  &:hover {
    color: #d3d6dd !important;
  }
}

.colorBlue {
  color: #257dff !important;

  &:hover {
    color: #257dff !important;
  }
}

//颜色
@each $colorkey, $color in $colors {
  .text-#{$colorkey} {
    color: $color;
  }

  .bg-#{$colorkey} {
    background-color: $color;
  }
}

//对齐
@each $var in (left, center, right) {
  .text-#{$var} {
    text-align: $var !important;
  }
}

//flex
@each $key, $value in $flex-jc {
  .jc-#{$key} {
    justify-content: $value !important;
  }
}

@each $key, $value in $flex-ai {
  .ai-#{$key} {
    align-items: $value !important;
  }
}

//字体
@each $fontkey, $fontvalue in $font-sizes {
  .fs-#{$fontkey} {
    font-size: $fontvalue * $base-font-size;
  }
}

//.mt-1 => margin top
//spacing

@each $typekey, $type in $spacing-types {
  //.m-1
  @each $sizekey, $size in $spacing-sizes {
    .#{$typekey}-#{$sizekey} {
      #{$type}: $size * $spacing-base-size;
    }
  }

  //.mx-1
  @each $sizekey, $size in $spacing-sizes {
    .#{$typekey}x-#{$sizekey} {
      #{$type}-left: $size * $spacing-base-size;
      #{$type}-right: $size * $spacing-base-size;
    }

    .#{$typekey}y-#{$sizekey} {
      #{$type}-top: $size * $spacing-base-size;
      #{$type}-bottom: $size * $spacing-base-size;
    }
  }

  //.mt-1
  @each $directionkey, $direction in $spacing-directions {
    @each $sizekey, $size in $spacing-sizes {
      .#{$typekey}#{$directionkey}-#{$sizekey} {
        #{$type}-#{$direction}: $size * $spacing-base-size;
      }
    }
  }

  .#{$typekey} {
    #{$type}: 0;
  }
}


.l_sreach{
    .el-input{
        font-size: 0.13rem;
        background: transparent;
        .el-input__inner {
          background: transparent;
          height: 0.38rem;
        }
        .el-input__icon{
          line-height: 0.38rem;
        }
    }
    .el-range-editor.is-active, .el-range-editor.is-active:hover, .el-select .el-input.is-focus .el-input__inner,
    .el-select .el-input__inner:focus, .el-input__inner:hover{
      border: 1px solid #259FA3;
    }
}


.el-dropdown {
  vertical-align: top;
}
.el-icon-arrow-down {
  font-size: 12px;
}

.user{
  .el-input__inner{
    height: 0.31rem;
  }
  .el-cascader, .el-input__icon{
    line-height: 0.31rem;
  }

  .el-input{
    font-size: 0.13rem;
    background: transparent;
    border-radius: 0;
    .el-input__inner {
      background: transparent;
      border-radius: 0;
    border: 1px solid rgba(41, 115, 253, 0.3800);
    }
  }

  .el-range-editor.is-active, .el-range-editor.is-active:hover, .el-select .el-input.is-focus .el-input__inner,
  .el-select .el-input__inner:focus, .el-input__inner:hover{
    border: 1px solid rgba(41, 115, 253, 0.3800);
  }
}

.anchorBL, .BMap_omCtrl{
  display: none !important;
}


.red{
	color: #FE2C7D;
}
.green{
	color: #00E0DB;
}
.purple{
	color: #5D2CFE;
}
.yellow{
	color: #FFD91C;
}
.blue{
	color: #2C76FE;
}

.red{
	color: #FF2200;
}
.orange{
	color: #FFA100;
}
.grass{
	color:#00FF34;
}


.bggreen{
	background: #00E0DB;
}
.bgpurple{
	background: #5D2CFE;
}
.bgyellow{
	background: #FFD91C;
}
.bgblue{
	background: #2C76FE;
}

.bgred{
	background: #FE2C7D;
}
.dorp{
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
}

.el-dropdown-menu{
	background-color: rgba(0, 0, 0, 0.8);
	border: 1px solid rgba(0,224,219,0.8);
	// border: 1px solid rgba(0,0,0,0.8);
}
.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled){
	
	color: #fff;
}
.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
	background-color: rgba(255, 255, 255, 0.06);
	color: #00E0DB;
}
.el-popper[x-placement^=bottom] .popper__arrow::after{
	border-bottom-color:#00E0DB;
}

.el-popper[x-placement^=bottom] .popper__arrow{
	border-bottom-color: rgba(0,224,219,0.8);
	top:-7px;
}

.theme{
	color:#00E0DB !important;
}

@for $i from 1 through 9 {
	  .f#{$i} {
		flex: $i  !important;
	  }
	}
	
.flex{
	display:flex;
}

.el-button{
	padding: 0;
	width: 0.63rem;
	height: 0.38rem;
	line-height: 0.38rem;
	background: #00E0DB;
	border-radius: 0.03rem 0.03rem 0.03rem 0.03rem;
	font-size: 0.18rem;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: #0D0D0D;
	
	background-color: #00E0DB !important;
	color: #032929 !important;
	border-color: #00E0DB !important;
}
.el-input__inner{
	height: 0.38rem;
	background: rgba(255,255,255,0.0588);
	border-radius: 0.03rem 0.03rem 0.03rem 0.03rem;
	opacity: 1;
	border: 0.01rem solid rgba(255,255,255,0.06);
	// width: 3rem;
	width: 100%;
	color: #fff;
}
.el-form-item{
	margin-bottom: 0;
}
.el-input__icon{
	line-height: 0.38rem;
}
input::placeholder {
	color: #606266;
}










// data-V样式

.dv-scroll-ranking-board .ranking-column {
	height: 0.15rem;
	background: #102c41;
	border: none;
	border-radius: 0.6rem 0.6rem 0.6rem 0.6rem !important;
	margin-top: 5px;
}

.dv-scroll-ranking-board .ranking-column .inside-column {
	width: 5.5rem;
	height: 0.15rem;
	background: linear-gradient(270deg, #FFE11D 0%, #00E0DB 100%) !important;
	border-radius: 0.6rem 0.6rem 0.6rem 0.6rem !important;
	margin-bottom: 0;
	overflow: hidden;
}

.dv-scroll-ranking-board .ranking-column .shine {
	background-color: transparent !important;
	animation: none !important;
}

.dv-scroll-ranking-board .ranking-info .rank {
	font-size: 0.18rem;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: #FFFFFF;
	width:0.5rem;
	margin-right: 0.1rem;
}

.dv-scroll-ranking-board .ranking-info .info-name {
	font-size: 0.18rem;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: #FFFFFF;
}

.ranking-value {
	font-size: 0.15rem;
	font-family: Source Han Sans CN-Medium, Source Han Sans CN;
	font-weight: 500;
	color: #FFE11D;
}
		





.form-inline {
	padding: 0.25rem 0;
}


.row-item:hover {

	background: rgba(255, 255, 255, 0.1) !important;
}


