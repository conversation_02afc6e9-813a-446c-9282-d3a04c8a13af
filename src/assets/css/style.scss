
@import './theme.scss';

.form_box{
    .el-card__body{
        padding-bottom: 0;
    }
    // .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
    //     // margin-bottom: 15px !important;
    //     // display: flex;
    // }
    // .el-form-item__content{
    //     flex: 1;
    // }
    .el-select{
        // width: 260px;
        height: 32px;
    }
    .el-form-item__label{
        color: $label_color;
    }
    .el-input__inner{
        // width: 260px;
        height: 32px;
        border: 1px solid $border_color;
    }
    .el-range-editor{
        // width: 260px !important;
        height: 32px;
    }
    .el-input.is-focus .el-input__inner,
    .el-input.is-active .el-input__inner, .el-input__inner:focus{
        border: 1px solid  $border_btn_color;
    }
    .el-select-dropdown__item.selected,
    .el-date-table td.today span,
    .el-date-table td.today span:hover{
        color: $text_color;
    }
    .el-button {
        height: 32px;
    }
    .el-button--primary{
        background: $back_color;
    }
}
.table_box{
    position: relative;
    .el-pagination.is-background .el-pager li:not(.disabled).active{
        background: $back_color;
    }
    .el-input.is-focus .el-input__inner,
    .el-input.is-active .el-input__inner, .el-input__inner:focus{
        border: 1px solid  $border_btn_color;
    }
    .el-select-dropdown__item.selected,
    .el-date-table td.today span,
    .el-date-table td.today span:hover{
        color: $text_color;
    }
    .pagination-box{
        position: absolute;
        bottom: 20px;
        right: 20px;
    }
}
.el-table th.el-table__cell{
    text-align: center;
}
.el-table th.el-table__cell.is-center{
    text-align: center;
}
.el-table th.el-table__cell.is-right{
    text-align: center;
}
.el-table__empty-block {
    border: 1px solid #E5E6EB;
    border-top: none;
}
.table_box.el-card{
    border-radius: 0 0 10px 10px !important;
}
.dialog_box{
    .el-dialog__body {
        padding-top: 0;
    }
    .el-dialog__header{
        padding: 15px;
        background: $dialog_back_color;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: dialog_text_color;
        .el-dialog__close{
            font-size: 22px;
            font-weight: 500;
            color: $label_color;
        }
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active{
        background: $back_color;
    }
    .el-input__inner {
        height: 30px;
    }
    .el-button--primary{
        background: $back_color;
    }
}
.drawer_box{
    .el-drawer__header{
        background: $dialog_back_color;
        margin: 0;
        padding: 18px 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: $label_color;
        .el-dialog__close{
            font-size: 22px;
            font-weight: 500;
            color: $label_color;
        }
    }
    .el-drawer__body{
        padding: 0 20px 0 56px;
    }
    .el-button--primary{
        background: $back_color;
    }
    .el-input__inner {
        height: 30px;
    }
    .el-input__icon{
        line-height: normal;
    }
    .el-date-editor .el-range-separator{
        line-height: 20px;
    }
}
.drawer_box_1{
    .el-drawer__body{
        padding: 0;
        .item_box{
            flex: 1;
            .el-form-item{
                margin: 0;
                .el-form-item__content{
                    margin: 0!important;
                }
            }
            .el-input__inner,
            .el-textarea__inner{
                min-width: 320px;
                border: none;
                resize: none;
                background: transparent;
                padding: 0;
            }
        }
        .el-descriptions-item__label,
        .el-descriptions-item__content{
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}
.el-select-dropdown__item.selected,
.el-date-table td.today span,
.el-date-table td.today span:hover{
    color: $text_color;
}
.point_icon{
    position: relative;
    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: $title_text_color;
    margin: 20px 0;
    &::before{
        content: '';
        position: absolute;
        top: 0;
        left: -22px;
        width: 4px;
        height: 20px;
        background: $theme_color;
        border-radius: 2px 2px 2px 2px;
    }
}
.el-descriptions .is-bordered .el-descriptions-item__label {
    border-color: 1px solid $border_color !important;
    white-space: nowrap !important;
}
.el-descriptions-item__label{
    white-space: nowrap !important;
}
.el-descriptions-item__label.is-bordered-label{
    background: $F5F7FA;
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 350;
    color: $label_color;
}
.el-descriptions-item__cell.el-descriptions-item__content{
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 350;
    color: $value_color
}
.exprot_btn,
.exprot_btn:focus,
.exprot_btn:hover {
    color: $text_color;
    border: 1px solid $border_btn_color;
    background: transparent;
    height: 32px;
}
.text_btn,
.text_btn:focus,
.text_btn:hover {
    color: $text_color;
}
.edit_text_btn,
.edit_text_btn:focus,
.edit_text_btn:hover {
    color: $edit_text_color;
}
.check_text_btn,
.check_text_btn:focus,
.check_text_btn:hover{
    color: $check_text_color;
}
.delete_text_btn,
.delete_text_btn:focus,
.delete_text_btn:hover {
    color: $delete_text_color;
}
.success_text_btn,
.success_text_btn:focus,
.success_text_btn:hover {
    color: $success_text_color;
}

.grey_text_btn,
.grey_text_btn:focus,
.grey_text_btn:hover {
    color: $grey;
}
.default_btn,
.default_btn:focus,
.default_btn:hover {
    background: rgba(86,114,250,0.1);
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    font-weight: 400;
    color: #5672FA;
    height: 28px;
    border: none;
}
.default_device_btn,
.default_device_btn:focus,
.default_device_btn:hover {
    background: transparent;
    font-size: 16px;
    font-weight: 400;
    color: #3D3D3D;
    height: 28px;
    border: none;
}
.add_btn,
.add_btn:focus,
.add_btn:hover {
    background: transparent;
    color: $add_btn;
    border: 1px solid $add_btn;
    height: 32px;
}
.edit_btn,
.edit_btn:focus,
.edit_btn:hover {
    background: transparent;
    color: $edit_text_color;
    border: 1px solid $edit_text_color;
    height: 32px;
}
.add_fill_btn,
.add_fill_btn:focus,
.add_fill_btn:hover {
    background: $add_btn !important;
    color: #fff;
    border: 1px solid $add_btn;
    height: 32px;
}
.edit_fill_btn,
.edit_fill_btn:focus,
.edit_fill_btn:hover {
    background: $edit_text_color !important;
    color: #fff;
    border: 1px solid $edit_text_color;
    height: 32px;
}
.delete_fill_btn,
.delete_fill_btn:focus,
.delete_fill_btn:hover {
    background: $delete_text_color !important;
    color: #fff;
    border: 1px solid $delete_text_color;
    height: 32px;
}
.grey_fill_btn,
.grey_fill_btn:focus,
.grey_fill_btn:hover {
    background: $grey_btn !important;
    color: $grey;
    border: 1px solid $grey_btn;
    height: 32px;
}
.check_fill_btn,
.check_fill_btn:focus,
.check_fill_btn:hover {
    background: $check_text_color !important;
    color: #fff;
    border: 1px solid $check_text_color;
    height: 32px;
}
.delete_btn,
.delete_btn:focus,
.delete_btn:hover {
    background: transparent;
    color: $delete_btn;
    border: 1px solid $delete_btn;
    height: 32px;
}
.orange{
    color: $orange;
    position: relative;
    &::before{
        position: absolute;
        left: -10px;
        top: 8px;
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $orange;
    }
}
.blue{
    color: $blue;
    position: relative;
    &::before{
        position: absolute;
        left: -10px;
        top: 8px;
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $blue;
    }
}
.green{
    color: $green;
    position: relative;
    &::before{
        position: absolute;
        left: -10px;
        top: 8px;
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $green;
    }
}
.red{
    color: $red;
    position: relative;
    &::before{
        position: absolute;
        left: -10px;
        top: 8px;
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $red;
    }
}
.grey{
    color: $grey;
    position: relative;
    &::before{
        position: absolute;
        left: -10px;
        top: 8px;
        content: '';
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $grey;
    }
}
.orange_full{
    color: $orange;
    position: relative;
    &::before{
        position: absolute;
        left: -25px;
        top: 3px;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $orange;
    }
}
.blue_full{
    color: $blue;
    position: relative;
    &::before{
        position: absolute;
        left: -25px;
        top: 3px;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $blue;
    }
}
.green_full{
    color: $green;
    position: relative;
    &::before{
        position: absolute;
        left: -25px;
        top: 3px;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $green;
    }
}
.red_full{
    color: $red;
    position: relative;
    &::before{
        position: absolute;
        left: -25px;
        top: 3px;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $red;
    }
}
.grey_full{
    color: $grey;
    position: relative;
    &::before{
        position: absolute;
        left: -25px;
        top: 3px;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $grey;
    }
}
.el-checkbox__input.is-checked,
.el-checkbox__input.is-indeterminate{
    .el-checkbox__inner{
        background-color: $checkbox_border_color;
        border-color: $checkbox_border_color;
    }
}
.el-checkbox__input.is-focus{
    .el-checkbox__inner{
        border-color: $checkbox_border_color;
    }
}
.el-checkbox__label,
.el-checkbox__input.is-checked+.el-checkbox__label,
.el-radio__input.is-checked+.el-radio__label{
    color: $checkbox_border_color;
}
.el-checkbox__inner:hover,
.el-radio__inner:hover{
    border-color: $checkbox_border_color;
}
.el-radio__input.is-checked .el-radio__inner{
    background-color: $checkbox_border_color;
    border-color: $checkbox_border_color;
}
.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th,
.el-table__footer .el-table__cell,
.el-table__footer-wrapper tbody td.el-table__cell,
.el-table__header-wrapper tbody td.el-table__cell{
    height: 50px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: $label_color;
}
.el-table .el-table__cell{
    padding: 0;
    height: 40px;
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 350;
    color: $value_color
}
.el-message-box {
    .el-message-box__header{
        .el-message-box__close:hover{
            color: $text_color
        }
    }
    .el-message-box__btns{
        .el-button--primary{
            background: $border_btn_color;
        }
    }
}
.el-tabs.el-tabs--top{
    .el-tabs__header{
        .is-active,
        .el-tabs__item:hover{
            color: $theme_color
        }
        .el-tabs__active-bar{
            background-color: $theme_color;
        }
    }
}
.status{
    width: 16px;
    height: 16px;
    border-radius: 100%;
    background: $theme_color;
    margin-right: 10px;
}
.el-dialog__wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    .el-dialog{
        margin: 0 !important;
    }
}
.el-radio-button__orig-radio:checked+.el-radio-button__inner{
    background-color: $checkbox_border_color;
    border-color: $checkbox_border_color;
}
.el-radio-button__inner:hover{
    color: $checkbox_border_color;
}
.border_theme {
    border: 1px solid $border_theme_color;
}
.el-step__head.is-finish {
    color: $theme_color;
    border-color: $theme_color;
}
.el-step__title.is-finish,
.el-step__title.is-process,
.el-step__title.is-wait{
    color: #1D2129;
}
.el-step__description.is-finish,
.el-step__description.is-process,
.el-step__description.is-wait{
    color: #86909C;
}
.footer_btn{
    width: 60%;
    height: 60px;
    background: #FFFFFF;
    box-shadow: 0px 0px 8px 0px #F2F3F5;
    border-radius: 0px 0px 0px 0px;
    position: fixed;
    bottom: 0;
    right: 0px;
    margin: 0;
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    z-index: 10;
}
.el-button--primary.is-plain{
    color: #fff;
}
.tabs_box{
    .el-tabs__nav-wrap::after{
        background-color: transparent;
    }
}

.el-descriptions .el-descriptions__header{
    margin: 0;
}
.source_main{
    .el-descriptions-row .el-descriptions-item__label {
        min-width: 200px;
        max-width: 200px;
    }
}
.app-container{
    padding: 10px;
}
.el-card{
    border: none !important;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
    border-color: #E5E6EB;
}
.el-table--border::after, .el-table--group::after, .el-table::before{
    background: transparent;
}
.mb10{
    margin-bottom: 10px;
}
.card_radius_t, .el-card{
    border-radius: 10px 10px 0 0;
}
.card_radius_b{
    border-radius: 0 0 10px 10px !important;
}
.mesaapp_main{
    .el-card{
        margin-top: 0px;
    }
}

.el-input-number .el-input__inner{
    text-align: left;
}
.init_dialog{
    .el-dialog__header{
        padding: 0;
    }
}
.el-table__fixed, .el-table__fixed-right{
    height: 100% !important; //设置高优先，以覆盖内联样式
} 
.el-table__fixed-right::before, .el-table__fixed::before{
    background-color: transparent;
}
.more-icon{
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    i{
        color: #4E5969;
        font-size: 18px;
    }
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px; /*滚动条粗细*/
  height: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
    background: #EBEBEB;
  border-radius: 0px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 0px;
  background: #D8D8D8;
}
.tabs-box{
    .el-tabs__nav-wrap::after{
        background-color: transparent !important;
    }
    .el-tabs__item{
        height: 24px;
        line-height: 24px;
    }
    .el-tabs__header{
        margin: 0;
    }
}