// 注释：可以退配合多个类使用，比如：fmc fc 可以实现y轴x轴元素水平居中 

.f {
    display: flex;
}

.f1 {
    flex: 1
}

.align-items-center {
    align-items: center;
}

.fc {
    @extend .f;
    @extend .align-items-center;
}

.fa {
    @extend .f;
    justify-content: space-around;
}

.fb {
    @extend .f;
    justify-content: space-between;
}

.fac {
    @extend .f;
    justify-content: space-around;
    @extend .align-items-center;
}

.fbc {
    @extend .f;
    justify-content: space-between;
    @extend .align-items-center;
}

.fcc {
    @extend .f;
    @extend .align-items-center;
    justify-content: center;
}

.fccm {
    @extend .f;
    @extend .align-items-center;
    justify-content: center;
    flex-direction: column;
}

.fm {
    @extend .f;
    flex-direction: column;
}

.fend {
    display: flex;
    justify-content: flex-end;
}

.fmc {
    @extend .fm;
    justify-content: center;
}

.fmb {
    @extend .fm;
    justify-content: space-between;
}

.fma {
    @extend .fm;
    justify-content: space-around;
}

.el-table__fixed-header-wrapper,
.el-table__header-wrapper {
    th {
                background-color: #f8f8f9 !important;
                }
}