

export const startEndDate = {

    data() {
        return {

            endDate:'',
			startData:''
        }
		
    },
	created(){
		this.endDate=this.formatDate(new Date())

		
		console.log(this.startData)
		console.log(this.endDate)
	},
	methods:{
		formatDate(val){
			return `${val.getFullYear()}-${(val.getMonth() + 1).toString().padStart(2, '0')}-${val.getDate().toString().padStart(2, '0')}`;
		},
		getStartData(type){
			const today = new Date();
			let startDate=''
			if(type==3){
				
				
				// 获取当前月份的开始日期
				startDate = new Date(today.getFullYear(), today.getMonth() - 2, 1);
				
				
	
				
			}else{
				startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - (type-1));
			}
			this.startData= this.formatDate(startDate)
		}
	}
}