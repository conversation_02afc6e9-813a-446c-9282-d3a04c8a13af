<template>
  <div class="task-detail" v-loading="loading">
    <div class="detail-section">
      <h3 class="section-title">基本信息</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">任务编号：</span>
            <span class="value">{{ taskInfo.offalTaskCode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">客户名称：</span>
            <span class="value">{{ taskInfo.customerName || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ taskInfo.createTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">更新时间：</span>
            <span class="value">{{ taskInfo.updateTime || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">创建人：</span>
            <span class="value">{{ taskInfo.createBy || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">更新人：</span>
            <span class="value">{{ taskInfo.updateBy || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 备注信息 -->
    <div class="detail-section">
      <h3 class="section-title">备注信息</h3>
      <div class="remark-content">
        {{ taskInfo.remark || '-' }}
      </div>
    </div>

    <div class="detail-section">
      <h3 class="section-title">关联称重记录</h3>
      <el-table
        :data="weightRecords"
        stripe
        style="width: 100%"
        border
        max-height="300"
      >
        <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
        <el-table-column prop="productSourceCode" align="center" label="记录ID" width="180"></el-table-column>
        <el-table-column prop="productName" align="center" label="产品名称"></el-table-column>
        <el-table-column prop="weightingType" align="center" label="称重类型"></el-table-column>
        <el-table-column prop="productSourceCode" align="center" label="任务ID" width="180"></el-table-column>
        <el-table-column prop="unitPrice" align="center" label="单价(元/kg)" width="120"></el-table-column>
        <el-table-column prop="grossWeight" align="center" label="毛重(kg)"></el-table-column>
        <el-table-column prop="tareWeight" align="center" label="皮重(kg)"></el-table-column>
        <el-table-column prop="netWeight" align="center" label="净重(kg)"></el-table-column>
        <el-table-column prop="weightAmount" align="center" label="总价格(元)" width="120"></el-table-column>
        <el-table-column prop="updateTime" align="center" label="称重时间" width="180"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { offalTaskInfo, offalWeightPage } from '@/api/soldManage.js'

export default {
  name: 'TaskDetail',
  props: {
    offalTaskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      taskInfo: {},
      weightRecords: []
    }
  },
  created() {
    this.getTaskInfo()
    this.getWeightRecords()
  },
  methods: {
    // 获取任务详情
    async getTaskInfo() {
      this.loading = true
      try {
        const response = await offalTaskInfo({
          offalTaskId: this.offalTaskId
        })
        if (response.success) {
          this.taskInfo = response.result || {}
        } else {
          this.$message.error(response.message || '获取任务详情失败')
        }
      } catch (error) {
        console.error('获取任务详情失败:', error)
        this.$message.error('获取任务详情失败')
      } finally {
        this.loading = false
      }
    },

    // 获取关联的称重记录
    async getWeightRecords() {
      try {
        const response = await offalWeightPage({
          pageNum: 1,
          pageSize: 100,
          offalTaskId: this.offalTaskId,
          offalTaskCode: null,
          productName: null,
          offalCustomerId: null,
          customerName: null,
          offalTaskDate: null,
          startTime: null,
          endTime: null
        })
        if (response.success) {
          this.weightRecords = response.result.list || []
        }
      } catch (error) {
        console.error('获取称重记录失败:', error)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.task-detail {
  .detail-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }

    .info-item {
      display: flex;
      margin-bottom: 12px;
      
      .label {
        color: #86909c;
        font-size: 14px;
        margin-left: 5px;
      }
      
      .value {
        flex: 1;
        color: #1d2129;
        font-size: 14px;
      }
    }

    .remark-content {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 6px;
      color: #4e5969;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
</style>
