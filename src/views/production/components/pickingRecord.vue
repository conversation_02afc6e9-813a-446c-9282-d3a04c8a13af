<template>
    <div class="main">
        <div id="print">
            <div class="fc">
                <span class="point_icon">领料记录</span>
            </div>
            <el-button size="mini" icon="el-icon-download" class="exprot_btn mb20" @click="exportList">导 出</el-button>
            <el-table :data="tableData" border>
                <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                <el-table-column prop="collectName" show-overflow-tooltip label="领料单名称"></el-table-column>
                <el-table-column prop="createTime" align="center" show-overflow-tooltip label="领料申请时间" sortable></el-table-column>
                <el-table-column label="原料类型" align="center">
                    <template slot-scope="scope">
                        <span>{{materialsTypeHash[scope.row.materialsType]}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="materialsName" align="center" label="原料名称"></el-table-column>
                <el-table-column label="是否已排酸" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.acidFlag ? '是' : '否'}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="materialsWeight" align="right" label="计划领用总重量（kg）" sortable></el-table-column>
                <el-table-column prop="materialsNum" align="right" label="计划领用数量" sortable></el-table-column>
                <el-table-column prop="collectWeight" align="right" label="实际领用重量（kg）" sortable></el-table-column>
                <el-table-column prop="collectNum" align="right" label="实际领用数量" sortable></el-table-column>
                <el-table-column label="领料单" align="center"  show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="!isfooter" size="mini" class="text_btn" @click="handleDetails(scope.row)" type="text" >{{scope.row.collectCode}}</span>
                        <span v-else size="mini" type="text" >{{scope.row.collectCode}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注"></el-table-column>
            </el-table>
            <Detail ref="detail" :dataInfo="dataItem"></Detail>
        </div>
    </div>
</template>

<script>
import { materialsCollectInfoList, collectRecordExport } from "@/api/production/index.js"
import Detail from './pickingRecordDetail.vue'
import { exportExcel } from '@/utils/east';
export default {
    data() {
        return {
            tableData: [],
            dataItem: {},
            materialsTypeHash: {
                1: '羊',
                2: '牛'
            }
        }
    },
    props: {
        dataInfo: Object,
        isfooter: String
    },
    watch: {
        dataInfo() {
            if (this.dataInfo.carcassDivisionTaskId) {
                this.getList()
            }
        },
    },
    created() {
        if (this.dataInfo.carcassDivisionTaskId) {
            this.getList()
        }
    },
    components: {
        Detail
    },
    methods: {
        getList() {
            materialsCollectInfoList({
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handleDetails(row) {
            this.dataItem = row;
            this.$refs.detail.showModel()
        },
        //导出数据
        exportList(){
            exportExcel(collectRecordExport, {
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
            },'领料记录')
        },
    }
}
</script>

<style lang="scss" scoped>
.text_btn{
    cursor: pointer;
}
</style>