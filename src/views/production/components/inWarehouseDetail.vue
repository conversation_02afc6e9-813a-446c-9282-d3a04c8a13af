<template>
    <div class="dialog_box">
        <el-dialog
            title="入库记录"
            :visible.sync="dialogVisible"
            width="900px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-row class="mes-title mb20">
                    <el-col :span="12">产品名称：{{dataInfo.productName}}</el-col>
                    <el-col :span="12">产品编码：{{dataInfo.productCode}}</el-col>
                </el-row>
                <div class="tabel">
                    <el-table :data="tableData" border :show-summary="true">
                        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                        <el-table-column label="入库单" align="center" show-overflow-tooltip >
                            <template slot-scope="scope">
                                <span v-if="isfooter">{{ scope.row.inventoryCode }}</span>
                                <el-button v-else size="mini" class="text_btn" @click="openDetail(scope.row)" type="text" >{{scope.row.inventoryCode}}</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="inventoryWeight" align="right" width='140' label="入库重量（kg）" sortable></el-table-column>
                        <el-table-column prop="inventoryNum" align="right" width='120' label="入库数量" sortable></el-table-column>
                        <el-table-column prop="createUserName" align="right" width='120' label="操作人"></el-table-column>
                        <el-table-column prop="createTime" align="center" show-overflow-tooltip label="入库时间" sortable></el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
		<el-drawer
			class="drawer_box"
			:visible.sync="modelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			:title='warehouseTypeHash[currentItem.inventoryType]'
			:wrapperClosable="false">
			<InWarehouseDetail ref="inWarehouseDetail" isShowInfo="1"  :dataInfo='currentItem'></InWarehouseDetail>
		</el-drawer>
    </div>
</template>

<script>
import { inventoryByProduct } from "@/api/production/index.js";
import InWarehouseDetail from '../../stock/components/inWarehouseDetail.vue'
export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            materialsTypeHash: {
                1: '羊',
                2: '牛'
            },
            total: 0,
            ruleForm: {
                pageNum: 1,
                pageSize: 10,
            },
            warehouseTypeHash: {
                11: '急冻入库',
                12: '成品入库',
                13: '结余入库',
                14: '盘盈入库'
            },
            modelShow: false,
            currentItem: {}
        }
    },
    props: {
        dataInfo: Object,
        carcassDivisionTaskId: String,
        isfooter: String
    },
    components: {
        InWarehouseDetail
    },
    created() {
        // this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.$nextTick(() => {
                this.getList();
            })
        },
        handleClose() {
            this.dialogVisible = false
        },
        getList() {
            inventoryByProduct({
                productId: this.dataInfo.productId,
                carcassDivisionTaskId: this.carcassDivisionTaskId
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result
                }
            })
        },
        openDetail(row) {
            this.currentItem= row;
            this.modelShow = true
        }
    }
}
</script>

<style scoped>
.mes-title{
    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    line-height: 24px;
    margin-top: 30px;
}
</style>