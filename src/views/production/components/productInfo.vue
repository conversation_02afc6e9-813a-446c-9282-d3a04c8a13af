<template>
    <div class="main">
        <!-- <el-card class="box-card"> -->
            <div id="print">
                <div class="fc">
                    <span class="point_icon">生产任务信息</span>
                </div>
                <el-descriptions :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='生产任务日期：'>{{ dataInfo.divisionTaskDate }}</el-descriptions-item>
                    <el-descriptions-item label='生产任务单号：'>{{ dataInfo.divisionTaskCode }}</el-descriptions-item>
                    <el-descriptions-item label='生产任务类型：'>{{ divisionTaskTypeHash[dataInfo.divisionTaskType] }}</el-descriptions-item>
                    <el-descriptions-item label='任务名称：'>{{ dataInfo.divisionTaskName }}</el-descriptions-item>
                    <el-descriptions-item label='生产类型：'>{{ handeltext(materialsTypeList,dataInfo.materialsType) }}</el-descriptions-item>
                    <el-descriptions-item label='交付日期：'>{{ dataInfo.deliveryDate }}</el-descriptions-item>
                    <el-descriptions-item label='负责人：'>{{ dataInfo.managerName }}</el-descriptions-item>
                    <el-descriptions-item label='所在部门：'>{{ dataInfo.managerDepartment }}</el-descriptions-item>
                    <el-descriptions-item label='创建人：'>{{ dataInfo.createUserName }}</el-descriptions-item>
                    <el-descriptions-item label='创建时间：'>{{ dataInfo.createTime }}</el-descriptions-item>
                    <el-descriptions-item label='最后修改人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                    <el-descriptions-item label='最后修改时间：'>{{ dataInfo.updateTime }}</el-descriptions-item>
                    <el-descriptions-item label='备注：'>{{ dataInfo.remark }}</el-descriptions-item>
                </el-descriptions>
                <!-- <el-descriptions-item label="生产人员">
                    <span>{{dataInfo.operaterIds}}</span>
                </el-descriptions-item> -->

                <div class="fc card-title">
                    <span class="point_icon">生成产品</span>
                </div>
                <el-table :data="tableData" :show-summary="true" border>
                    <el-table-column type="index" label="序号"></el-table-column>
                    <!-- <el-table-column prop="productCode" label="内部产品编码"></el-table-column>
                    <el-table-column prop="productName" label="产品名称"></el-table-column> -->

                    <el-table-column prop="name" label="内部产品编码" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.productCode }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="产品名称">
                        <template slot-scope="scope">
                            <span>{{ scope.row.productName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="weightingTypeName" width="80"  align="center" label="称重类型"></el-table-column>
                    <el-table-column prop="specification" width="170" align="center" label="规格单位"></el-table-column>
                    <!-- <el-table-column prop="deliveryDate" width="120" label="交付日期"></el-table-column> -->
                    <el-table-column prop="productNum" width="120" align="right" label="计划生产数" sortable :sum-text="'合计'"></el-table-column>
                    <el-table-column prop="name" label="总重量（kg)"  align="right" sortable>
                        <template slot-scope="scope">
                            <span>{{scope.row.productWeightName}}</span>
                        </template>
                    </el-table-column><el-table-column prop="name" label="备注">
                        <template slot-scope="scope">
                            <span>{{scope.row.note}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="fc card-title">
                    <span class="point_icon">消耗物料</span>
                </div>
                <div v-if="dataInfo.divisionTaskType == 1 || dataInfo.divisionTaskType == 2">
                    <el-button class="add_btn mb15" size="small" @click="showModel" v-if="dataInfo.divisionTaskStatus == 2 && !isfooter">创建领料单</el-button>
                    <el-table :data="tableMaterialsData" border :show-summary="true">
                        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                        <el-table-column prop="materialsTypeName" align="center" label="原料类型"></el-table-column>
                        <el-table-column prop="materialsName" label="原料名称"></el-table-column>
                        <el-table-column prop="materialsWeight"  align="right" sortable label="计划消耗总重量（kg) "></el-table-column>
                        <el-table-column prop="materialsNum" align="right" sortable label="计划消耗数量" :sum-text="'合计'"></el-table-column>
                        <el-table-column prop="collectWeight" align="right" sortable label="已领总重量（kg）"></el-table-column>
                        <el-table-column prop="collectNum" align="right" sortable label="已领重量（kg）"></el-table-column>
                        <el-table-column prop="name" label="备注">
                            <template slot-scope="scope">
                                <span>{{scope.row.note}}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div v-if="dataInfo.divisionTaskType == 3">
                    <el-button class="add_btn mb15" size="small" @click="showFormModel" v-if="dataInfo.divisionTaskStatus == 2 && !isfooter">创建出库单</el-button>
                    <el-table :data="tableCollectProductData" border :show-summary="true">
                        <el-table-column type="index" width="55" label="序号" align="center"></el-table-column>
                        <el-table-column prop="name" label="产品名称">
                            <template slot-scope="scope">
                                <span>{{ scope.row.productName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" show-overflow-tooltip label="内部产品编码" align="center">
                            <template slot-scope="scope">
                                <span>{{ scope.row.productCode }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="weightingTypeName" align="center" width="80" label="称重类型 "></el-table-column>
                        <el-table-column prop="specification" align="center" width="170" label="规格单位"></el-table-column>
                        <el-table-column prop="warehouseName" align="center" label="仓库"></el-table-column>
                        <el-table-column prop="productNum" align="right" sortable width="120" label="计划消耗数量"></el-table-column>
                        <el-table-column prop="inventoryNum" align="right" sortable width="120" label="已领数量"></el-table-column>
                        <el-table-column prop="inventoryWeight" align="right" sortable width="120" label="已领重量（kg）"></el-table-column>
                        <el-table-column prop="name" label="备注">
                            <template slot-scope="scope">
                                <span>{{scope.row.note}}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        <!-- </el-card> -->
        <CreatMaterials
            ref="creatMaterials"
            :materialsList='tableMaterialsData'
            :divisionTaskCode='dataInfo.divisionTaskCode'
            @confirmTask='confirmTask'></CreatMaterials>
		<el-drawer
			class="drawer_box"
			:visible.sync="formModelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			title='新建出库单'
			:wrapperClosable="false">
			<Form ref="form" :dataInfo='dataInfo' @close='closeFormModel'></Form>
		</el-drawer>
    </div>
</template>

<script>
import CreatMaterials from './creatMaterials.vue'
import Form from '../../stock/components/form.vue'
export default {
    data() {
        return {
            tableData: [],
            tableMaterialsData: [],
            tableCollectProductData: [],
            divisionTaskTypeHash: {
                1: '热线分割',
                2: '冷线分割',
                3: '二次精加工',
            },
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
                { text: "不限", value: '' },
            ],
            materialsTypeList: [
                { text: "羊", value: 1 },
                { text: "牛", value: 2 },
                { text: "不限", value: '' },
            ],
            formModelShow: false
        }
    },
    computed: {
        handeltext(){
            return (list,value)=>{
            let name=''
                list.forEach(item=>{
                    if(item.value==value){
                        name=item.text
                    }
                })
                return name
            }
        },
    },
    components: {
        CreatMaterials,
        Form
    },
    props: {
        dataInfo: Object,
        isfooter:String
    },
    watch: {
        dataInfo() {
            this.setProductData()
            if (this.dataInfo && this.dataInfo.materialsList){
                this.setMaterialsData(this.dataInfo.materialsList)
            }
            if (this.dataInfo && this.dataInfo.collectProductList){
                this.setCollectProductData(this.dataInfo.collectProductList)
            }
        }
    },
    created() {
        this.setProductData()
        if (this.dataInfo && this.dataInfo.materialsList){
            this.setMaterialsData(this.dataInfo.materialsList)
        }
        if (this.dataInfo && this.dataInfo.collectProductList){
            this.setCollectProductData(this.dataInfo.collectProductList)
        }
    },
    methods: {
        setProductData() {
            if (this.dataInfo && this.dataInfo.productList) {
                this.tableData = this.dataInfo.productList.map((item) => {
                    if (item.weightingType == 2) {
                        item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                        item.productWeightName = item.productWeight + '-' + item.productWeightEnd
                    } else {
                        item.unitWeightName = item.unitWeight + '/' + item.unitName;
                        item.productWeightName = item.productWeight
                    }
                    this.weightingTypeList.forEach((i) => {
                        if (i.value == item.weightingType) {
                            item.weightingTypeName = i.text;
                        }
                    });
                    return item
                })
            }
        },
        setMaterialsData(data) {
            if (data) {
                this.tableMaterialsData = data.map((item) => {
                    item.disable = true
                    this.materialsTypeList.forEach((i) => {
                        if (i.value == item.materialsType) {
                            item.materialsTypeName = i.text;
                        }
                    });
                    return item
                })
            }
        },
        setCollectProductData(data) {
            if (data) {
                this.tableCollectProductData = data.map((item) => {
                    item.disable = true
                    this.materialsTypeList.forEach((i) => {
                        if (i.value == item.materialsType) {
                            item.materialsTypeName = i.text;
                        }
                    });
                    this.weightingTypeList.forEach((i) => {
                        if (i.value == item.weightingType) {
                            item.weightingTypeName = i.text;
                        }
                    });
                    return item
                })
            }
        },
        showModel() {
            this.$refs.creatMaterials.showModel()
        },
        showFormModel() {
            this.formModelShow = true
        },
        confirmTask() {
            this.$emit('confirmTask')
        },
        closeFormModel() {
            this.formModelShow = false
            this.confirmTask()
        },
    }
}
</script>


<style lang="scss" scoped>
.main {
    padding-bottom: 20px;
}
</style>