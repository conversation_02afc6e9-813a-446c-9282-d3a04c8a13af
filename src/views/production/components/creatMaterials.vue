<template>
    <div class="dialog_box">
        <el-dialog
            title="创建领料单"
            :visible.sync="dialogVisible"
            width="1366px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content mt20">
                <el-form ref="ruleForm" label-width="100px" >
                    <el-form-item label="领料单名称：" prop="collectName">
                        <el-input v-model="collectName" style="width: 240px"></el-input>
                    </el-form-item>
                </el-form>
                <div style="display:flex; margin-bottom: 10px">
                    <MaterialsList @selectList='getSelectList' :defaultData='tableData'></MaterialsList>
                    <el-button style="margin-left: 10px" size="mini" class="delete_btn" @click="removeData">删除</el-button>
                </div>
                <el-table :data="tableData" border :show-summary="true"
                            @select-all="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.check"></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                    <el-table-column show-overflow-tooltip prop="materialsTypeName" width="80" align="center" label="原料类型"></el-table-column>
                    <el-table-column show-overflow-tooltip prop="materialsName" width="100" label="原料名称"></el-table-column>
                    <el-table-column prop="materialsWeight" label="计划消耗总重量（kg) ">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.materialsWeight" :disabled='scope.row.disable' type="number" maxlength="8" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="materialsNum" width="140" label="计划消耗数量" :sum-text="'合计'">
                        <template slot-scope="scope">
                            <el-input size="mudel" v-model="scope.row.materialsNum" :disabled='scope.row.disable' type="number" maxlength="8" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="collectWeight" width="140" label="已领总重量（kg）" align="right" :sum-text="'合计'"></el-table-column>
                    <el-table-column prop="collectNum" width="80" label="已领数量" align="right" :sum-text="'合计'"></el-table-column>
                    <el-table-column prop="collectWeight1" :sum-text="'合计'" width="180">
                        <template slot="header">
                            <span>
                                本次领用总重量（kg）
                                <span style="color:red">*</span>
                            </span>
                        </template>
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.collectWeight1" type="number" maxlength="8" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="collectNum1" label="本次领用数量" :sum-text="'合计'">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.collectNum1" type="number" maxlength="8" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="note" label="备注" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.note" type="text" placeholder="请输入备注"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import MaterialsList from './materialsList1.vue'
import { productMaterialsCollectAdd } from "@/api/production/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            collectName: '',
        }
    },
    props: {
        materialsList: Array,
        divisionTaskCode: String
    },
    created() {
        if (this.materialsList && this.materialsList.length > 0) {
            this.getSelectList(this.materialsList)
        }
    },
    components: {
        MaterialsList
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            if (this.materialsList && this.materialsList.length > 0) {
                this.getSelectList(JSON.parse(JSON.stringify(this.materialsList)))
            }
        },
        handleClose() {
            this.dialogVisible = false
            this.tableData = []
            this.collectName = ''
        },
        submitForm() {
            const divisionMaterialsList = []
            this.tableData.forEach(item => {
                if (item.carcassDivisionMaterialsId == null && item.materialsId != null) {
                    divisionMaterialsList.push({
                        materialsWeight: item.materialsWeight,
                        materialsNum: item.materialsNum,
                        collectWeight: item.collectWeight1,
                        collectNum: item.collectNum1,
                        materialsId: item.materialsId,
                        note: item.note
                    })
                } else {
                    divisionMaterialsList.push({
                        collectWeight: item.collectWeight1,
                        collectNum: item.collectNum1,
                        carcassDivisionMaterialsId: item.carcassDivisionMaterialsId,
                        note: item.note
                    })
                }
            })
            productMaterialsCollectAdd({
                materialsList: divisionMaterialsList,
                divisionTaskCode: this.divisionTaskCode,
                collectName: this.collectName
            }).then(res => {
                if (res.code == 200) {
                    this.$message.success('新增领料单成功')
                    this.handleClose()
                    this.$emit('confirmTask')
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        getSelectList(data) {
            data.forEach(item => {
                item.check = false
                item.collectWeight = item.collectWeight || 0
                item.collectNum = item.collectNum || 0
                this.tableData.push(item)
            })
        },
        changeNum(item, index) {
            item.productWeight = item.unitWeight * item.productNum
            this.tableData.splice(index, 1, item);
        },
        removeData() {
            const indexs = []
            const tableData = []
            this.tableData.forEach((item, index) => {
                if(item.check) {
                    indexs.push(index)
                } else {
                    tableData.push(item)
                }
            })

            if(indexs.length > 0) {
                this.$confirm('点击后列表删除已选内容', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData = tableData
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
            } else {
                this.$message.info('请先勾选列表中想要删除的数据')
            }
        }
    }
}
</script>

<style>

</style>