<template>
    <div class="dialog_box">
        <el-dialog
            title="查看领料单"
            :visible.sync="dialogVisible"
            width="1366px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-row class="title1 mb20">
                    <el-col :span="12">领料单名称：{{dataInfo.collectName}}</el-col>
                    <el-col :span="12">领料人员：{{dataInfo.updateUserName}}</el-col>
                </el-row>
                <div class="tabel">
                    <el-table :data="tableData" :show-summary="true" border>
                        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                        <el-table-column prop="materialsTypeName" align="center" width="80" label="原料类型"></el-table-column>
                        <el-table-column prop="materialsName" width="100" label="原料名称"></el-table-column>
                        <el-table-column prop="materialsWeight" align="right" label="计划领用总重量（kg）" sortable></el-table-column>
                        <el-table-column prop="materialsNum" align="right" label="计划领用数量" sortable></el-table-column>
                        <el-table-column prop="collectWeight" align="right" label="实际领用总重量（kg）" sortable></el-table-column>
                        <el-table-column prop="collectNum" align="right" label="实际领用数量" sortable></el-table-column>
                        <el-table-column prop="name" label="备注">
                            <template slot-scope="scope">
                                {{scope.row.remark}}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { productMaterialsCollectInfo } from "@/api/production/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            materialsTypeHash: {
                1: '羊',
                2: '牛'
            }
        }
    },
    props: {
        dataInfo: Object
    },
    created() {
        // this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.$nextTick(() => {
                this.getList();
            })
        },
        handleClose() {
            this.dialogVisible = false
        },
        getList() {
            productMaterialsCollectInfo({
                collectCode: this.dataInfo.collectCode,
                pageNum: 1,
                pageSize: 10000,
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result.materialsList.map((item) => {
                        item.materialsTypeName = this.materialsTypeHash[item.materialsType]
                        return item
                    })
                }
            })
        },
    }
}
</script>

<style scoped>
.title1{
    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    line-height: 24px;
    margin-top: 30px;
}
</style>