<template>
    <div class="main">
        <div style="display:flex; margin-bottom: 10px">
            <CollectProductList @selectList='getSelectList' :defaultData='tableData'></CollectProductList>
            <el-button style="margin-left: 10px" size="mini" class="delete_btn" @click="removeData">删除</el-button>
        </div>
        <el-table :data="tableData" :show-summary="true"
                    max-height="300" border
                    @select-all="handleSelectionChange">
            <el-table-column type="selection" min-width="80" align="center">
                <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.check"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column type="index" min-width="55" label="序号" align="center"></el-table-column>
                <el-table-column prop="name" show-overflow-tooltip label="产品名称">
                    <template slot-scope="scope">
                        <span>{{ scope.row.productName }}</span>
                    </template>
                </el-table-column>
            <el-table-column prop="name" label="内部产品编码" show-overflow-tooltip align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.productCode }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="weightingTypeName" show-overflow-tooltip min-width='80' align="center" label="称重类型"></el-table-column>
            <el-table-column prop="specification" show-overflow-tooltipalign="center" min-width='120' label="规格单位"></el-table-column>
            <el-table-column prop="warehouseName" show-overflow-tooltip align="center" label="仓库"></el-table-column>
            <el-table-column prop="inventoryNum" align="right" show-overflow-tooltip min-width='80' label="库存数量"></el-table-column>
            <!-- <el-table-column prop="materialsWeight" >
                <template slot="header">
                    <span>
                        计划消耗总重量（kg）
                        <span style="color:red">*</span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <el-input v-model="scope.row.materialsWeight" type="number" maxlength="8" placeholder="请输入"></el-input>
                </template>
            </el-table-column> -->
            <el-table-column prop="productNum" :sum-text="'合计'">
                <template slot="header">
                    <span>
                        计划消耗数量
                        <span style="color:red">*</span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <el-input-number step-strictly :controls="false" style="width: 100%" v-model="scope.row.productNum" type="number" :min="1" maxlength="8" placeholder="请输入"></el-input-number>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" placeholder="请输入内容"></el-input>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import CollectProductList from './collectProductList.vue'
export default {
    data() {
        return {
            tableData: [],
        }
    },
    props: {
        divisionTaskType: String,
        collectProductList: Array,
    },
    watch: {
        divisionTaskType() {
            if (this.tableData.length > 1) {
                this.getSelectList(this.tableData)
            }
        },
        collectProductList() {
            if (this.collectProductList && this.collectProductList.length > 0) {
                this.getSelectList(this.collectProductList)
            }
        }
    },
    created() {
        if (this.collectProductList && this.collectProductList.length > 0) {
            this.getSelectList(this.collectProductList)
        }
    },
    components: {
        CollectProductList
    },
    methods: {
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        getSelectList(data) {
            const data1 = JSON.parse(JSON.stringify(data))
            data1.forEach(item => {
                item.note = item.note
                item.check = false
                this.tableData.push(item)
            })
        },
        changeNum(item, index) {
            item.productWeight = item.unitWeight * item.productNum
            this.tableData.splice(index, 1, item);
        },
        removeData() {
            const indexs = []
            const tableData = []
            this.tableData.forEach((item, index) => {
                if(item.check) {
                    indexs.push(index)
                } else {
                    tableData.push(item)
                }
            })

            if(indexs.length > 0) {
                this.$confirm('点击后列表删除已选内容', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData = tableData
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
            } else {
                this.$message.info('请先勾选列表中想要删除的数据')
            }
        }
    }
}
</script>
