<template>
    <div class="main">
        <div id="print">
            <div class="fc">
                <span class="point_icon">领料记录</span>
            </div>
            <el-button size="mini" icon="el-icon-download" class="exprot_btn mb20" @click="exportList">导 出</el-button>
            <el-table :data="tableData" border>
                <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                <el-table-column prop="createTime" align="center" show-overflow-tooltip label="领料申请时间" sortable></el-table-column>
                <el-table-column prop="warehouseName" align="center" label="出库仓库"></el-table-column>
                <el-table-column prop="productName" label="产品名称"></el-table-column>
                <el-table-column prop="productCode" align="center" show-overflow-tooltip label="内部产品编码"></el-table-column>
                <el-table-column prop="weightingTypeName" align="center" width='80' label="称重类型"></el-table-column>
                <el-table-column prop="specification" align="center" width="140" label="规格单位"></el-table-column>
                <el-table-column prop="inventoryNum" align="right" width='80' label="出库数量" sortable></el-table-column>
                <el-table-column prop="inventoryWeight" align="right" width='120' label="出库重量（kg）" sortable></el-table-column>
                <el-table-column label="出库单" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="!isfooter" size="mini" class="text_btn" @click="handleDetails(scope.row)" type="text" >{{scope.row.inventoryCode}}</span>
                        <span v-else size="mini" type="text" >{{scope.row.inventoryCode}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="inventoryStatusName" align="center" width='80' label="单据状态"></el-table-column>
                <el-table-column prop="updateTime" align="center" show-overflow-tooltip label="出库完成时间" sortable></el-table-column>
            </el-table>
            <el-drawer
                class="drawer_box"
                :visible.sync="modelShow" 
                :show-close="true" 
                :append-to-body="true" 
                :destroy-on-close="true"
                size="80%"
                :title='warehouseTypeHash[currentItem.inventoryType]'
                :wrapperClosable="false">
                <OutWarehouseDetail ref="outWarehouseDetail" isShowInfo="1" :dataInfo='currentItem'></OutWarehouseDetail>
            </el-drawer>
        </div>
    </div>
</template>

<script>
import { collectRecordForType3, collectRecordExport } from "@/api/production/index.js"
import OutWarehouseDetail from '../../stock/components/outWarehouseDetail.vue'
import { exportExcel } from '@/utils/east';
export default {
    data() {
        return {
            tableData: [],
            currentItem: {},
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
                { text: "不限", value: '' },
            ],
            warehouseTypeHash: {
                '21': '领料出库',
                '22': '销售出库',
                '23': '盘亏出库',
                '24': '急冻出库',
            },
            modelShow: false
        }
    },
    props: {
        dataInfo: Object,
        isfooter: String
    },
    watch: {
        dataInfo() {
            if (this.dataInfo.carcassDivisionTaskId) {
                this.getList()
            }
        },
    },
    created() {
        if (this.dataInfo.carcassDivisionTaskId) {
            this.getList()
        }
    },
    components: {
        OutWarehouseDetail
    },
    methods: {
        getList() {
            collectRecordForType3({
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId,
                pageNum: 1,
                pageSize: 10000,
            }).then(res => {
                if (res.code == 200) {
                    const statusHash = {
                        1: '已完成',
                        2: '待出库',
                        3: '进行中'
                    }
                    this.tableData = res.result.map(item => {
                        this.weightingTypeList.forEach((i) => {
                            if (i.value == item.weightingType) {
                                item.weightingTypeName = i.text;
                            }
                        });
                        item.inventoryStatusName = statusHash[item.inventoryStatus]
                        return item
                    })
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handleDetails(row) {
            this.currentItem = row;
            this.modelShow = true
        },
        //导出数据
        exportList(){
            exportExcel(collectRecordExport, {
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
            },'领料记录')
        },
    }
}
</script>

<style lang="scss" scoped>
.text_btn{
    cursor: pointer;
}
</style>