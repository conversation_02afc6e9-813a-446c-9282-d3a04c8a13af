<template>
    <div class="dialog_box">
        <el-button class="add_btn" size="small" @click="showModel">添加</el-button>
        <el-dialog
            title="选择物料"
            :visible.sync="dialogVisible"
            width="900px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-form ref="ruleForm" label-width="80px" class="mt20">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="原料名称" prop="materialsName">
                                <el-input size="mini" v-model="ruleForm.materialsName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="原料类型" prop="materialsType">
                                <el-select size="mini" v-model="ruleForm.materialsType" style="width: 100%">
                                    <el-option v-for="(item, index) in materialsTypeList" :key='index' :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" class="btn">
                            <el-form-item>
                                <el-button size="mini" type="primary" @click="handleQuery" >搜索</el-button
                                >
                                <el-button size="mini" @click="resetQuery"
                                >重置</el-button
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div class="tabel">
                    <el-table :data="tableData" border @select-all="handleSelectionChange" max-height="380">
                        <el-table-column type="selection" align="center" width="55">
                            <template slot-scope="scope">
                                <el-checkbox :disabled='scope.row.disalbel' v-model="scope.row.check"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column label="序号" width="55" align="center">
                            <template slot-scope="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="materialsTypeName" align="center" label="原料类型"></el-table-column>
                        <el-table-column prop="materialsName" align="center" label="原料名称"></el-table-column>
                    </el-table>
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="ruleForm.pageNum"
                        :limit.sync="ruleForm.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button size="mini" type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { materialsList } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            sreachValue: '',
            prodTypeList: [],
            materialsTypeList: [
                { text: "羊", value: 1 },
                { text: "牛", value: 2 },
                { text: "不限", value: '' },
            ],
            ruleForm: {
                pageNum: 1,
                pageSize: 10,
                materialsName: '', //产品编码
                materialsType: '', //称重类型（1定重，2抄码）
            },
            tableData: [],
            total: 0
        }
    },
    props: {
        defaultData: Array
    },
    created() {
        this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.getList();
        },
        handleClose() {
            this.dialogVisible = false
        },
        //重置
        resetQuery() {
            this.ruleForm = {
                pageNum: 1,
                pageSize: 10,
                materialsName: '', //产品编码
                materialsType: '',
            }
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.ruleForm.pageNum = 1;
            this.getList();
        },
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                if (!item.disalbel) {
                    item.check = data.length > 0 ? true : false;
                }
                return item
            })
        },
        getList() {
            materialsList({
                ...this.ruleForm
            }).then(res => {
                if (res.code == 200) {
                    if (this.defaultData && this.defaultData.length > 0) {
                        this.tableData = res.result.list.map((item) => {
                            item.disalbel = false
                            item.check =  false
                            item.note = '';
                            this.materialsTypeList.forEach((i) => {
                                if (i.value == item.materialsType) {
                                    item.materialsTypeName = i.text;
                                }
                            });
                            this.defaultData.forEach(i => {
                                if (i.materialsId == item.materialsId) {
                                    item.disalbel = true 
                                }
                            });
                            return item
                        })
                    }  else {
                        this.tableData = res.result.list.map((item) => {
                            item.check =  false
                            item.disalbel = false
                            item.note = ''
                            this.materialsTypeList.forEach((i) => {
                                if (i.value == item.materialsType) {
                                    item.materialsTypeName = i.text;
                                }
                            });
                            return item
                        });
                    }
                    this.total = Number(res.result.total);
                }
            })
        },
        submitForm() {
            const selectList = [];
            this.tableData.forEach(item => {
                if (item.check) {
                    selectList.push(item)
                }
            })
            this.$emit('selectList', selectList)
            this.handleClose()
        }
    }
}
</script>

<style scoped>
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.btn{
    display: flex;
    padding-left: 10px;
}
</style>