<template>
    <div style="padding-bottom: 80px;">
        <div style="border-bottom: 10px solid #F5F7FA;">
            <el-descriptions class="" :title="dataInfo.divisionTaskName" :column="3" size="medium" border>
                <template slot="extra">
                    <!-- <span>{{divisionTaskStatusHash[dataInfo.divisionTaskStatus]}}</span> -->
                    <img class="img" v-if="dataInfo.divisionTaskStatus == -1" src="../../../assets/images/caogao_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.divisionTaskStatus == 2" src="../../../assets/images/jinxingzhong_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.divisionTaskStatus == 1" src="../../../assets/images/yiwancheng_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.divisionTaskStatus == 0" src="../../../assets/images/yiguanbi_icon.png" alt="" srcset="">
                </template>
            </el-descriptions>
            <div v-if="dataInfo.closeAfterHours > 0" style="font-size: 14px" class="mb20">该任务已设置定时关闭，自动关闭时间: {{dataInfo.closeTime}}</div>
        </div>

        <!-- <el-row :gutter="20" class="fcc">
            <el-col :span="20">
                <el-descriptions class="mt20" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='创建人：'>{{ dataInfo.createUserName }}</el-descriptions-item>
                    <el-descriptions-item label='创建时间：'>{{ dataInfo.createTime }}</el-descriptions-item>
                    <el-descriptions-item label='最后修改人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                    <el-descriptions-item label='最后修改时间：'>{{ dataInfo.updateTime }}</el-descriptions-item>
                </el-descriptions>
            </el-col>
            <el-col :span="4" class="fcc">
                <div class="status"></div>
                
            </el-col>
        </el-row> -->
        <el-tabs v-model="activeName" class="tabs_box mt20">
            <el-tab-pane label="生产信息" name="1"></el-tab-pane>
            <el-tab-pane label="领料记录" name="2"></el-tab-pane>
            <el-tab-pane label="生产入库记录" name="3"></el-tab-pane>
            <!-- <el-tab-pane label="结余入库记录" name="4">结余入库记录</el-tab-pane>
            <el-tab-pane label="操作记录" name="5">操作记录</el-tab-pane> -->
        </el-tabs>
        <ProductInfo v-if="activeName == 1" :dataInfo='dataInfo' :isfooter="isfooter" @confirmTask='getInfo'></ProductInfo>
        <PickingRecord v-if="activeName == 2 && (dataInfo.divisionTaskType == 1 || dataInfo.divisionTaskType == 2)" :isfooter="isfooter" :dataInfo='dataInfo'></PickingRecord>
        <PickingRecord1 v-if="activeName == 2 && dataInfo.divisionTaskType == 3" :isfooter="isfooter" :dataInfo='dataInfo'></PickingRecord1>
        <inWarehouse v-if="activeName == 3" :isfooter="isfooter" :dataInfo='dataInfo'></inWarehouse>
        <Confirm ref="confim" @submit="submit"></Confirm>
        <FixedTime ref="fixedTime" :dataInfo='dataInfo' @fixesTime='getInfo'></FixedTime>
        <div class="footer_btn fcc" v-if="!isfooter">
            <el-button type="primary" size="small" v-if="dataInfo.divisionTaskStatus == -1" @click="submitTask">提交任务</el-button>
            <el-button type="primary" size="small" v-if="dataInfo.divisionTaskStatus == 2" @click="okTask">完成任务</el-button>
            <el-button type="primary" size="small" v-if="dataInfo.divisionTaskStatus == 2" @click="handleOpen">定时任务</el-button>
            <el-button class="edit_fill_btn" size="small" v-if="dataInfo.divisionTaskStatus == -1" @click="edit">编辑</el-button>
            <el-button class="delete_fill_btn" size="small" v-if="dataInfo.divisionTaskStatus == -1" @click="deleteEnclosure">删除</el-button>
        </div>
    </div>
</template>

<script>
import {
    carcassDivisionTaskInfo,
    updateCloseTimeOrStatus,
    carcassDivisionTaskDelete,
} from "@/api/production/index.js";
import ProductInfo from './productInfo.vue'
import Confirm from './confirm.vue'
import FixedTime from './fixedtimes.vue'
import PickingRecord from './pickingRecord.vue'
import PickingRecord1 from './pickingRecord1.vue'
import inWarehouse from './inWarehouse.vue'
export default {
    data() {
        return {
            activeName: '1',
            dataInfo: {},
            divisionTaskStatusHash: {
                '-1': '暂存',
                '0': '已取消',
                '1': '已完成',
                '2': '进行中',
            }
        }
    },
    props: {
        taskId: String,
        isfooter: String
    },
    components: {
        ProductInfo,
        Confirm,
        FixedTime,
        PickingRecord,
        inWarehouse,
        PickingRecord1
    },
    watch: {
        taskId() {
            if (this.taskId) {
                this.getInfo()
            }
        }
    }, 
    created() {
        if (this.taskId) {
            this.getInfo()
        }
    },
    methods: {
        getInfo() {
            carcassDivisionTaskInfo({
                carcassDivisionTaskId: this.taskId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                }
            })
        },
        submitTask(row) {
            this.$refs.confim.showModel()
        },
        submit(data) {
            updateCloseTimeOrStatus({
                ...data,
                collectFlag: data.collectFlag ? 1 : 0,
                divisionTaskStatus: 2,
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
            }).then(res => {
                if (res.code == 200) {
                    this.$message.success('任务已提交')
                    this.$emit('closeCutApart')
                }
            })
        },
        okTask() {
            this.$confirm('是否确定完成生产任务？确定后不可再进行任何操作', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                updateCloseTimeOrStatus({
                    divisionTaskStatus: 1,
                    carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('任务已完成')
                        this.$emit('closeCutApart')
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });          
            });
        },
        deleteEnclosure() {
            this.$confirm('此操作将永久删除该文件任务, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                carcassDivisionTaskDelete({
                    carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('任务已完成')
                        this.$emit('closeCutApart')
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        edit() {
            this.$emit('edit', this.dataInfo)
        },
        handleOpen() {
            this.$refs.fixedTime.handleOpen()
        }
    }
}
</script>

<style lang="scss" scoped>
.title{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.title div {
    font-size: 18px;
}
.el-row {
    font-size: 14px !important;
    .el-col-7{
        margin-top: 20px;
    }
}
.card-title {
  margin-bottom: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.model {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  &-text {
    color: rgb(102, 102, 102);
  }
  .header {
    &-title {
      font-size: 18px;
    }
  }
}
.footer_btn{
    width: 80%;
}
.img{
    width: 79px;
    height: 77px;
}
</style>