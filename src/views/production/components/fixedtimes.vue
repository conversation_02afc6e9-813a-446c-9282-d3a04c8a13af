<template>
    <div class="dialog_box">
        <el-dialog
            title="操作确认"
            :visible.sync="dialogVisible"
            width="30%"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <h3>任务创建后的固定时间内，自动变更状态为已完成</h3>
                <el-radio-group v-model="closeAfterHours">
                    <p style='margin-bottom: 20px'><el-radio label="24">24小时</el-radio></p>
                    <p style='margin-bottom: 20px'><el-radio label="36">36小时</el-radio></p>
                    <p style='margin-bottom: 20px'><el-radio label="48">48小时</el-radio></p>
                    <p style='margin-bottom: 20px'><el-radio label="-9999">
                        <el-input v-model="input1" :disabled='closeAfterHours != -9999' :min="1" maxlength="3" type="number" placeholder="输入内容"></el-input> 小时
                    </el-radio></p>
                    <p style='margin-bottom: 20px'><el-radio label="-1000">不设置定时完成</el-radio></p>
                </el-radio-group>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size='mini' @click="handleClose">取 消</el-button>
                <el-button size='mini' type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { updateCloseTimeOrStatus } from "@/api/production/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            closeAfterHours: '-1000',
            input1: ''
        }
    },
    props: {
        dataInfo: Object
    },
    methods: {
        handleClose() {
            this.dialogVisible = false
            this.closeAfterHours = '-1000'
            this.input1 = ''
        },
        handleOpen() {
            this.dialogVisible = true
            if (!this.dataInfo) {
                return
            }
            if (!this.dataInfo.closeAfterHours || this.dataInfo.closeAfterHours == 0) {
                this.closeAfterHours = '-1000'
                this.input1 = ''
            } else if ([24, 36, 48].indexOf(this.dataInfo.closeAfterHours) == -1) {
                this.closeAfterHours = '-9999'
                this.input1 = this.dataInfo.closeAfterHours
            } else {
                this.closeAfterHours = this.dataInfo.closeAfterHours || '-1000'
                this.input1 = ''
            }
        },
        submitForm() {
            if (this.closeAfterHours == '-9999' && !this.input1 || this.closeAfterHours == '-9999' && this.input1 == 0) {
                this.$message.error('请输入正确的时间')
                return
            }
            if (this.closeAfterHours == '-9999' && !/^[0-9]+$/.test(this.input1)) {
                this.$message.error('请输入大于0的纯数字')
                return
            }
            let closeAfterHours = this.closeAfterHours
            if(this.closeAfterHours == '-9999') {
                closeAfterHours = this.input1
            }
            if(this.closeAfterHours == '-1000') {
                closeAfterHours = 0
            }
            updateCloseTimeOrStatus({
                closeAfterHours: closeAfterHours,
                carcassDivisionTaskId: this.dataInfo.carcassDivisionTaskId
            }).then(res => {
                if(res.code == 200) {
                    this.$message.success('定时任务设置成功')
                    this.$emit('fixesTime')
                    this.handleClose()
                } else {
                    this.$message.error(res.message)
                }
            })
        }
    }
}
</script>

<style>

</style>