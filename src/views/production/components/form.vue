<template>
  <div style="padding-bottom: 80px" class="num_box">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" style="padding-right: 50px">
            <div class="fc">
                <span class="point_icon">生产任务信息</span>
            </div>
            <el-row>
                  <el-col :span="12">
                    <el-form-item label="生产任务日期" prop="divisionTaskDate">
                        <el-date-picker
                            style="width: 100%"
                            v-model="ruleForm.divisionTaskDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            @change='setTaskName'
                            :picker-options="pickerOptions"
                            placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产任务单号" prop="divisionTaskCode">
                        <el-row>
                            <el-col :span="16">
                                <el-input :disabled='systemCode' maxlength="25" placeholder="FG+时间" v-model="ruleForm.divisionTaskCode"></el-input>
                            </el-col>
                            <el-col :span="8" style="padding-left: 20px">
                                <el-checkbox v-model="systemCode">用系统编号</el-checkbox>
                            </el-col>
                        </el-row>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产任务类型" prop="divisionTaskType">
                        <el-select v-model="ruleForm.divisionTaskType" style="width: 100%" @change='setTaskName'>
                            <el-option v-for="(item, index) in divisionTaskTypeList" :key='index' :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="任务名称" prop="divisionTaskName">
                        <el-input v-model="ruleForm.divisionTaskName" maxlength="25" placeholder="默认生产任务日期+生产任务类型"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产任务类型" prop="materialsType">
                        <el-select v-model="ruleForm.materialsType" style="width: 100%" @change='setTaskName'>
                            <el-option v-for="(item, index) in materialsTypeList" :key='index' :label="item.text" :value="item.value" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="交付日期" prop="deliveryDate">
                        <el-date-picker style="width: 100%" v-model="ruleForm.deliveryDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="负责人" prop="managerUserId">
                        <el-select
                            v-model="ruleForm.managerUserId"
                            style="width: 100%"
                            remote
                            filterable
                            reserve-keyword
                            placeholder="请输入关键词"
                            :remote-method="remoteMethod"
                            @change='changeManagerUser'
                            :loading='loading'>
                            <el-option
                                v-for="item in managerList"
                                :key="item.userId"
                                :label="item.corprateName || item.nickName"
                                :value="item.userId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="所在部门" prop="managerDepartment">
                        <el-input v-model="ruleForm.managerDepartment"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="ruleForm.remark"
                            type="textarea"
                            rows='3'
                            maxlength="100"
                            placeholder="输入内容"
                            show-word-limit></el-input>
                    </el-form-item>
                  </el-col>
            </el-row>
            <!-- 生产人员 -->
            <!-- <el-row style="padding: 0 100px">
                <Personnel></Personnel>
            </el-row> -->
            <div class="fc">
                <span class="point_icon">生成产品</span>
            </div>
            <el-row>
                <div>
                    <Generate ref="generate" :productList='productList'></Generate>
                </div>
            </el-row>
            <div class="fc">
                <span class="point_icon">消耗物料</span>
            </div>
            <el-row>
                <div v-if="ruleForm.divisionTaskType == 3">
                    <CollectProduct ref="collectProduct" :collectProductList='collectProductList' :divisionTaskType='ruleForm.divisionTaskType'></CollectProduct>
                </div>
                <div v-else>
                    <Materials ref="materials" :materialsList='materialsList' :divisionTaskType='ruleForm.divisionTaskType'></Materials>
                </div>
            </el-row>
            
            <el-form-item class="fcc footer_btn">
                <el-button type="primary" class="add_fill_btn" size="small" @click="draft">暂 存</el-button>
                <el-button type="primary" size="small" @click="submitForm('ruleForm')">提 交</el-button>
                <el-button size="small" class="grey_fill_btn" @click="resetForm('ruleForm')">取 消</el-button>
            </el-form-item>
        </el-form>
        <Confirm ref="confim" :divisionTaskType='ruleForm.divisionTaskType' @submit="submit"></Confirm>
  </div>
</template>

<script>
import {
    listUser,
    carcassDivisionTaskInfo,
    carcassDivisionTaskEdit,
    carcassDivisionTaskAdd,
    carcassDivisionTaskParam
} from "@/api/production/index.js";
// import Personnel from "./personnel.vue"
import Generate from './generate1.vue'
import Materials from './materials.vue'
import Confirm from './confirm.vue'
import CollectProduct from './collectProduct.vue'
export default {
    dicts: ['mes_bt_flag'],
    data() {
        return {
            systemCode: true,
            loading: false,
            managerList: [],
            divisionTaskTypeList: [
                { label: '热线分隔', value: '1' },
                { label: '冷线分割', value: '2' },
                { label: '二次精加工', value: '3' },
            ],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 86400000;
                },
            },
            divisionTaskStatus: -1, //分割任务状态（-1草稿,0已取消,1已完成,2进行中）
            collectFlag: true, //是否生成领料单（1是，0否）~
            ruleForm: {
                divisionTaskDate: '', //分割任务日期
                divisionTaskCode: '', //分割任务编号
                divisionTaskName: '', //分割任务名称
                divisionTaskType: '1', //分割任务类型（1热线分割，2冷线分割，3仓库领料）
                managerUserId: '', //任务负责人userId
                managerName: '', //负责人名称
                managerDepartmentId: '', //负责人所属部门ID
                managerDepartment: '', //负责人所属部门
                remark: '',
                collectName: '',
                materialsType: '',
                deliveryDate: ''
            },
            rules: {
                divisionTaskDate: [
                    { required: true, message: '请选择生产任务日期', trigger: 'change' },
                ],
                divisionTaskType: [
                    { required: true, message: '请选择生产任类型', trigger: 'change' },
                ],
                divisionTaskCode: [
                    { required: true, message: '请输入生产任务单号', trigger: 'blur' },
                ],
                divisionTaskName: [
                    { required: true, message: '请输入生产任务名称', trigger: 'blur' },
                ],
                materialsType: [
                    { required: true, message: '请选择生产类型', trigger: 'change' },
                ],
                deliveryDate:[
                    { required: true, message: '请选择交付日期', trigger: 'change' },
                ],
                managerUserId: [
                    { required: true, message: '请选择负责人', trigger: 'change' },
                ]
            },

            dialogVisible: false,
            prodTypeList: [],
            prodUnitList: [],
            generate: [],
            materials: [],
            collectProduct: [],
            productList: [],
            materialsList: [],
            collectProductList: [],
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
                { text: "不限", value: '' },
            ],
            materialsTypeList: [
                { text: "羊", value: 1 },
                { text: "牛", value: 2 },
            ],
            validProductList: [],
            validMaterialsList: [],
            validCollectProduct: []
        }
    },
    props: {
        taskId: String
    },
    components: {
        // Personnel
        Generate,
        Materials,
        Confirm,
        CollectProduct
    },
    watch: {
        systemCode() {
            this.$refs.ruleForm.clearValidate('divisionTaskCode')
            this.ruleForm.divisionTaskCode = ''
        },
        taskId() {
            if (this.taskId) {
                this.getInfo()
            } else if (!this.taskId) {
                const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
                this.remoteMethod(userInfo.corprateName || userInfo.nickName)
                this.ruleForm.managerUserId = userInfo.userId //负责人ID
                this.ruleForm.managerName = userInfo.corprateName || userInfo.nickName//负责人名称
                this.ruleForm.managerDepartmentId = userInfo.dept.deptId //负责人所属部门ID
                this.ruleForm.managerDepartment = userInfo.dept.deptName //负责人所属部门
            }
        }
    },
    created() {
        if (this.taskId) {
            this.getInfo()
        } else {
            const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
            this.remoteMethod(userInfo.userName)
            this.ruleForm.managerUserId = userInfo.userId //负责人ID
            this.ruleForm.managerName = userInfo.corprateName || userInfo.nickName //负责人名称
            this.ruleForm.managerDepartmentId = userInfo.dept.deptId //负责人所属部门ID
            this.ruleForm.managerDepartment = userInfo.dept.deptName //负责人所属部门
        }
    },
    methods: {
        getInfo() {
            carcassDivisionTaskInfo({
                carcassDivisionTaskId: this.taskId
            }).then(res => {
                if(res.code == 200) {
                    const dataInfo = res.result
                    this.ruleForm = {
                        divisionTaskDate: dataInfo.divisionTaskDate, //分割任务日期
                        divisionTaskCode: dataInfo.divisionTaskCode, //分割任务编号
                        divisionTaskName: dataInfo.divisionTaskName, //分割任务名称
                        divisionTaskType: dataInfo.divisionTaskType + '', //分割任务类型（1热线分割，2冷线分割，3仓库领料）
                        managerUserId: dataInfo.managerUserId, //任务负责人userId
                        managerName: dataInfo.managerName, //负责人名称
                        managerDepartmentId: dataInfo.managerDepartmentId, //负责人所属部门ID
                        managerDepartment: dataInfo.managerDepartment, //负责人所属部门
                        remark: dataInfo.remark,
                        collectName: dataInfo.collectName,
                        materialsType: dataInfo.materialsType,
                        deliveryDate: dataInfo.deliveryDate
                    }
                    this.productList = dataInfo.productList.map((item) => {
                        // item.unitWeightName = item.unitWeight + '/' + item.unitName;
                        
                        if (item.weightingType == 2) {
                            item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                        } else {
                            item.unitWeightName = item.unitWeight + '/' + item.unitName;
                        }
                        this.weightingTypeList.forEach((i) => {
                            if (i.value == item.weightingType) {
                                item.weightingTypeName = i.text;
                            }
                        });
                        return item
                    })
                    this.materialsList = dataInfo.materialsList.map((item) => {
                        this.materialsTypeList.forEach((i) => {
                            if (i.value == item.materialsType) {
                                item.materialsTypeName = i.text;
                            }
                        });
                        return item
                    })
                    this.collectProductList = dataInfo.collectProductList.map((item) => {
                        if (item.weightingType == 2) {
                            item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                        } else {
                            item.unitWeightName = item.unitWeight + '/' + item.unitName;
                        }
                        this.weightingTypeList.forEach((i) => {
                            if (i.value == item.weightingType) {
                                item.weightingTypeName = i.text;
                            }
                        });
                        return item
                    })
                    this.remoteMethod(dataInfo.managerName)
                }
            })
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
                listUser({
                    pageNum: 1,
                    pageSize: 999,
                    phonenumber: query,
                    tenantId: userInfo.tenantId
                }).then(res => {
                    this.loading = false;
                    if(res.code == 200) {
                        this.managerList = res.result.list
                    } else {
                        this.managerList = [];
                    }
                })
            } else {
                this.managerList = [];
            }
        },
        changeManagerUser(value, row) {
            this.managerList.forEach(item => {
                if (value == item.userId) {
                    this.ruleForm.managerName = item.corprateName || item.nickName //负责人名称
                    this.ruleForm.managerDepartmentId = item.dept.deptId //负责人所属部门ID
                    this.ruleForm.managerDepartment = item.dept.deptName //负责人所属部门
                }
            });
        },
        setTaskName() {
            if (!this.ruleForm.divisionTaskDate) {return}
            this.divisionTaskTypeList.forEach(item => {
                if (item.value == this.ruleForm.divisionTaskType) {
                    this.ruleForm.divisionTaskName = this.ruleForm.divisionTaskDate + ' ' + item.label
                }
            })
        },
        submitForm() {
            if (!this.ruleForm.divisionTaskDate) {
                this.$message.error('请选择分割任务日期')
                return
            }
            if (!this.ruleForm.divisionTaskName) {
                this.$message.error('请输入生产任务名称')
                return
            }
            if (!this.ruleForm.materialsType) {
                this.$message.error('请选择生产类型')
                return
            }
            if (!this.ruleForm.divisionTaskType) {
                this.$message.error('请选择生产任类型')
                return
            }
            if (!this.ruleForm.deliveryDate) {
                this.$message.error('请选择交付日期')
                return
            }
            if (!this.ruleForm.managerUserId) {
                this.$message.error('请选择负责人')
                return
            }
            const generate = this.$refs.generate.tableData
            if (generate.length < 1) {
                this.$message.error('请添加生成产品')
                return
            }
            const materials = this.$refs.materials && this.$refs.materials?.tableData
            if (materials && materials.length < 1 && (this.ruleForm.divisionTaskType == 1 || this.ruleForm.divisionTaskType == 2)) {
                this.$message.error('请添加消耗物料')
                return
            }
            const collectProduct = this.$refs.collectProduct && this.$refs.collectProduct?.tableData
            if (collectProduct && collectProduct.length < 1 && this.ruleForm.divisionTaskType == 3) {
                this.$message.error('请添加消耗物料')
                return
            }
            this.generate = generate
            this.materials = materials || []
            this.collectProduct = collectProduct || []
            this.divisionTaskStatus = 2
            this.$refs.confim.showModel()
        },
        draft() {
            // this.generate = this.$refs.generate && this.$refs.generate.tableData
            // this.materials = this.$refs.materials && this.$refs.materials.tableData
            // this.collectProduct = this.$refs.collectProduct && this.$refs.collectProduct.tableData
            if (!this.ruleForm.divisionTaskDate) {
                this.$message.error('请选择生产任务日期')
                return
            }
            if (!this.ruleForm.divisionTaskName) {
                this.$message.error('请输入生产任务名称')
                return
            }
            if (!this.ruleForm.divisionTaskType) {
                this.$message.error('请选择生产任类型')
                return
            }
            if (!this.ruleForm.deliveryDate) {
                this.$message.error('请选择交付日期')
                return
            }
            if (!this.ruleForm.managerUserId) {
                this.$message.error('请选择负责人')
                return
            }
            const generate = this.$refs.generate.tableData
            if (generate.length < 1) {
                this.$message.error('请添加生成产品')
                return
            }
            const materials = this.$refs.materials && this.$refs.materials?.tableData
            if (materials && materials.length < 1 && (this.ruleForm.divisionTaskType == 1 || this.ruleForm.divisionTaskType == 2)) {
                this.$message.error('请添加消耗物料')
                return
            }
            const collectProduct = this.$refs.collectProduct && this.$refs.collectProduct?.tableData
            if (collectProduct && collectProduct.length < 1 && this.ruleForm.divisionTaskType == 3) {
                this.$message.error('请添加消耗物料')
                return
            }
            this.generate = generate
            this.materials = materials || []
            this.collectProduct = collectProduct || []
            this.divisionTaskStatus = -1
            if (this.taskId) {
                this.editSubmitt()
            } else {
                this.addSubmit()
            }
        },
        getFormList() {
            const productList = []
            const materialsList = []
            const collectProduct = []
            if(this.generate && this.generate.length > 0) {
                this.generate.forEach(item => {
                    productList.push({
                        productId: item.productId, //产品编号
                        unitWeight: item.unitWeight, //单位重量
                        unitWeightEnd: item.unitWeightEnd || '', //单位重量
                        // deliveryDate: item.deliveryDate, //交付日期
                        productNum: item.productNum, //生产数量
                        productWeight: item.productWeight, //生产重量
                        productWeightEnd: item.productWeightEnd || '', //生产重量
                        note: item.note
                    })
                })
            }
            if (this.materials && this.materials.length > 0) {
                this.materials.forEach(item => {
                    materialsList.push({
                        materialsId: item.materialsId, //原料ID
                        acidFlag: item.acidFlag, //是否已排酸（1是，0否;根据任务类型 divisionTaskType =1 热线，不需要排酸；divisionTaskType = 2 冷线，需要排酸）
                        materialsWeight: item.materialsWeight, //计划消耗重量
                        materialsNum: item.materialsNum, //计划消耗数量
                        note: item.note
                    })
                })
            }
            if (this.collectProduct && this.collectProduct.length > 0) {
                this.collectProduct.forEach(item => {
                    collectProduct.push({
                        productId: item.productId, //产品ID
                        warehouseId: item.warehouseId, //仓库ID
                        productNum: item.productNum, //计划消耗数量
                        note: item.note //二次精加工任务消耗物料备注
                    })
                })
            }
            this.validProductList = productList;
            this.validMaterialsList = materialsList;
            this.validCollectProduct = collectProduct;
        },
        submit(data) {
            this.collectFlag = data.collectFlag
            this.ruleForm.collectName = data.collectName
            if (this.taskId) {
                this.editSubmitt()
            } else {
                if (this.divisionTaskStatus != -1) {
                    this.paramSubmit()
                } else {
                    this.addSubmit()
                }
            }
        },
        editSubmitt() {
            this.getFormList()
            carcassDivisionTaskEdit({
                ...this.ruleForm,
                carcassDivisionTaskId: this.taskId,
                divisionTaskStatus: this.divisionTaskStatus,
                collectFlag: this.divisionTaskStatus == -1 ? 0 : this.collectFlag ? 1 : 0,
                productList: this.validProductList,
                materialsList: this.validMaterialsList,
                collectProductList: this.validCollectProduct
            }).then(res => {
                this.$message.success('保存成功')
                this.$refs.ruleForm.resetFields();
                this.$emit('close')
            })
        },
        addSubmit() {
            this.getFormList()
            carcassDivisionTaskAdd({
                ...this.ruleForm,
                divisionTaskStatus: this.divisionTaskStatus,
                collectFlag: this.divisionTaskStatus == -1 ? 0 : this.collectFlag ? 1 : 0,
                productList: this.validProductList,
                materialsList: this.validMaterialsList,
                collectProductList: this.validCollectProduct
            }).then(res => {
                this.$message.success(this.divisionTaskStatus == -1 ? '已暂存' : '保存成功')
                this.$refs.ruleForm.resetFields();
                this.$emit('close')
            })
        },
        paramSubmit () {
            this.addSubmit()
            // carcassDivisionTaskParam().then(res => {
            //     if(res.code == 200) {
            //         this.addSubmit()
            //     } else {
            //         this.$message(res.message)
            //     }
            // })
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding-left: 20px
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
.footer_btn{
    width: 80%;
}
</style>
<style>
.num_box .el-input-number.is-without-controls .el-input__inner{
    text-align: left;
}
</style>