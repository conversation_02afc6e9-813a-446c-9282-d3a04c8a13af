<template>
    <div class="main">
        <div style="display:flex; margin-bottom: 10px">
            <MaterialsList @selectList='getSelectList' :defaultData='tableData'></MaterialsList>
            <el-button style="margin-left: 10px" size="mini" class="delete_btn" @click="removeData">删除</el-button>
        </div>
        <el-table :data="tableData" :show-summary="true"
                    max-height="300" border
                    @select-all="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center">
                <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.check"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
            <el-table-column prop="materialsTypeName" align="center" label="原料类型"></el-table-column>
            <el-table-column prop="materialsName" label="原料名称"></el-table-column>
            <el-table-column prop="acidFlagName" align="center" label="是否已排酸"></el-table-column>
            <el-table-column prop="materialsWeight" >
                <template slot="header">
                    <span>
                        计划消耗总重量（kg）
                        <span style="color:red">*</span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <el-input-number step-strictly :controls="false" style="width: 100%" v-model="scope.row.materialsWeight" type="number" :min="1" maxlength="8" placeholder="请输入"></el-input-number>
                </template>
            </el-table-column>
            <el-table-column prop="materialsNum" label="计划消耗数量" :sum-text="'合计'">
                <template slot-scope="scope">
                    <el-input-number step-strictly :controls="false" style="width: 100%" v-model="scope.row.materialsNum" type="number" :min="1" maxlength="8" placeholder="请输入"></el-input-number>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" placeholder="请输入内容"></el-input>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import MaterialsList from './materialsList1.vue'
export default {
    data() {
        return {
            tableData: [],
        }
    },
    props: {
        divisionTaskType: String,
        materialsList: Array,
    },
    watch: {
        divisionTaskType() {
            if (this.tableData.length > 1) {
                this.getSelectList(this.tableData)
            }
        },
        materialsList() {
            if (this.materialsList && this.materialsList.length > 0) {
                this.getSelectList(this.materialsList)
            }
        }
    },
    created() {
        if (this.materialsList && this.materialsList.length > 0) {
            this.getSelectList(this.materialsList)
        }
    },
    components: {
        MaterialsList
    },
    methods: {
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        getSelectList(data) {
            const data1 = JSON.parse(JSON.stringify(data))
            data1.forEach(item => {
                item.note = item.note
                item.check = false
                item.acidFlag = this.divisionTaskType == 2 ? 1 : 0
                item.acidFlagName = this.divisionTaskType == 2 ? '是' : '否'
                this.tableData.push(item)
            })
        },
        changeNum(item, index) {
            item.productWeight = item.unitWeight * item.productNum
            this.tableData.splice(index, 1, item);
        },
        removeData() {
            const indexs = []
            const tableData = []
            this.tableData.forEach((item, index) => {
                if(item.check) {
                    indexs.push(index)
                } else {
                    tableData.push(item)
                }
            })

            if(indexs.length > 0) {
                this.$confirm('点击后列表删除已选内容', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData = tableData
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
            } else {
                this.$message.info('请先勾选列表中想要删除的数据')
            }
        }
    }
}
</script>

<style>

</style>