<template>
    <div class="main">
        <div class="content">
            <span><el-tag closable>产品详情</el-tag></span>
            <el-button type="primary" size='small' plain icon="el-icon-plus" @click="addUser">添加人员</el-button>
        </div>
        <el-dialog
            title="选择人员"
            :visible.sync="dialogVisible"
            width="50%"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <div class="tree">
                    <div class="sreach">
                        <el-input placeholder="请输入内容" v-model="sreachValue" class="input-with-select">
                            <el-button slot="append" icon="el-icon-search"></el-button>
                        </el-input>
                    </div>
                    <div class="tree_content">
                        <el-tree
                            :data="treeList"
                            show-checkbox
                            node-key="id"
                            :default-expanded-keys="[3]"
                            :default-checked-keys="[7]">
                        </el-tree>
                    </div>
                </div>
                <div class="list">
                    <div class="list_title">
                        <span>已选：3</span>
                        <el-button type="text">清空</el-button>
                    </div>
                    <div class="list_item">
                        <span>22222</span>
                        <i class="el-icon-close"></i>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: true,
            sreachValue: '',
            treeList: [{
                    id: 3,
                    label: '二级 2-1',
                    disabled: true,
                    children: [{
                    id: 4,
                    label: '三级 3-1-1'
                    }, {
                    id: 5,
                    label: '三级 3-1-2',
                    }]
                }, {
                    id: 2,
                    label: '二级 2-2',
                    disabled: true,
                    children: [{
                    id: 6,
                    label: '三级 3-2-1'
                    }, {
                        id: 7,
                        label: '三级 3-2-2',
                    }]
                }],
                defaultProps: {
                children: 'children',
                label: 'label'
                }
        }
    },
    methods: {
        addUser() {
            this.dialogVisible = true
        },
        handleClose() {
            this.dialogVisible = false
        }
    }
}
</script>

<style scoped>
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.content span{
    margin-right: 10px;
    cursor: pointer;
}
.popup_content{
    display: flex;
}
.popup_content .tree, .popup_content .list{
    flex: 1;
    border: 1px solid #DCDFE6;
    /* padding: 10px; */
}
.sreach{
    background: #edf2fc;
    height: 60px;
    padding: 10px;
}
.tree_content{
    padding-left: 10px;
    padding-bottom: 10px;
}
.list_title{
    background: #edf2fc;
    height: 60px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.list_item{
    display: flex;
    justify-content: space-between;
    line-height: 36px;
}
</style>