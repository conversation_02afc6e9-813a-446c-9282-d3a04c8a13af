<template>
    <div class="dialog_box">
        <el-dialog
            title="操作确认"
            :visible.sync="dialogVisible"
            :modal='false'
            width="30%">
           <el-row style="padding-left: 45px" class="mt20">
                <div style="font-size: 18px">
                    是否提交任务？提交后将不支持编辑和删除
                </div>
                <div style="margin-top: 20px">
                    <el-checkbox v-model="collectFlag">
                        {{
                            divisionTaskType != 3 ? '同步计划消耗数量创建领料单' : '同步计划消耗数量创建出库单'
                        }}
                    </el-checkbox>
                </div>
                <div style="margin-top: 20px" v-if="collectFlag && divisionTaskType != 3">
                    <el-input v-model="collectName" placeholder="请输入领料单名称"></el-input>
                </div>
                <div style="margin-top: 20px" v-if="collectFlag && divisionTaskType != 3">
                    您可以维护一个领料单名称，方便分配人员查看
                </div>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" size="mini" @click="addSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            collectFlag: true,
            collectName: ''
        }
    },
    props: {
        divisionTaskType: String
    },
    methods: {
        addSubmit() {
            this.dialogVisible = false
            this.$emit('submit', {
                collectFlag: this.collectFlag,
                collectName: this.collectName
            })
        },
        showModel() {
            this.dialogVisible = true
        }
    }
}
</script>

<style>

</style>