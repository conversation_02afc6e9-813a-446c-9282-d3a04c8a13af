<template>
    <div class="main">
        <div id="print">
            <div class="fc">
                <span class="point_icon">生产入库记录</span>
            </div>
            <el-table :data="tableData" border>
                <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                <el-table-column show-overflow-tooltip prop="warehouseName" align="center" label="入库仓库"></el-table-column>
                <el-table-column show-overflow-tooltip prop="productName" label="产品名称"></el-table-column>
                <el-table-column show-overflow-tooltip prop="productTypeName" align="center" label="产品类型"></el-table-column>
                <el-table-column show-overflow-tooltip prop="specification" align="center" label="规格单位"></el-table-column>
                <el-table-column show-overflow-tooltip prop="inventoryNum" align="right" label="实际入库量" sortable></el-table-column>
                <el-table-column show-overflow-tooltip prop="inventoryWeight" align="right" label="实际入库重量（kg）" sortable></el-table-column>
                <el-table-column label="入库单" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-button size="mini" icon="el-icon-warning-outline" class="text_btn" @click="detail(scope.row)" type="text" >查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <InWarehouseDetail ref="inWarehouseDetail" :isfooter="isfooter" :carcassDivisionTaskId='dataInfo.carcassDivisionTaskId' :dataInfo="currentItem"></InWarehouseDetail>
    </div>
</template>

<script>
import InWarehouseDetail from './inWarehouseDetail.vue'
import { inventoryStatistics } from "@/api/production/index.js";
export default {
    data() {
        return {
            tableData: [],
            currentItem: {}
        }
    },
    components: {
        InWarehouseDetail
    },
    props: {
        dataInfo: Object,
        isfooter: String
    },
    watch: {
        dataInfo() {
            if (this.dataInfo && this.dataInfo.carcassDivisionTaskId){
                this.inventoryStatistics(this.dataInfo.carcassDivisionTaskId)
            }
        }
    },
    created() {
        if (this.dataInfo && this.dataInfo.carcassDivisionTaskId){
            this.inventoryStatistics(this.dataInfo.carcassDivisionTaskId)
        }
    },
    methods: {
        inventoryStatistics(carcassDivisionTaskId) {
			inventoryStatistics({
                carcassDivisionTaskId
            }).then((res) => {
				if (res.code == 200) {
					this.tableData = res.result;
					this.loading = false;
				}
			});
        },
        detail(row) {
            this.currentItem = row;
            this.$refs.inWarehouseDetail.showModel()
        }
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-8{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
</style>