<template>
    <div class="main">
        <div style="display:flex; margin-bottom: 10px">
            <ProductList @selectList='getSelectList' :defaultData='tableData'></ProductList>
            <el-button style="margin-left: 10px" class="delete_btn" size="mini" @click="removeData">删除</el-button>
        </div>
      
        <!-- <el-form :model="ruleForm" :rules="rules" ref="ruleForm"> -->
            <el-table :data="tableData" :show-summary="true"
                        max-height="300" border
                        @select-all="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.check"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column type="index" width="55" label="序号" align="center"></el-table-column>
                <el-table-column  label="内部产品编码" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.productCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="产品名称">
                    <template slot-scope="scope">
                        <span>{{ scope.row.productName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="weightingTypeName" align="center" width="80" label="称重类型"></el-table-column>
                <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
                <!-- <el-table-column prop="name" label="交付日期">
                    <template slot-scope="scope">
                        <el-date-picker style="width: 100%" v-model="scope.row.deliveryDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </template>
                </el-table-column> -->
                <el-table-column prop="productNum" :sum-text="'合计'">
                    <template slot="header">
                        <span>
                            计划生产数
                            <span style="color:red">*</span>
                        </span>
                    </template>
                    <template slot-scope="scope">

                        <!-- <el-form-item
                            :prop="'tableData.' + scope.$index + '.productNum'"
                            :rules="rules.productNum"
                        > -->
                            <el-input-number step-strictly :controls="false" style="width: 100%; text-align: left;" v-model="scope.row.productNum" type="number" :min="1" maxlength="5" placeholder="请输入" @input='changeNum(scope.row, scope.$index)'></el-input-number>
                        <!-- </el-form-item> -->
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="总重量（kg)">
                    <template slot-scope="scope">
                        <span v-if="scope.row.productNum > 0">
                            <span v-if="scope.row.weightingType == 2">
                                {{scope.row.productWeight}} - {{scope.row.productWeightEnd}}
                            </span>
                            <span v-else>
                                {{scope.row.productWeight}}
                            </span>
                        </span>
                        <!-- <el-input v-model="scope.row.productWeight" disabled type="number" placeholder=""></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="备注">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.note" placeholder="请输入内容"></el-input>
                    </template>
                </el-table-column>
            </el-table>
        <!-- </el-form> -->
    </div>
</template>

<script>
import ProductList from './productList.vue'
export default {
    data() {
        return {
            ruleForm: { tableData: [],},
            tableData: [],
            flag: false,
            rules: {
                productNum: [
                    { required: true, message: "请输入计划生产数", trigger: "blur" },
                ]
            }
        }
    },
    components: {
        ProductList
    },
    props: {
        productList: Array
    },
    watch: {
        productList() {
            if (this.productList && this.productList.length > 0) {
                this.getSelectList(this.productList)
            }
        }
    },
    created() {
        if (this.productList && this.productList.length > 0) {
            this.getSelectList(this.productList)
        }
    },
    methods: {
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        getSelectList(data) {
            const data1 = JSON.parse(JSON.stringify(data))
            data1.forEach(item => {
                item.note = item.note
                item.check = false
                this.tableData.push(item)
            })
        },
        changeNum(item, index) {
            if (item.weightingType == 2) {
                item.productWeight = ((item.unitWeight * item.productNum).toFixed(2) * 1)
                item.productWeightEnd = ((item.unitWeightEnd * item.productNum).toFixed(2) * 1)
            } else {
                item.productWeight = (item.unitWeight * item.productNum).toFixed(2) * 1
            }
            this.tableData.splice(index, 1, item);
        },
        removeData() {
            const indexs = []
            const tableData = []
            this.tableData.forEach((item, index) => {
                if(item.check) {
                    indexs.push(index)
                } else {
                    tableData.push(item)
                }
            })



            if(indexs.length > 0) {
                this.$confirm('点击后列表删除已选内容', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData = tableData
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
            } else {
                this.$message.info('请先勾选列表中想要删除的数据')
            }
        }
    }
}
</script>

<style>

</style>