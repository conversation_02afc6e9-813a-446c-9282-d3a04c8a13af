<template>
    <div class="dialog_box">
        <el-dialog
            title="操作确认"
            :visible.sync="dialogVisible"
            width="45%"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px">
                    <div class="sync">
                        <div class="sync-box">
                            <div class="swich">
                                <label>设置定时创建任务，系统可在设置的日期范围内，每天00:00自动生成任务数据</label>
                                <el-form-item>
                                    <el-switch
                                    active-color="#12AE63"
                                    inactive-color="#DCDCDC"
                                    v-model="ruleForm.timingTaskFlag"></el-switch>
                                </el-form-item>
                            </div>
                            <div class="tips">自动创建的任务第二天00:00变更为已完成</div>
                        </div>
                    </div>
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="生成时间" prop="createTimeConf" style="width: 100%">
                                <el-select v-model="ruleForm.createTimeConf" style="width: 100%" >
                                    <el-option label="当天" :value="1" />
                                    <el-option label="明天" :value="2" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="22">
                            <el-form-item label="原料类型" prop="materialsType" style="width: 100%">
                                <el-select v-model="ruleForm.materialsType" multiple style="width: 100%" >
                                    <el-option label="牛" :value="2" />
                                    <el-option label="羊" :value="1" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="22">
                            <el-form-item label="生产类型" prop="divisionTaskType" style="width: 100%">
                                <el-select v-model="ruleForm.divisionTaskType" multiple style="width: 100%" >
                                    <el-option label="冷线分割" :value="2" />
                                    <el-option label="热线分割" :value="1" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="22">
                            <el-form-item label="日期范围" required style="width: 100%">
                                <el-row>
                                    <el-col :span="11">
                                        <el-form-item prop="startTime">
                                            <el-date-picker type="date" placeholder="开始日期" v-model="ruleForm.startTime" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col class="fcc" :span="2">-</el-col>
                                    <el-col :span="11">
                                        <el-form-item prop="endTime">
                                            <el-date-picker type="date" placeholder="截止日期" v-model="ruleForm.endTime" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>

                        <el-col :span="22">
                            <el-form-item label="负责人" prop="managerUserId">
                                <el-select
                                    v-model="ruleForm.managerUserId"
                                    style="width: 100%"
                                    remote
                                    filterable
                                    reserve-keyword
                                    placeholder="请输入关键词"
                                    :remote-method="remoteMethod"
                                    @change='changeManagerUser'
                                    :loading='loading'>
                                    <el-option
                                        v-for="item in managerList"
                                        :key="item.userId"
                                        :label="item.corprateName || item.nickName"
                                        :value="item.userId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="22">
                            <el-form-item label="所在部门" prop="managerDepartment">
                                <el-input v-model="ruleForm.managerDepartment"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size='mini' @click="handleClose">取 消</el-button>
                <el-button size='mini' type="primary" @click="submitForm('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
        autoConf, 
        listUser
    } from "@/api/production/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                timingTaskFlag: true,
                createTimeConf: '', //生成时间规则：生成当天还是明天的任务,1当天 2明天
                materialsType: '', //分原料类型
                divisionTaskType: '', //生产任务类型（1热线 2冷线）
                startTime: '',//开始时间
                endTime: '',//结束时间
                managerUserId: '',
                managerDepartment: '',
                managerDepartmentId: ''
            },
            rules: {
                createTimeConf:[
                    { required: true, message: '请选择生成时间', trigger: 'change' },
                ], 
                materialsType:[
                    { required: true, message: '请选择原料类型', trigger: 'blur' },
                ], 
                divisionTaskType:[
                    { required: true, message: '请选择生产类型', trigger: 'blur' },
                ], 
                startTime:[
                    { required: true, message: '请选择开始日期', trigger: 'change' },
                ], 
                endTime:[
                    { required: true, message: '请选择截止日期', trigger: 'change' },
                ], 
                managerUserId:[
                    { required: true, message: '请选择负责人', trigger: 'change' },
                ], 
            },
            loading: false,
            managerList: []
        }
    },
    props: {
        dataInfo: Object
    },
    methods: {
        handleClose() {
            this.dialogVisible = false
            this.$refs.ruleForm.resetFields();
        },
        handleOpen() {
            this.dialogVisible = true
            const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
            this.remoteMethod(userInfo.corprateName || userInfo.nickName)
            this.ruleForm.managerUserId = userInfo.userId //负责人ID
            this.ruleForm.managerName = userInfo.corprateName || userInfo.nickName//负责人名称
            this.ruleForm.managerDepartmentId = userInfo.dept.deptId //负责人所属部门ID
            this.ruleForm.managerDepartment = userInfo.dept.deptName //负责人所属部门
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    autoConf({
                        ...this.ruleForm,
                        materialsType: this.ruleForm.materialsType.join(','),
                        divisionTaskType: this.ruleForm.divisionTaskType.join(','),
                        timingTaskFlag: this.ruleForm.timingTaskFlag ? 1 : 0
                    }).then(res => {
                        if(res.code == 200) {
                            this.handleClose()
                            this.$message.success('定时任务设置成功')
                            this.$emit('autoConf')
                        } else {
                            this.$message.error(res.message)
                        }
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
            
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
                listUser({
                    pageNum: 1,
                    pageSize: 999,
                    phonenumber: query,
                    tenantId: userInfo.tenantId
                }).then(res => {
                    this.loading = false;
                    if(res.code == 200) {
                        this.managerList = res.result.list
                    } else {
                        this.managerList = [];
                    }
                })
            } else {
                this.managerList = [];
            }
        },
        changeManagerUser(value, row) {
            this.managerList.forEach(item => {
                if (value == item.userId) {
                    this.ruleForm.managerName = item.corprateName || item.nickName //负责人名称
                    this.ruleForm.managerDepartmentId = item.dept.deptId //负责人所属部门ID
                    this.ruleForm.managerDepartment = item.dept.deptName //负责人所属部门
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>

.sync{
    margin-top: 10px;
    margin-bottom: 10px;
    .sync-box{
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        padding-left: 10px;
        padding-right: 10px;
        .swich{
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #333333;
            line-height: 22px;
            label{
                margin-right: 10px;
            }
            .el-form-item{
                margin: 0 !important;
            }
        }
        .tips{
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #999999;
            line-height: 22px;      
        }
    }
}
</style>
<style>
.swich .el-form-item__content{
    margin-left: 0 !important;
}
</style>