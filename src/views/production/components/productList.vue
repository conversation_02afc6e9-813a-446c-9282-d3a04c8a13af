<template>
    <div class="dialog_box">
        <el-button size="mini" class="add_btn" @click="showModel">添加</el-button>
        <el-dialog
            title="选择产品"
            :visible.sync="dialogVisible"
            width="1200px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-form ref="ruleForm" label-width="80px" class="mt20">
                    <el-row>
                        <el-col :span="4">
                            <el-form-item label="产品编码" prop="productCode">
                                <el-input size="mini" v-model="ruleForm.productCode"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="产品名称" prop="productName">
                                <el-input size="mini" v-model="ruleForm.productName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="产品类型" prop="productTypeId">
                                <el-select size="mini" v-model="ruleForm.productTypeId" style="width: 100%">
                                    <el-option v-for="(item, index) in prodTypeList" :key='index' :label="item.productTypeName" :value="item.productTypeId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="称重类型" prop="weightingTypeList">
                                <el-select size="mini" v-model="ruleForm.weightingType" style="width: 100%">
                                    <el-option v-for="(item, index) in weightingTypeList" :key='index' :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" class="btn">
                            <el-form-item>
                                <el-button size="mini" type="primary" @click="handleQuery" >搜索</el-button
                                >
                                <el-button size="mini" @click="resetQuery"
                                >重置</el-button
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div class="tabel">
                    <el-table :data="tableData" border @select-all="handleSelectionChange" max-height="380">
                        <el-table-column type="selection" width="55">
                            <template slot-scope="scope">
                                <el-checkbox :disabled='scope.row.disalbel' v-model="scope.row.check"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                        <el-table-column prop="productCode" align="center" label="内部产品编码"></el-table-column>
                        <el-table-column prop="productName" label="产品名称"></el-table-column>
                        <el-table-column prop="productTypeName" align="center" label="产品类型"></el-table-column>
                        <el-table-column prop="weightingTypeName" align="center" label="称重类型"></el-table-column>
                        <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
                    </el-table>
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="ruleForm.pageNum"
                        :limit.sync="ruleForm.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button size="mini" type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { productList, productTypeList } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            sreachValue: '',
            prodTypeList: [],
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
                { text: "不限", value: '' },
            ],
            ruleForm: {
                pageNum: 1,
                pageSize: 10,
                productCode: '', //产品编码
                productName: '', //产品名称
                productTypeId: '', //产品类型主键ID
                weightingType: '', //称重类型（1定重，2抄码）
                productStatus: '1' //状态（1启用，0禁用）
            },
            tableData: [],
            total: 0
        }
    },
    props: {
        defaultData: Array
    },
    created() {
        this.getSelectType()
        this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.getList();
        },
        handleClose() {
            this.dialogVisible = false
        },
        //重置
        resetQuery() {
            this.$refs.ruleForm.resetFields();
            this.ruleForm = {
                pageNum: 1,
                pageSize: 10,
                productCode: '', //产品编码
                productName: '', //产品名称
                productTypeId: '', //产品类型主键ID
                weightingType: '', //称重类型（1定重，2抄码）
                productStatus: '1' //状态（1启用，0禁用）
            }
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.ruleForm.pageNum = 1;
            this.getList();
        },
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                if (!item.disalbel) {
                    item.check = data.length > 0 ? true : false;
                }
                return item
            })
        },
        getList() {
            productList({
                ...this.ruleForm
            }).then(res => {
                if (res.code == 200) {
                    if (this.defaultData && this.defaultData.length > 0) {
                        this.tableData = res.result.list.map((item) => {
                            item.note = ''
                            item.disalbel = false 
                            if (item.weightingType == 2) {
                                item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                            } else {
                                item.unitWeightName = item.unitWeight + '/' + item.unitName;
                            }
                            item.check =  false
                            this.weightingTypeList.forEach((i) => {
                                if (i.value == item.weightingType) {
                                    item.weightingTypeName = i.text;
                                }
                            });
                            this.defaultData.forEach(i => {
                                if (i.productCode == item.productCode) {
                                    item.disalbel = true 
                                }
                            });
                            return item
                        })
                    }  else {
                        this.tableData = res.result.list.map((item) => {
                            item.note = ''
                            if (item.weightingType == 2) {
                                item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                            } else {
                                item.unitWeightName = item.unitWeight + '/' + item.unitName;
                            }
                            item.check =  false
                            item.disalbel = false
                            this.weightingTypeList.forEach((i) => {
                                if (i.value == item.weightingType) {
                                    item.weightingTypeName = i.text;
                                }
                            });
                            return item
                        });
                    }
                    this.total = Number(res.result.total);
                }
            })
        },
        getSelectType() {
            productTypeList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                if (res.code == 200) {
                    this.prodTypeList = res.result.list;
                }
            });
        },
        submitForm() {
            const selectList = [];
            this.tableData.forEach(item => {
                if (item.check) {
                    selectList.push(item)
                }
            })
            this.$emit('selectList', selectList)
            this.handleClose()
        }
    }
}
</script>

<style scoped>
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.btn{
    display: flex;
    padding-left: 10px;
}
</style>