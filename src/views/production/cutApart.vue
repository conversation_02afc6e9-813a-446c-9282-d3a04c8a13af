<template>
    <div class="app-container">
        <el-card class="box-card form-card mb10" shadow="never" ref="formBox">
        <el-row>
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box"
            >
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item label="" prop="divisionTaskName">
                            <el-input
                            v-model="queryParams.divisionTaskName"
                            placeholder="任务名称"
                            />
                        </el-form-item>
                        <el-form-item label="" prop="divisionTaskCode">
                            <el-input
                            v-model="queryParams.divisionTaskCode"
                            placeholder="任务单号"
                            clearable
                            />
                        </el-form-item>
                        <el-form-item label="" prop="divisionTaskType">
                            <el-select v-model="queryParams.divisionTaskType" placeholder="分割任务类型">
                                <el-option v-for="(item, index) in divisionTaskTypeList" :key="index" :label="item.text" :value="item.value" />
                            </el-select>
                        </el-form-item> 
                        <el-form-item label="" prop="divisionTaskStatus">
                            <el-select v-model="queryParams.divisionTaskStatus" placeholder="任务状态">
                                <el-option v-for="(item, index) in divisionTaskStatusList" :key="index" :label="item.text" :value="item.value" />
                            </el-select>
                        </el-form-item> 
                    <el-form-item prop="taskTime">
                        <el-date-picker
                            v-model="taskTime"
                            type="daterange"
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="分割任务开始日期"
                            end-placeholder="分割任务结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-row>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    <template v-if="toggleSearchDom">
                        <el-button type="text" @click="packUp">
                            {{ toggleSearchStatus ? '收起' : '展开' }}
                            <i
                            :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                            ></i>
                        </el-button>
                    </template>
                </el-form-item>
            </el-row>
        </el-form>
        </el-row>
        </el-card>
        <el-card class="table_box" shadow="never" >
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col class="fend">
                    <el-button size="mini" icon="el-icon-plus" @click="addCutApart" class="default_btn">新建</el-button>
                    <el-button size="mini" icon="el-icon-notebook-2" @click="regularTime" class="default_btn">定时任务</el-button>
                    <el-button size="mini" icon="el-icon-download" @click="exportList" class="default_btn">导出数据</el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe border
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                >
                    <el-table-column type="index" width="55" label="序号" align="center"></el-table-column>
                    <el-table-column  show-overflow-tooltip v-for="(item, index) in tableColumn"
                    :key="index" :align="item.align" :sortable="item.sortable"
                    :prop="item.prop" :label="item.label" :min-width="item.width">
                        <template slot-scope="scope">
                            <span
                                v-if="item.prop == 'divisionTaskStatusName'"
                                :class="{
                                    orange: scope.row.divisionTaskStatus == 2,
                                    blue: scope.row.divisionTaskStatus == -1,
                                    green: scope.row.divisionTaskStatus == 1,
                                    grey: scope.row.divisionTaskStatus == 0,
                                }">{{ scope.row.divisionTaskStatusName }}
                            </span>
                            <span v-else>
                                {{ scope.row[item.prop] }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width='240' fixed="right">
                        <template slot-scope="scope">
                            <el-button class="text_btn" icon="el-icon-warning-outline" size="mini" @click="handleDetails(scope.row)" type="text" >查看</el-button>
                            <el-button class="text_btn" icon="el-icon-finished" size="mini" v-if="scope.row.divisionTaskStatus == -1" @click="submitTask(scope.row)" type="text" >提交任务</el-button>
                            <el-button class="edit_text_btn" icon="el-icon-edit" size="mini" v-if="scope.row.divisionTaskStatus == -1" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <!-- <el-button class="delete_text_btn" icon="el-icon-delete" size="mini" v-if="scope.row.divisionTaskStatus == -1" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button> -->
                            <el-button class="success_text_btn" icon="el-icon-circle-check" size="mini" v-if="scope.row.divisionTaskStatus == 2" @click="okTask(scope.row)" type="text" >完成任务</el-button>
                            <el-dropdown @command="handleCommand" ref="dropdown" :dateRow="scope.row" v-if="scope.row.divisionTaskStatus == -1" >
                                <span class="more-icon">
                                    <i class="el-icon-more"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item :command="{
                                        type: 'delete',
                                        row: scope.row
                                    }">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-drawer
            class="drawer_box"
            :visible.sync="addCutApartModel" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            title="生产任务"
            :wrapperClosable="false">
            <Model :taskId='taskId' @close='closeCutApart'></Model>
        </el-drawer>
        <Confirm ref="confim" :divisionTaskType='divisionTaskType' @submit="submit"></Confirm>
        <el-drawer
            class="drawer_box"
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            title="生产任务详情"
            :wrapperClosable="false">
            <Details :taskId='taskId' @closeCutApart='closeCutApart' @edit='editEnclosure'></Details>
        </el-drawer>
        <FixedTast ref="fixedTast" @autoConf="getList"></FixedTast>
    </div>
</template>
  
<script>
import Model from "./components/form.vue";
import Confirm from './components/confirm.vue'
import Details from './components/detail.vue'
import FixedTast from './components/fixedTast.vue'
import {
    carcassDivisionTaskList,
    updateCloseTimeOrStatus,
    carcassDivisionTaskDelete,
    carcassDivisionTaskExport
} from "@/api/production/index.js";
import {exportExcel} from '@/utils/east-mind.js'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                divisionTaskCode: "", //分割任务编号
                divisionTaskName: "", //分割任务名称
                divisionTaskType: '', //分割任务类型（1热线分割，2冷线分割，3仓库领料）
                divisionTaskStatus: ''
            },
            taskTime: [],
            productModelShow: false,
            addCutApartModel: false,
            detailStatus: false,
            defaultList: [],
            canChangeList: [],
            tableColumn: [
                { check: true, prop: "divisionTaskName",label: "任务名称", align: 'center', width: 260 },
                { check: true, prop: "divisionTaskCode", label: "任务单号", align: 'center', width: 200 },
                { check: true, prop: "divisionTaskTypeName", label: "任务类型", width: 130, align: 'center' },
                { check: true, prop: "divisionTaskStatusName", label: "任务状态", width: 100, align: 'center' },
                { check: true, prop: "managerName", label: "负责人", width: 130, align: 'center' },
                { check: true, prop: "divisionTaskDate", label: "任务日期", align: 'center', sortable: true, width: 180  },
            ],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            prodTypeList: [],
            divisionTaskTypeList: [
                { text: "热线分割", value: 1 },
                { text: "冷线分割", value: 2 },
                { text: "二次精加工", value: 3 },
            ],
            divisionTaskStatusList: [
                { text: "草稿", value: '-1' },
                { text: "已取消", value: '0' },
                { text: "已完成", value: '1' },
                { text: "进行中", value: '2' },
            ],
            taskId: '',
            dataRow: {},
            divisionTaskType: '',
            sreachShow: false,
            windowHeight: '',
        };
    },
    components: {
        Model,
        Confirm,
        Details,
        FixedTast
    },
    created() {
        this.getList()
    },
    methods: {
        //列表查询
        getList() {
            carcassDivisionTaskList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list.map((item) => {
                        this.divisionTaskTypeList.forEach((i) => {
                            if (i.value == item.divisionTaskType) {
                                item.divisionTaskTypeName = i.text;
                            }
                        });
                        this.divisionTaskStatusList.forEach((i) => {
                            if (i.value == item.divisionTaskStatus) {
                                item.divisionTaskStatusName = i.text;
                            }
                        });
                        return item
                    });
                    this.total = Number(res.result.total);
                }
            })
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.taskTime = []
        },
        submitTask(row) {
            this.dataRow = row
            this.divisionTaskType = row.divisionTaskType
            this.$refs.confim.showModel()
        },
        submit(data) {
            updateCloseTimeOrStatus({
                ...data,
                collectFlag: data.collectFlag ? 1 : 0,
                divisionTaskStatus: 2,
                carcassDivisionTaskId: this.dataRow.carcassDivisionTaskId
            }).then(res => {
                if (res.code == 200) {
                    this.$message.success('任务已提交')
                    this.getList();
                }
            })
        },
        okTask(row) {
            this.$confirm('是否确定完成生产任务？ 确定后不可再进行任何操作', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                updateCloseTimeOrStatus({
                    divisionTaskStatus: 1,
                    carcassDivisionTaskId: row.carcassDivisionTaskId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('任务已完成')
                        this.getList();
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });          
            });
        },
        deleteEnclosure(row) {
            this.$confirm('此操作将永久删除该文件任务, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                carcassDivisionTaskDelete({
                    carcassDivisionTaskId: row.carcassDivisionTaskId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('任务已完成')
                        this.getList();
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        handleCommand(data) {
            if (data.type == 'delete') {
                this.deleteEnclosure(data.row)
            }
        },
        handleDetails(row) {
            this.taskId = row.carcassDivisionTaskId
            this.detailStatus = true
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.taskTime);
            this.getList();
        },
        addCutApart() {
            this.taskId = ''
            this.addCutApartModel = true
        },
        // 定时任务
        regularTime() {
            this.$refs.fixedTast.handleOpen()
        },
        //编辑
        editEnclosure(row) {
            this.detailStatus = false
            this.taskId = row.carcassDivisionTaskId
            this.addCutApartModel = true
        },
        //关闭
        closeCutApart() {
            this.detailStatus = false
            this.addCutApartModel = false;
            this.getList();
        },
        exportList(){exportExcel(carcassDivisionTaskExport,this.queryParams,'分割任务')},
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  