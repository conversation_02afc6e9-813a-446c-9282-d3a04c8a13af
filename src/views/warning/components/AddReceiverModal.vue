<template>
  <div class="dialog_box">
    <el-dialog
      title="添加接收人"
      :visible.sync="dialogVisible"
      @close="resetForm"
      width="60%"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="receiver-dialog"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="queryParams" size="small" :inline="true">
          <el-form-item>
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 用户表格 -->
      <el-table
        ref="userTable"
        :data="userList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        height="400"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="userName" label="用户名" min-width="180" align="center" />
        <!-- <el-table-column prop="nickName" label="姓名" min-width="180" align="center" /> -->
        <!-- <el-table-column prop="email" label="邮箱" min-width="180" align="center" /> -->
        <el-table-column prop="phonenumber" label="手机号" min-width="120" align="center" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        style="margin-top: 16px;"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="selectedUsers.length === 0">
          添加 ({{ selectedUsers.length }})
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userList, warningReceiverAdd } from '@/api/warning.js'

export default {
  name: 'AddReceiverModal',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      userList: [],
      selectedUsers: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
    }
  },
  methods: {
    showModal() {
      this.dialogVisible = true
      this.getList()
    },

    // 用户列表
    async getList() {
      this.loading = true
      try {
        const res = await userList(this.queryParams)
        if (res.code === 200) {
          console.log('🚀🚀获取用户列表',res)
          this.userList = res.rows
          this.total = Number(res.total)
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
      this.getList()
    },

    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },

    async submitForm() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请选择要添加的接收人')
        return
      }

      try {
        const receiveUserIdList = this.selectedUsers.map(user => user.userId)
        const res = await warningReceiverAdd({ receiveUserIdList })
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.$emit('refresh')
          this.resetForm()
        }
        
        
      } catch (error) {
        console.error('添加接收人失败:', error)
      }
    },

    // 重置表单
    resetForm() {
      this.dialogVisible = false
      this.selectedUsers = []
      this.userList = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: ''
      }
      // 清空表格选择
      this.$nextTick(() => {
        if (this.$refs.userTable) {
          this.$refs.userTable.clearSelection()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #F7F8FA;
  border-radius: 4px;
}

.dialog-footer {
  text-align: center;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
</style>

<style lang="scss">
// 全局样式：修复添加接收人弹窗层级问题
.receiver-dialog {
  z-index: 3000 !important;

  .el-dialog__wrapper {
    z-index: 3000 !important;
  }

  .el-overlay {
    z-index: 2999 !important;
  }
}
</style>
