<template>
  <div class="mt20">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="155px"
        class="demo-ruleForm"
      >
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" :disabled='selectUserInfo.userId && selectUserInfo.isCert == 1 && drawer.id ? true : false' @input="changePhone"/>
        </el-form-item>
          <!-- <el-form-item label="供应商类型" prop="supplierType">
              <el-select v-model="form.supplierType" disabled clearable class="select" @change="changePhone">
                <el-option label="企业" :value="1" />
                <el-option label="中间商" :value="2" />
                <el-option label="养殖户" :value="3" />
              </el-select>
        </el-form-item> -->
        <el-form-item label="供应商名称" prop="supplierName">
            <el-input v-model="form.supplierName" placeholder="请输入供应商名称"  :disabled='selectUserInfo.userId && selectUserInfo.isCert == 1 ? true : false'/>
        </el-form-item>
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="form.contactName" placeholder="请输入联系人" :disabled='selectUserInfo.userId ? true : false'/>
        </el-form-item>
        <el-form-item :label="form.supplierType == 1 ? '统一信用代码' : '身份证号'" prop="certNo">
          <el-input v-model="form.certNo" placeholder="请输入证件号码" :disabled='selectUserInfo.userId ? true && selectUserInfo.isCert == 1 : false' />
        </el-form-item>
        <el-form-item label="所属地区" prop="businessAddressIds">
            <el-cascader :options="areaProps" :clearable="true" style="width:100%" v-model="address" @change="handleAreaChange" ref="myCascader" > </el-cascader>
        </el-form-item>
        <!-- <el-form-item label="历史采购次数" prop="totalTradeTime">
          <el-input type="number" v-model="form.totalTradeTime" placeholder="请输入历史采购次数" />
        </el-form-item>
        <el-form-item  type="number" label="历史采购羊只数" prop="totalSheepNum">
          <el-input v-model="form.totalSheepNum" placeholder="请输入历史采购次数" />
        </el-form-item>
        <el-form-item label="历史采购牛只数" prop="totalCowNum">
          <el-input type="number" v-model="form.totalCowNum" placeholder="请输入历史采购次数" />
        </el-form-item> -->
      </el-form>
      <div class="footer_btn fcc">
        <el-button size="mini"  type="primary" @click="submitForm">提交</el-button>
        <el-button size="mini"  @click="close">取消</el-button>
      </div>
    <Confirm ref="confirm" :registerType="selectUserInfo.registerFlag"  @submit="submit"></Confirm>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import {add,info,edit, selectSysUser} from "@/api/supplier/index.js"
import { bankCardList } from "@/api/settlementManage/index.js";
import Confirm from '../../settlementManage/bill/components/confirm.vue'
export default {
  mixins:[areaData],
  name: "EastMindMesappUiModelFrom",
  props:{
    drawer:{
        type:Object,
        default:{}
    }
  },
  components: {
    Confirm
  },
  data() {
    return {
        address:'',
        form:{
            supplierName:'',
            supplierType:3,
            contactName:'',
            contactPhone:'',
            certNo:'',
            businessAddress:'',//所属区域
            businessAddressIds:'',//id
            // totalTradeTime:'',
            // totalSheepNum:'',//历史羊
            // totalCowNum:''//牛
        },
        selectUserInfo: {},
        rules: {
        supplierName: [
          {
            required: true,
            message: "请填写供应商名称",
            trigger: "blur",
          },
        ],
        supplierType: [
          {
            required: true,
            message: "请选择类型",
            trigger: "blur",
          },
        ],
        contactName: [
          {
            required: true,
            message: "请填写联系人名称",
            trigger: "blur",
          },
        ],
        contactPhone: [
          {
            required: true,
            message: "请填写联系人电话",
            trigger: "blur",
          },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          },
        ],
        certNo: [
          {
            required: true,
            message: "请填写证件号码",
            trigger: "blur",
          },
        ],
        businessAddressIds: [
          {
            required: true,
            message: "请选择所属区域",
            trigger: "change",
          },
        ],
      },
    };
  },

created(){
    this.getInfo()
},
  methods: {
    //查看详情
    getInfo(){
        if(this.drawer?.id){
            info({supplierId:this.drawer.id}).then(res=>{
                if(res.code==200){
                    this.form=res.result
                    this.address=(res.result.businessAddressIds).split(',')
                    this.getSysUser({
                      supplierId: res.result.supplierId
                    })
                }
            })
        }
    },
    changePhone() {
      if (this.form.contactPhone.length === 11) {
        this.getSysUser()
      }
    },
    getSysUser(params) {
        this.$refs.ruleForm.clearValidate(['supplierName', 'contactName' ,'certNo' ,'businessAddressIds'])
        bankCardList({
          contactPhone: this.form.contactPhone,
          // supplierType: this.form.supplierType,
          // ...params
        }).then(res=>{
            if(res.code==200){
              const data = res.result
              if(data.supplierName) {
                  this.form.supplierName = data.supplierName
              }
              // if(data.supplierType) {
              //     this.form.supplierType = data.supplierType
              // }
              if(data.contactPhone) {
                  this.form.supplierContactPhone = data.contactPhone
              }
              if(data.certNo) {
                  this.form.certNo = data.certNo
              }
              this.form.userId = data.userId
              // if(data.contactName) {
              //     this.form.supplierContactName = data.contactName
              // }
              if (data.businessAddressIds) {
                  this.form.businessAddressIds =  data.businessAddressIds//id
                  this.form.businessAddress =  data.businessAddress
                  this.address=data.businessAddressIds && data.businessAddressIds.split(',')
              }
              this.selectUserInfo = data
            }
        })
    },
    close(){
        this.$emit("close")
    },
    handleAreaChange(){
        let areaCodeData  = this.$refs['myCascader'].getCheckedNodes()[0];
        this.form.businessAddressIds=(areaCodeData.path).toString()
        this.form.businessAddress=(areaCodeData.pathLabels).toString()
    },
    addInfo(registerFlag){
        add({
          ...this.form,
          ...registerFlag
        }).then(res=>{
            if(res.code==200){
                this.$message({
                  message: '添加成功',
                  type: 'success'
              });
              this.$emit("refresh");
              this.close();
            }
        })
    },
    updataInfo(registerFlag){
        edit({
          ...this.form,
          ...registerFlag
        }).then(res=>{
            if(res.code==200){
                this.$message({
                  message: '编辑成功',
                  type: 'success'
              });
              this.$emit("refresh");
              this.close();
            }
        })
    },
    submitForm(){
        this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }

        if (this.drawer.id && this.selectUserInfo.registerFlag != 0) {
          this.updataInfo();
        } else {
          this.$refs.confirm.showModel()
        }
      });
    },
    submit(registerFlag) {
      if (this.drawer.id) {
          this.updataInfo(registerFlag);
        } else {
          this.addInfo(registerFlag);
        }
    }
  },
};
</script>

<style lang="scss" scoped>
.select{
    width: 100%;
}
.submit{
    width: 100%;
    display: flex;
    justify-content: center;
}
.footer_btn{
  width: 700px;
}
</style>