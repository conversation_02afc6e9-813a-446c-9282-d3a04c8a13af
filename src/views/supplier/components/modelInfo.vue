<template>
  <div class="mt20">
    <el-descriptions border>
      <el-descriptions-item label="供应商名称">{{info.supplierName}}</el-descriptions-item>
      <el-descriptions-item label="类别">{{handelType( info.supplierType) }}</el-descriptions-item>
      <el-descriptions-item label="联系人">{{ info.contactName }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ info.contactPhone }}</el-descriptions-item>
      <el-descriptions-item label="身份证号码">{{ info.certNo }}</el-descriptions-item>
      <el-descriptions-item label="所属地区">{{ info.businessAddress }}</el-descriptions-item>
      <el-descriptions-item label="历史采购次数">{{ info.totalTradeTime }}</el-descriptions-item>
      <el-descriptions-item label="历史采购羊只数">{{ info.totalSheepNum }}</el-descriptions-item>
      <el-descriptions-item label="历史采购牛只数">{{ info.totalCowNum }}</el-descriptions-item>
      <el-descriptions-item label="供应商编码">{{ info.supplierCode }}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{ info.createUserName }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ info.updateTime }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {info} from "@/api/supplier/index.js"
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      info: {},
      list:[
        {text:'企业',value:1},
        {text:'中间商',value:2},
        {text:'养殖户',value:3}
      ]
    };
  },
  created() {
    this.getInfo();
  },
  computed:{
    handelType(){
        return (value)=>{
            let name=''
            this.list.forEach(item=>{
                if(item.value==value){
                    name=item.text
                }
            })
            return name
        }
    }
  },
  methods: {
    //查看详情
    getInfo() {
      if (this.dialog?.id) {
        info({ supplierId: this.dialog.id }).then((res) => {
          if (res.code == 200) {
            this.info = res.result;
          }
        });
      }
    },
  },
};
</script>

<style>
</style>