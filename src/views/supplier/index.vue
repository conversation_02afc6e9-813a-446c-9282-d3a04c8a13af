<template>
    <div class="app-container">
      <el-card class="box-card form-card mb10" shadow="never">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box"
            >
              <el-row class=" form_row">
                  <el-row class="form_col">
                      <el-form-item prop="supplierName">
                        <el-input v-model="queryParams.supplierName" placeholder="供应商名称" clearable />
                        </el-form-item>
                        <el-form-item prop="supplierType">
                          <el-select v-model="queryParams.supplierType" placeholder="供应商类型">
                            <el-option label="企业" value="1" />
                            <el-option label="中间商" value="2" />
                            <el-option label="养殖户" value="3" />
                          </el-select>
                        </el-form-item>
                        <el-form-item prop="businessAddressIds">
                                <el-cascader placeholder="地区" :options="areaProps" :clearable="true" v-model="queryParams.businessAddressIds"  ref="myCascader" > </el-cascader>
                            </el-form-item>
                        <el-form-item>
                        <el-date-picker
                          v-model="dateRange"
                          value-format="yyyy-MM-dd"
                          type="daterange"
                          range-separator="-"
                          start-placeholder="更新开始日期"
                          end-placeholder="更新结束日期"
                        ></el-date-picker>
                      </el-form-item>
                  </el-row>
            </el-row>
            <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
            </el-row>
          </el-form>
      </el-card>
      <el-card shadow="never" class="table_box card_radius_b">
        <el-row :gutter="10" class="mb8 form_btn fend">
            <el-button class="default_btn" icon="el-icon-plus" size="mini" @click="addModel">新增</el-button>
            <el-button  icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
          <el-table :data="tableData" stripe border style="width: 100%" v-loading="loading" :max-height="tableHeight">
            <el-table-column  show-overflow-tooltip type="index" align="center" width="55" label="序号"></el-table-column>
            <el-table-column  show-overflow-tooltip prop="supplierCode" align="center" label="供应商编码" min-width="150px"  />
            <el-table-column  show-overflow-tooltip prop="supplierName" align="center" min-width="150" label="供应商名称"></el-table-column>
            <el-table-column  show-overflow-tooltip prop="supplierType" align="center" min-width="120" label="供应商类型" :formatter="formatStatus"></el-table-column>
            <el-table-column  show-overflow-tooltip prop="contactPhone" align="center" label="联系电话" min-width="130"></el-table-column>
            <el-table-column  show-overflow-tooltip prop="businessAddress" align="left"  min-width="260" label="所属地区"></el-table-column>
            <el-table-column  show-overflow-tooltip align="center"  min-width="160" label="是否为平台用户">
              <template slot-scope="scope">
                {{ scope.row.userId ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column  show-overflow-tooltip prop="createUserName" align="center" min-width="120" label="创建人"></el-table-column>
            <el-table-column  show-overflow-tooltip prop="updateTime" align="center" label="更新时间" sortable min-width="160px"></el-table-column>
            <el-table-column  show-overflow-tooltip label="操作" min-width="200px" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-warning-outline"
                  @click="handleDetails(scope.row.supplierId)"
                  type="text"
                  size="mini"
                  class="text_btn"
                >查看</el-button>
                <el-button
                  icon="el-icon-edit"
                  @click="editForm(scope.row.supplierId)"
                  type="text"
                  size="mini"
                  class="edit_text_btn"
                >编辑</el-button>
                <el-button
                  icon="el-icon-delete"
                  @click="delData(scope.row.supplierId)"
                  type="text"
                  size="mini"
                  class="delete_text_btn"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
      <el-drawer
        class="drawer_box"
        :title="drawer.title" 
        :visible.sync="drawer.open" 
        :show-close="true" 
        :append-to-body="true" 
        :modal="true"
        :destroy-on-close="true"
        size="700px"
        :wrapperClosable="false">
          <modelFrom @close="close" @refresh="refresh" ref="modelFrom" :drawer="drawer"></modelFrom>
      </el-drawer>
      <!-- 详情 -->
      <el-dialog
        class="dialog_box"
        title="供应商详情"
        :visible.sync="dialog.open"
        width="70%"
        append-to-body
        @close="dialog.open=false"
      >
        <modelInfo :dialog="dialog" v-if="dialog.open" ></modelInfo>
      </el-dialog>
    </div>
  </template>
  
<script>
  import {list ,del,supplierExport} from '@/api/supplier/index.js'
  import modelFrom from './components/modelFrom.vue'
  import modelInfo from './components/modelInfo.vue'
  import { areaData } from "@/utils/mixin/area.js";
  import { exportExcel } from '@/utils/east';
  import { tableUi } from "@/utils/mixin/tableUi.js";
  export default {
    mixins:[areaData, tableUi],
    components:{
        modelFrom,
        modelInfo
    },
    data() {
      return {
        drawer:{
            open:false,
            title:'新增供应商'
        },
        dialog:{
            open:false,
            id:''
        },
        status: [
          { text: "企业", value: 1 },
          { text: "中间商", value: 2 },
          { text: "养殖户", value: 3 },
        ],
        queryParams: {
            businessAddressIds:'',
            supplierName:'',
            supplierType:'',
          pageNum: 1,
          pageSize: 10,
        },
        tableData:[],
        dateRange:[],
        loading: true,
        total: 0,
      };
    },
    computed: {
      formatStatus() {
        return (row, com, val) => {
          let name = "";
          this.status.forEach((item) => {
            if (item.value == val) {
              name = item.text;
            }
          });
          return name;
        };
      },
    },
    created() {
      this.getList();
    },
    methods: {
        //导出数据
        exportList(){
            exportExcel(supplierExport,this.queryParams,'供应商')
        },
        //新增
        addModel(){
            this.drawer.open=true
            this.drawer.title='新增供应商'
            this.drawer.id=''
        },
        //编辑
        editForm(id){
            this.drawer.open=true
            this.drawer.title='编辑供应商',
            this.drawer.id=id
        },
        close(){
            this.drawer.open=false
        },
        refresh(){
            this.getList();
        },
      //列表查询
      getList() {
        this.queryParams.businessAddressIds= this.queryParams.businessAddressIds.toString()
        list(this.queryParams).then((res) => {
          if (res.code == 200) {
            this.tableData = res.result.list;
            this.total = Number(res.result.total);
            this.loading = false;
          }
        });
      },
      reset() {
        this.resetForm("queryForm");
      },

      //重置
      resetQuery() {
        this.dateRange = [];
        this.reset();
        this.handleQuery();
      },
      //刷新页面
      refreshList() {
        this.getList();
      },
      //搜索
      handleQuery() {
        this.queryParams.pageNum = 1;
        if (this.dateRange?.length>0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1] ;
      } else {
       delete this.queryParams.startTime ;
       delete this.queryParams.endTime;
      }
        this.getList();
      },
      /** 查看详情按钮操作 */
      handleDetails(id) {
       this.dialog.open=true
       this.dialog.id=id
      },
      //删除
      delData(id){
        this.$confirm('此操作将永久删除该供应商, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            del({supplierId:id}).then(res=>{
                if(res.code==200){
                    this.getList();
                    this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                }
            })
          
        });
      }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  </style>
  