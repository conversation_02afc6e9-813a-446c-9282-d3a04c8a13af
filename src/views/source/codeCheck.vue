<template>
	<div class="app-container tabs_box">
        <el-card class="box-card form-card mb10" shadow="never">
            <el-row :gutter="10">
				<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" 
					class="form_box">
					<el-row class=" form_row">
						<el-row class="form_col">
							<el-form-item prop="companyName">
								<el-input v-model="queryParams.companyName" placeholder="申请企业" clearable />
							</el-form-item>
							<el-form-item prop="contactName">
								<el-input v-model="queryParams.contactName" placeholder="联系人" clearable />
							</el-form-item>
							<el-form-item prop="contactPhone">
								<el-input v-model="queryParams.contactPhone" placeholder="联系电话" clearable />
							</el-form-item>
							<el-form-item>
								<el-date-picker
								v-model="dateEnter"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="-"
								start-placeholder="申请开始日期"
								end-placeholder="申请结束日期"
								></el-date-picker>
							</el-form-item>
							<el-form-item>
								<el-date-picker
								v-model="dateEnter1"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="-"
								start-placeholder="处理开始日期"
								end-placeholder="处理结束日期"
								></el-date-picker>
							</el-form-item>
						</el-row>
					</el-row>
					<el-row>
						<el-form-item>
							<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
							<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
							<template v-if="toggleSearchDom">
								<el-button type="text" @click="packUp">
									{{ toggleSearchStatus ? '收起' : '展开' }}
									<i
									:class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
									></i>
								</el-button>
							</template>
						</el-form-item>
					</el-row>
				</el-form>
		    </el-row>
		</el-card>
		<el-card class="table_box" shadow="never">
			<el-row class="tabs-box">
				<el-tabs v-model="listType">
					<el-tab-pane label="待处理" name="0">
					</el-tab-pane>
					<el-tab-pane label="已处理" name="1">
					</el-tab-pane>
				</el-tabs>
			</el-row>
			<!-- 表格数据 -->
			<div :style="{height: tableHeight + 'px'}">
				<el-table :data="tableData" border v-if="listType == '0'" :max-height="tableHeight" style="width: 100%;margin-top: 10px;" v-loading="loading">
					<el-table-column  show-overflow-tooltip width="55" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="companyName" label="申请企业"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="contactName" label="联系人"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="contactPhone" label="联系电话"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="applyNum" sortable label="申请数量" width="120"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="createTime" sortable label="申请时间"></el-table-column>
					<el-table-column  show-overflow-tooltip label="状态" width="120">
						<template slot-scope="scope">
							<span :class="{
									orange: scope.row.applyStatus == 0,
									green: scope.row.applyStatus == 1,
								}">{{ scope.row.applyStatus == 1 ? '已处理' : '未处理' }}
							</span>
						</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip  label="操作" fixed="right">
						<template slot-scope="scope">
							<el-button class="text_btn" @click="addForm(scope.row)" type="text" >生成溯源码</el-button>
						</template>
					</el-table-column>
				</el-table>

				<el-table :data="tableData" border v-if="listType == 1" :max-height="tableHeight" style="width: 100%" v-loading="loading">
					<el-table-column  show-overflow-tooltip width="55" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="companyName" label="申请企业"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="contactName" label="联系人"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="contactPhone" label="联系电话" width="120"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="applyNum" sortable label="申请数量" width="120"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="createTime" sortable label="申请时间"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="actualNum" sortable label="实际下发数量"></el-table-column>
					<el-table-column  show-overflow-tooltip label="状态" width="120">
						<template slot-scope="scope">
							<span :class="{
									orange: scope.row.applyStatus == 0,
									green: scope.row.applyStatus == 1,
								}">{{ scope.row.applyStatus == 1 ? '已处理' : '未处理' }}
							</span>
						</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip prop="updateUserName" label="处理人" width="120"></el-table-column>
					<el-table-column  show-overflow-tooltip prop="updateTime" sortable label="处理时间"></el-table-column>
					<el-table-column  show-overflow-tooltip  label="操作" fixed="right">
						<template slot-scope="scope">
							<el-button class="text_btn" @click="exportList(scope.row)" type="text" >下载</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				@pagination="getList"
			/>
		</el-card>
		<CodeCreate ref="codeCreate" :traceCodeApplyId="currentItem.traceCodeApplyId" @getList="getList"></CodeCreate>
	</div>
</template>
      
<script>
import {
    applyList,
    traceCodeExport
} from "@/api/source/index.js";
import { exportExcel } from "@/utils/east";
import CodeCreate from "./components/codeCreate.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                companyName: "",
                contactName: "",
                contactPhone: "",
                pageNum: 1,
                pageSize: 10,
            },
            materialsList: [], //原料名称
            warehouseList: [], //原料等级
            tableData: [],
            loading: true,
            dateEnter: [],
            dateEnter1: [],
            total: 0,
            currentItem: {},
            modelShow: false,
            listType: '0',
            formModelShow: false,
			sreachShow: false,
        };
    },
    components: {
		CodeCreate
    },
    computed: {
    },
	watch: {
		listType() {
			this.getList()
		}
	},
    created() {
        this.getList();
	},
	methods: {
    refresh() {
        this.getList();
    },
    //列表查询
    getList() {
		applyList({
			...this.queryParams,
			applyStatus: this.listType
		}).then((res) => {
			if (res.code == 200) {
				this.tableData = res.result.list;
				this.total = Number(res.result.total);
				this.loading = false;
			}
		});
	},
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
        this.dateEnter = [];
        this.dateEnter1 = [];
        this.reset();
        this.handleQuery();
    },
    //刷新页面
    refreshList() {
        this.getList();
    },
    handelData(startTime, endTime, list) {
        if (list?.length > 0) {
            this.queryParams[startTime] = list[0];
            this.queryParams[endTime] = list[1];
        } else {
            delete this.queryParams[startTime];
            delete this.queryParams[endTime];
        }
    },
    //搜索
    handleQuery() {
        this.queryParams.pageNum = 1;
        this.handelData("startTime", "endTime", this.dateEnter);
        this.handelData("startTime2", "endTime2", this.dateEnter1);
        this.getList();
    },
	changeList(listType) {
		this.listType = listType
		this.queryParams.pageNum = 1
		this.queryParams.pageSize = 10
        this.getList();
	},
    addForm(row) {
		this.currentItem = row
		this.$refs.codeCreate.handleOpen()
    },
    exportList(row){
		exportExcel(traceCodeExport,
		{
			traceCodeApplyId: row.traceCodeApplyId
		},'溯源码列表')
	},
  },
};
</script>
      
<style lang="scss" scoped>
    .tab{
		padding: 0 20px;
		color: #333333;
		span{
			margin: 0 10px;
			cursor: pointer;
		}
    }
</style>
      