<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box">
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <!-- <el-form-item label="申请人" prop="butcherCode">
                            <el-input
                            v-model="queryParams.butcherCode"
                            placeholder="请输入申请人"
                            clearable
                            />
                        </el-form-item> -->
                        <el-form-item prop="acidTime">
                            <el-date-picker
                                v-model="acidTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="维护开始日期"
                                end-placeholder="维护结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
        <el-row class="mb8 form_btn fc">
            <el-col :span="12" style="color: #909399; font-size: 14px;">
                当前可使用溯源码共 <span style="color: #5672FA;font-size: 16px"> {{unActivationNum}} </span> 个
            </el-col>
            <el-col :span='12' class="fend">
                <el-button size="mini" class="default_btn" icon="el-icon-plus" @click="addCode">申请</el-button>
                <el-button size="mini" class="default_btn" icon="el-icon-download" @click="exportList">导出数据</el-button>
            </el-col>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
            <el-table
                :data="tableData"
                stripe border
                style="width: 100%"
                v-loading="loading" :max-height="tableHeight"
            >
                <el-table-column  show-overflow-tooltip align="center" width="55" type="index" label="序号"></el-table-column>
                <el-table-column  show-overflow-tooltip align="right" prop="applyNum" sortable label="申请数量"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" label="申请状态">
                    <template slot-scope="scope">
                        <span :class="{
                                orange: scope.row.applyStatus == 0,
                                green: scope.row.applyStatus == 1,
                            }">{{ scope.row.applyStatus == 1 ? '已处理' : '未处理' }}
                        </span>
                    </template>
                </el-table-column>
                <!-- <el-table-column  show-overflow-tooltip align="right" prop="unActivationNum" label="待激活数量"></el-table-column> -->
                <el-table-column  show-overflow-tooltip align="center" prop="createUserName" label="申请人"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" prop="createTime" sortable label="申请时间"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-download" v-if="scope.row.applyStatus == 1" class="text_btn" size="mini" @click="downLoadList(scope.row)" type="text" >下载</el-button>
                        <el-button icon="el-icon-warning-outline" v-if="scope.row.applyStatus == 1" class="text_btn" size="mini" @click="handleDetails(scope.row)" type="text" >查看激活信息</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
        </el-card>
        <el-drawer
            class="drawer_box"
            title='查看激活信息'
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="60%"
            :wrapperClosable="false">
            <ActivationInfo :traceCodeApplyId="traceCodeApplyId"></ActivationInfo>
        </el-drawer>
        <CodeApply ref="codeApply" @getList="getList"></CodeApply>
    </div>
</template>
  
<script>
import {
    applyList,
    applyExport,
    traceCodeExport,
    availableNums
} from "@/api/source/index.js";
import {exportExcel, exportTxt} from '@/utils/east-mind.js'
import CodeApply from './components/codeApply.vue'
import ActivationInfo from './components/activationInfo.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                // butcherCode: "",
            },
            detailStatus: false,
            tableData: [],
            loading: true,
            total: 0,
            dataRow: {},
            traceCodeApplyId: '',
            acidTime: [],
            windowHeight: 0,
            tableHeight: 0,
            tableBoxHeight: 0,
            unActivationNum: 0
            
        };
    },
    components: {
        CodeApply,
        ActivationInfo
    },
    created() {
        this.getList()
        this.availableNumFn()
    },
    methods: {
        availableNumFn() {
            availableNums({}).then((res) => {
                if (res.code == 200) {
                    this.unActivationNum = res.result.unActivationNum
                }
            })
        },
        //列表查询
        getList() {
            applyList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list
                    this.total = Number(res.result.total);
                }
            })
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.acidTime = []
        },
        handleDetails(row) {
            this.traceCodeApplyId = row.traceCodeApplyId
            this.detailStatus = true
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.acidTime);
            this.getList();
        },
        addCutApart() {
            this.traceCodeApplyId = ''
            this.addCutApartModel = true
        },
        //编辑
        editEnclosure(row) {
            this.detailStatus = false
            this.traceCodeApplyId = row.traceCodeApplyId
            this.addCutApartModel = true
        },
        //关闭
        closeCutApart() {
            this.addCutApartModel = false;
            this.resetQuery()
        },
        downLoadList(row){
            exportTxt(traceCodeExport,
            {
                traceCodeApplyId: row.traceCodeApplyId
            },'溯源码列表')
        },
        exportList(){
            exportExcel(applyExport,
            this.queryParams
            ,'溯源码列表')
        },
        
        addCode() {
            this.$refs.codeApply.handleOpen()
        }
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  