<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-row :gutter="10">
                <el-form
                    :model="queryParams"
                    ref="queryForm"
                    size="small"
                    :inline="true"
                    class="form_box">
                    <el-row class=" form_row">
                        <el-row class="form_col">
                            <el-form-item prop="productName">
                                <el-input
                                v-model="queryParams.productName"
                                placeholder="产品名称"
                                clearable
                                />
                            </el-form-item>
                            <el-form-item prop="relationCode">
                                <el-input
                                v-model="queryParams.relationCode"
                                placeholder="任务单号"
                                clearable
                                />
                            </el-form-item>
                            <el-form-item prop="templateName">
                                <el-select v-model="queryParams.templateName" placeholder="关联溯源模板">
                                    <el-option v-for="(item, index) in templateList" :key="index" :label="item.templateName" :value="item.templateName" />
                                </el-select>
                            </el-form-item> 
                            <el-form-item prop="acidTime">
                                <el-date-picker
                                    v-model="acidTime"
                                    type="daterange"
                                    value-format="yyyy-MM-dd"
                                    range-separator="至"
                                    start-placeholder="维护开始日期"
                                    end-placeholder="维护结束日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </el-row>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col class="fend">
                    <el-button size="mini" class="default_btn" icon="el-icon-edit" @click='bulkEdit'>批量编辑</el-button>
                    <el-button size="mini" class="default_btn" icon="el-icon-download" @click="exportList">导出数据</el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe border
                    style="width: 100%"
                    v-loading="loading"
                    ref="multipleTable"
                    @selection-change="handleSelectionChange"
                    :max-height="tableHeight"
                >
                    <el-table-column align="center" type="selection" width="55"> </el-table-column>
                    <el-table-column width="55" type="index" align="center" label="序号"></el-table-column>
                    <el-table-column show-overflow-tooltip v-for="(item, index) in tableColumn" :key="index" :sortable="item.sortable" :prop="item.prop" :label="item.label" :align="item.align" :min-width="item.width"></el-table-column>
                    <el-table-column label="操作" fixed="right" align="center" width="210">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-warning-outline" class="text_btn" size="mini" @click="handleCodingTraceModel(scope.row)" type="text" >查看溯源码</el-button>
                            <el-button icon="el-icon-edit" class="edit_text_btn" size="mini" @click="handleDetails(scope.row)" type="text" >编辑溯源信息</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-drawer
            class="drawer_box"
            title='溯源明细编辑'
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="90%"
            :wrapperClosable="false">
            <InfoForm @getList="resetQuery" @colse="resetQuery" :codingTraceRelationIds="codingTraceRelationIds" :codingTraceId="codingTraceId"></InfoForm>
        </el-drawer>
        <el-drawer
            class="drawer_box"
            title='查看溯源码'
            :visible.sync="codingTraceModel" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            :wrapperClosable="false">
            <CodeList :codingTraceId="codingTraceId"></CodeList>
        </el-drawer>
        
    </div>
</template>
<script>
import {
    codingTraceList,
    traceTemplateList,
    codingTraceExport
} from "@/api/source/index.js";
import {exportExcel} from '@/utils/east-mind.js'
import InfoForm from './components/infoForm.vue'
import CodeList from './components/codeList.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productName: "",
                relationCode: "", 
                templateName: '',
            },
            detailStatus: false,
            tableColumn: [
                { check: true, prop: "relationCode", label: "关联任务单号", align: 'center'},
                { check: true, prop: "traceCodeNum", label: "绑码量", width: 120, align: 'right', sortable: true },
                { check: true, prop: "productName", label: "关联产品" },
                { check: true, prop: "templateName", label: "关联溯源模板" },
            ],
            tableData: [],
            loading: true,
            total: 0,
            dataRow: {},
            acidTaskId: '',
            acidTime: [],
            multipleSelection: [],
            divisionTaskStatusList: [],
            templateList: [],
            codingTraceId: '',
            codingTraceModel: false,
            codingTraceRelationIds: [],
        };
    },
    components: {
        InfoForm,
        CodeList
    },
    created() {
        this.getList()
        this.getTemplateList()
    },
    methods: {
        //列表查询
        getList() {
            codingTraceList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list
                    this.total = Number(res.result.total);
                }
            })
        },
        getTemplateList() {
            traceTemplateList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.templateList = res.result.list
                }
            })
        },
        handleSelectionChange(val) {
            if (this.multipleSelection.length > 0) {
                val.forEach(item => {
                    this.multipleSelection.push(item)
                })
            } else {
                this.multipleSelection = val;
            }
        },
        toggleSelection(rows) {
            if (rows) {
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row);
                });
            }
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.acidTime = []
        },
        bulkEdit() {
            const selection = this.$refs.multipleTable.selection
            if (selection.length <= 0) {
                this.$message('请勾选要编辑的数据');
                return
            }
            const codingTraceRelationIds = []
            const idHash = {}
            selection.forEach(item => {
                if (idHash[item.traceTemplateId]) {
                    idHash[item.traceTemplateId] = idHash[item.traceTemplateId] + 1
                } else {
                    idHash[item.traceTemplateId] = 1
                }
                codingTraceRelationIds.push(item.codingTraceId)
            })
            console.log(Object.keys(idHash))
            if (Object.keys(idHash).length > 1) {
                this.$alert('请选择同一模板下的任务进行批量编辑', '操作确认', {
                    confirmButtonText: '我知道了',
                    type: 'warning'
                }).then(() => {
                   
                }).catch(() => {        
                });
            } else {
                this.codingTraceId = codingTraceRelationIds[0]
                this.codingTraceRelationIds = codingTraceRelationIds
                this.detailStatus = true
            }
        },
        handleDetails(row) {
            this.codingTraceId = row.codingTraceId
            this.codingTraceRelationIds = [row.codingTraceId]
            this.detailStatus = true
        },
        handleCodingTraceModel(row) {
            this.codingTraceId = row.codingTraceId
            this.codingTraceModel = true
        },
        //重置
        resetQuery() {
            this.detailStatus = false
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.acidTime);
            this.getList();
        },
        addCutApart() {
            this.acidTaskId = ''
            this.addCutApartModel = true
        },
        //编辑
        editEnclosure(row) {
            this.detailStatus = false
            this.acidTaskId = row.acidTaskId
            this.addCutApartModel = true
        },
        //关闭
        closeCutApart() {
            this.addCutApartModel = false;
            this.resetQuery()
        },
        exportList(){exportExcel(codingTraceExport,this.queryParams,'溯源明细')},
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  