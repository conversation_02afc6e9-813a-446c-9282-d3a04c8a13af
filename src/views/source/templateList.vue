<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box">
                <el-row class="form_row">
                    <el-row class="form_col">
                        <el-form-item prop="templateName">
                            <el-input
                            v-model="queryParams.templateName"
                            placeholder="模版名称"
                            clearable
                            />
                        </el-form-item>
                        <el-form-item prop="templateType">
                            <el-select v-model="queryParams.templateType" placeholder='标签类型' clearable>
                            <el-option
                                v-for="(item,index) in tabTypes"
                                :label="item.text"
                                :value="item.value"
                                :key="index"
                            />
                            </el-select>
                        </el-form-item>
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
        <el-row :gutter="10" class="mb8 form_btn">
            <el-col class="fend">
                <el-button size="mini" class="default_btn" icon="el-icon-plus" @click="addCode">新建</el-button>
            </el-col>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
            <el-table
                :data="tableData"
                stripe border
                style="width: 100%"
                v-loading="loading" :max-height="tableHeight"
            >
                <el-table-column align="center" width="55" type="index" label="序号"></el-table-column>
                <el-table-column  show-overflow-tooltip prop="companyName" label="企业名称"></el-table-column>
                <el-table-column  show-overflow-tooltip prop="templateName" label="模板名称"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" prop="applyNum" label="标签类型">
                    <template slot-scope="scope">
                        <span>{{ handeltext(tabTypes, scope.row.templateType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column  show-overflow-tooltip align="center" prop="updateUserName" label="创建人"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" prop="updateTime" sortable label="创建时间"></el-table-column>
                <el-table-column  show-overflow-tooltip align="center" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-edit" class="edit_text_btn" size="mini" @click="editTemplate(scope.row)" type="text" >编辑</el-button>
                        <el-button icon="el-icon-delete" class="delete_text_btn" size="mini" @click="deleteTemplate(scope.row)" type="text" >删除</el-button>
                    </template>
                </el-table-column>
            </el-table>`
        </div>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
        </el-card>
        <TemplateForm ref="templateForm" :codingTemplateId="codingTemplateId" @getList="getList"></TemplateForm>
    </div>
</template>
  
<script>
import {
    codingTemplateList,
    codingTemplateDelete
} from "@/api/source/index.js";
import {exportExcel} from '@/utils/east-mind.js'
import TemplateForm from './components/templateForm.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                templateName: "",
                templateType: "",
            },
            detailStatus: false,
            tableData: [],
            loading: true,
            total: 0,
            dataRow: {},
            codingTemplateId: '',
            acidTime: [],
            tabTypes: [
                { text: '产品码', value: 1 },
                { text: '箱码', value: 2 },
                { text: '溯源码', value: 3 },
                { text: '产品码 + 溯源码', value: 4 },
            ],
        };
    },
    components: {
        TemplateForm
    },
    computed: {
        handeltext(){
            return (list,value)=>{
                let name=''
                list.forEach(item=>{
                    if(item.value==value){
                        name=item.text
                    }
                })
                return name
            }
        },
    },
    created() {
        this.getList()
    },
    methods: {
        //列表查询
        getList() {
            codingTemplateList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list
                    this.total = Number(res.result.total);
                }
            })
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.acidTime = []
        },
        deleteTemplate(row) {
            this.$confirm('是否删除该模板？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                codingTemplateDelete({
                    codingTemplateId: row.codingTemplateId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.acidTime);
            this.getList();
        },
        addCutApart() {
            this.traceCodeApplyId = ''
            this.addCutApartModel = true
        },
        //编辑
        editTemplate(row){
            this.codingTemplateId = row.codingTemplateId
            this.$refs.templateForm.handleOpen()
        },
        addCode() {
            this.codingTemplateId = ''
            this.$refs.templateForm.handleOpen()
        }
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  