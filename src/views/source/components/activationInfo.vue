<template>
    <div class="app-container">
        <el-table
            :data="tableData"
            stripe
            style="width: 100%"
            v-loading="loading"
            border
        >
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="traceCode" align="center" label="溯源码"></el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    <span :class="{
                            orange: scope.row.traceCodeStatus == 0,
                            green: scope.row.traceCodeStatus == 1,
                        }">{{ scope.row.traceCodeStatus == 1 ? '已激活' : '未激活' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="activationTime" align="center" label="激活时间"></el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
    </div>
</template>
  
<script>
import {
    traceCodeList,
} from "@/api/source/index.js";
export default {
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
            detailStatus: false,
            tableData: [],
            loading: true,
            total: 0,
        };
    },
    components: {
    },
    props: {
        traceCodeApplyId: String
    },
    created() {
        this.getList()
    },
    methods: {
        //列表查询
        getList() {
            traceCodeList({
                ...this.queryParams,
                traceCodeApplyId: this.traceCodeApplyId
            }).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list
                    this.total = Number(res.result.total);
                }
            })
        },
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  