<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            class="dialog_box"
            width="30%"
            :modal='false'
            title="溯源码申请"
            :before-close="handleClose">
            <p class="source-title mt20">
                <i class="el-icon-warning"></i>
                <span>申请数量</span>
            </p>
            <el-form ref="form" :model="ruleForm" :rules="rules" label-width="30px"> 
                <el-form-item prop="applyNum">
                    <el-input 
                        maxlength="8"
                        placeholder="请输入申请数量"
                        v-model="ruleForm.applyNum"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" class="add_fill_btn" @click="handleSubmit">确 定</el-button>
            </span>
            </el-dialog>
    </div>
</template>

<script>
import {
    applyAdd,
} from '@/api/source/index.js'
export default {
    data() {
        return {
            ruleForm: {
                applyNum: '',
            },
            dialogVisible: false,
            rules: {
                applyNum: [
                    { required: true, message: '请填写申请数量', trigger: 'blur' },
                    {
                        pattern: /^\+?[1-9][0-9]*$/,
                        message: "请输入大于0的纯数字",
                        trigger: 'blur',
                    },
                ]
            }
        }
    },
    props: {
        inventoryCheckId: String,
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true,
            this.ruleForm.applyNum = ''
        },
        handleClose() {
            this.dialogVisible = false
            this.$refs.form.resetFields();
        },
        handleSubmit(){
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.checkData()
                }
            });
        },
        checkData() {
            applyAdd({
                applyNum: this.ruleForm.applyNum
            }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('申请成功')
                    this.$emit('getList')
                    this.handleClose()
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.source-title{
    font-size: 20px;
    font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
    font-weight: 500;
    color: #1D2129;
    span{
        margin-left: 10px
    }
    i {
        color: #F85300;
    }
}
:deep(.el-input__inner) {
    text-align: left !important;
}
</style>