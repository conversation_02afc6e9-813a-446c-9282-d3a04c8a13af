<template>
  <div style="padding-bottom: 80px" class="source_main">
        <el-form :model="ruleForm" ref="ruleForm" label-width="100px">
            <div class="source_title">
                <el-row class="name">
                    <el-col :span="12">
                        <el-form-item label="模版名称" prop="templateName">
                            <el-input v-model="ruleForm.templateName" maxlength="25" placeholder="请输入模板名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="btn">
                    <el-button class="add_btn" icon="el-icon-plus" size="small" @click="addItem">新增模块</el-button>
                </div>
            </div>
            <div class="source_content">
                <div class="item_box" v-for="(item, index) in dataList" :key="index">
                    <div class="item_title">
                        <div class="point_icon item_title_1" v-if="item.systemModule">
                            <label>{{item.moduleName}}</label>
                            <span @click="deleteItem(index)"><i class="el-icon-remove-outline"></i>移除</span>
                        </div>
                        <div class="point_icon item_title_2" v-else>
                            <el-form-item label="">
                                <el-input v-model="item.moduleName" 
                                class="title_input" maxlength="20"
                                placeholder="必填，请输入模块名称"></el-input>
                            </el-form-item>
                            <span><i class="el-icon-remove-outline" @click="deleteItem(index)"></i></span>
                        </div>
                    </div>
                    <el-descriptions class="mt20" :contentStyle='{width: "500px",height: "50px"}' :column="2" size="medium" border>
                        <el-descriptions-item v-for="(i, ind) in item.detailList" :key="ind">
                            <template slot="label">
                                <span v-if="i.systemField">{{i.paramKey}}</span>
                                <div v-else>
                                    <el-form-item >
                                        <el-input
                                            v-model="i.paramKey"
                                            placeholder="必填，请输入字段名称"
                                        ></el-input>
                                    </el-form-item>
                                </div>
                            </template>
                            
                            <div class="item" v-if="i.paramType == 2 && i.paramValue != '同步系统维护内容'">
                                <el-form-item>
                                    <el-input
                                    type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 8 }"
                                    v-model="i.paramValue"
                                    :placeholder="'必填，请输入' + i.paramKey "
                                    ></el-input>
                                </el-form-item>
                                <span><i class="el-icon-remove-outline" v-if="item.detailList.length > 2" @click="deleteRow(index, ind)"></i></span>
                            </div>
                            <div class="item" v-else-if="i.paramType == 3 && i.paramValue != '同步系统维护内容'">
                                <el-form-item>
                                    <!-- <div class="uploader_box">
                                        <div class="uploader3" v-if="i.value">
                                            <img src="../../../assets/images/daishenhe_icon.png" alt="">
                                        </div>
                                        <div class="uploader2" v-if="i.value">
                                            <i class="el-icon-view"></i>
                                            <span>预览</span>
                                        </div>
                                        <el-upload
                                        class="avatar-uploader"
                                        action="https://jsonplaceholder.typicode.com/posts/"
                                        :show-file-list="false"
                                        :on-success="handleAvatarSuccess">
                                            <div class="uploader1" v-if="i.value">
                                                <i class="el-icon-upload2"></i>
                                                <span>重新上传</span>
                                            </div>
                                            <div class="uploader" v-else>
                                                <i class="el-icon-upload2"></i>
                                                <span>上传图片</span>
                                            </div>
                                        </el-upload>
                                    </div> -->
                                    <uploadLogo v-model="i.paramValue"></uploadLogo>
                                </el-form-item>
                                <span><i class="el-icon-remove-outline remove" v-if="item.detailList.length > 2" @click="deleteRow(index, ind)"></i></span>
                            </div>
                            <div class="item" v-else>
                                <el-form-item>
                                    <el-input v-model="i.paramValue" :readonly="i.paramValue == '同步系统维护内容' ? true : false" :placeholder="i.paramKey ? '必填，请输入' + i.paramKey : '必填，请输入内容'"></el-input>
                                </el-form-item>
                                <span><i class="el-icon-remove-outline remove" v-if="item.detailList.length > 1" @click="deleteRow(index, ind)"></i></span>
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                    <div class="add_item_btn">
                        <el-button icon="el-icon-plus" class="delete_text_btn" size="mini" type="text" @click="addRow(index)">新增字段</el-button>
                        <el-button icon="el-icon-plus" class="delete_text_btn" size="mini" type="text" @click="addRow(index, 3)">新增图片</el-button>
                    </div>
                </div>
            </div>
            <el-form-item class="fcc footer_btn">
                <el-button type="primary" class="add_fill_btn" size="small" @click="getSystemInfo">恢复初始字段</el-button>
                <el-button type="primary" size="small" @click="submit">保 存</el-button>
                <el-button size="small" class="grey_fill_btn" @click="colse">取 消</el-button>
            </el-form-item>
        </el-form>
  </div>
</template>

<script>
import uploadLogo from '../../../components/FileUpload/uploadLogo.vue'
import {
    selectDefault,
    traceTemplateAdd,
    traceTemplateInfo,
    traceTemplateUpdate,
    selectSystem
} from "@/api/source/index";
export default {
    data() {
        return {
            ruleForm: {
                templateName: ''
            },
            rules: [],
            dataList: [],
            typeList: [
                { label: '文本', value: 1},
                { label: '文本域', value: 2},
                { label: '上传文件', value: 4},
                { label: '时间', value: 5},
                { label: '时间范围', value: 6},
                { label: '多图片', value: 7},
                { label: '富文本', value: 8},
            ]
        }
    },
    props: {
        traceTemplateId: String
    },
    watch: {
        traceTemplateId() {
            if (this.traceTemplateId) {
                this.getInfo()
            } else {
                this.getDefaultData()
            }
        }
    },
    components: {
        uploadLogo
    },
    created() {
        if (this.traceTemplateId) {
            this.getInfo()
        } else {
            this.getDefaultData()
        }
    },
    methods: {
        getDefaultData() {
            selectDefault({}).then(res => {
                const data= res.result
                this.ruleForm = {
                    templateName: ''
                }
                this.dataList = data.moduleList
            })
        },
        getSystemInfo() {
            selectSystem({}).then(res => {
                const data= res.result
                this.ruleForm = {
                    templateName: ''
                }
                this.dataList = data.moduleList
            })
        },
        getInfo() {
            traceTemplateInfo({
                traceTemplateId: this.traceTemplateId
            }).then(res => {
                const data= res.result
                this.ruleForm = {
                    templateName: data.templateName
                }
                this.dataList = data.moduleList
            })
        },
        handleAvatarSuccess() {

        },
        deleteRow(index, ind) {
            const dataList = JSON.parse(JSON.stringify(this.dataList))
            const item = dataList[index]
            item.detailList.splice(ind, 1);
            dataList.splice(index, 1, item);
            this.dataList = dataList
        },
        addRow(index, paramType) {
            const dataList = JSON.parse(JSON.stringify(this.dataList))
            const item = dataList[index]
            item.detailList.push( {
                paramType: paramType || 1, //字段类型（1文本 2文本域 3上传文件 4时间 5时间范围 6多图片 7富文本）
                paramKey: '', //字段名称
                paramValue: '', //字段值
                systemField: 0 //是否是系统字段（1是 0否）
            });
            this.dataList = dataList
        },
        addItem(index) {
            const dataList = JSON.parse(JSON.stringify(this.dataList))
            dataList.push({
                moduleName: '', //模块名称
                systemModule: 0, //是否是系统模块（1是 0否）
                traceTemplateId: this.traceTemplateId,
                detailList: [
                    {
                        paramType: 1, //字段类型（1文本 2文本域 3上传文件 4时间 5时间范围 6多图片 7富文本）
                        paramKey: '', //字段名称
                        paramValue: '', //字段值
                        systemField: 0 //是否是系统字段（1是 0否）
                    }
                ]
            });
            this.dataList = dataList
        },
        deleteItem(index) {
            const dataList = JSON.parse(JSON.stringify(this.dataList))
            dataList.splice(index, 1);
            this.dataList = dataList
        },
        submit() {
            if (this.traceTemplateId) {
                traceTemplateUpdate({
                    templateName: this.ruleForm.templateName,
                    moduleList: this.dataList,
                    traceTemplateId: this.traceTemplateId
                }).then((res) => {
                    this.$message.success('编辑成功')
                    this.$emit('getList')
                })
            } else {
                const dataList = []
                this.dataList.forEach(item => {
                    let obj = {
                        moduleName: item.moduleName, //模块名称
                        systemModule: item.systemModule, //是否是系统模块（1是 0否）
                        moduleCode: item.moduleCode,
                        detailList: []
                    }
                    item.detailList.forEach(i => {
                        obj.detailList.push({
                            paramType: i.paramType, //字段类型（1文本 2文本域 3上传文件 4时间 5时间范围 6多图片 7富文本）
                            paramKey: i.paramKey, //字段名称
                            paramValue: i.paramValue, //字段值
                            systemField: i.systemField, //是否是系统字段（1是 0否）
                            keyCode: i.keyCode 
                        })
                    })
                    dataList.push(obj)
                })
                traceTemplateAdd({
                    templateName: this.ruleForm.templateName,
                    moduleList: dataList
                }).then((res) => {
                    this.$message.success('新增成功')
                    this.$emit('getList')
                })
            }
        },
        colse() {
            this.$emit('colse')
        }
    }
}
</script>

<style lang="scss" scoped>
:deep(.el-drawer__body){
    padding-left: 0;
}
.source_title{
    position: fixed;
    width: 90%;
    top: 60px;
    right: 0;
    background: #fff;
    z-index: 299;
}
.name{
    height: 70px;
    display: flex;
    align-items: center;
    border-bottom: 10px solid #F5F7FA;
    .el-form-item{
        margin-bottom: 0;
    }
}
.btn{
    padding-left: 30px;
    margin-top: 20px;
    margin-bottom: 30px;
}
.source_content{
    margin-top: 158px;
    padding-left: 56px;
    padding-right: 20px;
    .item_title{
        font-size: 16px;
        line-height: 20px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: 700;
        color: #1F2026;
        .item_title_1{
            display: flex;
            align-items: center;
            min-width: 320px;
            span{
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #F85300;
                margin-left: 21px;
                cursor: pointer;
                i{
                    margin-right: 5px;
                }
            }
        }
        .item_title_2{
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            padding: 0 10px 0 15px;
            i{
                color: #F85300;
                cursor: pointer;
            }
            &::before{
                top: 13px
            }
        }
    }
    .item_box{
        .item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            i{
                color: #F85300;
                cursor: pointer;
            }
            .uploader_box{
                display: flex;
                align-items: center;
            }
            .uploader{
                width: 74px;
                height: 74px;
                background: #FFFFFF;
                border-radius: 2px 2px 2px 2px;
                opacity: 1;
                border: 1px solid #EEEEEE;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                font-size: 14px;
                font-family: Source Han Sans CN-Normal, Source Han Sans CN;
                color: #BBBBBB;
                line-height: 14px;
                i{
                    font-size: 20px;
                    color: #BBBBBB;
                    margin-bottom: 9px;
                }
            }
            .uploader1{
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #5672FA;
                line-height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                i{
                    font-size: 20px;
                    color: #5672FA;
                    margin-right: 5px;
                }
            }
            .uploader2{
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #12AE63;
                line-height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20px;
                cursor: pointer;
                i{
                    font-size: 20px;
                    color: #12AE63;
                    margin-right: 5px;
                }
            }
            .uploader3{
                width: 74px;
                height: 74px;
                background: #FFFFFF;
                border-radius: 2px 2px 2px 2px;
                margin-right: 23px;
                img{
                    width: 74px;
                    height: 74px;
                }
            }
        }
        .add_item_btn{
            margin-top: 20px;
        }
    }
}
:deep(.el-descriptions-row .el-descriptions-item__label) {
    width: 300px;
}
.footer_btn{
    width: 90%;
}
</style>