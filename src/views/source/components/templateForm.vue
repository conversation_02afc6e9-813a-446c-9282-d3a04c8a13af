<template>
    <div style="padding-bottom: 60px">
        <el-dialog
            :visible.sync="dialogVisible"
            class="dialog_box"
            width="30%"
            :modal='false'
            title="新建模版"
            :before-close="handleClose">
            <el-form ref="form" :model="ruleForm" :rules="rules" label-width="110px" class="mt20">
                <el-form-item prop="tenantId" label="企业名称">
                    <el-select v-model="ruleForm.tenantId"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入企业名称搜索"
                        :remote-method="remoteMethod"
                        style="width: 100%">
						<el-option
							v-for="(item,index) in options"
							:label="item.companyName"
							:value="item.tenantId"
							:key="index"
						/>
						</el-select>
                </el-form-item>
                <el-form-item prop="templateName" label="模版名称">
                    <el-input
                        maxlength="8"
                        placeholder="请输入模版名称"
                        v-model="ruleForm.templateName"></el-input>
                </el-form-item>
                <el-form-item prop="templateType" label="标签类型">
                    <el-select v-model="ruleForm.templateType" style="width: 100%">
						<el-option
							v-for="(item,index) in tabTypes"
							:label="item.text"
							:value="item.value"
							:key="index"
						/>
						</el-select>
                </el-form-item>
                <el-form-item prop="templatePic" label="预览图片">
                    <uploadLogo v-model="ruleForm.templatePic"></uploadLogo>
                </el-form-item>
                <el-form-item prop="templateFile" label="上传附件">
                    <file-upload
                        :fileType="['frx']"
                        :fileSize="100"
                        :limit="1"
                        v-model="ruleForm.templateFile"
                    >
                        <el-button type="primary" plain size="mini" class="lobtb">
                            <i class="el-icon-upload el-icon--right"></i>
                            上传文件
                        </el-button>
                    </file-upload>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" class="add_fill_btn" @click="handleSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    codingTemplateAdd,
    codingTemplateUpdate,
    codingTemplateInfo,
    enterpriseList
} from '@/api/source/index.js'
import uploadLogo from '../../../components/FileUpload/uploadLogo.vue'
export default {
    data() {
        return {
            ruleForm: {
                templateName: '',
                templateType: '',
                templateFile: '',
                templatePic: '',
                tenantId: ''
            },
            dialogVisible: false,
            rules: {
                tenantId: [
                    { required: true, message: '请填写企业名称', trigger: 'change' },
                ],
                templateName: [
                    { required: true, message: '请填写模版名称', trigger: 'blur' },
                ],
                templateType: [
                    { required: true, message: '请选择标签类型', trigger: 'change' },
                ],
                // templatePic: [
                //     { required: true, message: '请上传模版预览', trigger: 'change' },
                // ],
                // templateFile: [
                //     { required: true, message: '请上传模版文件', trigger: 'change' },
                // ]
            },
            tabTypes: [
                { text: '产品码', value: 1 },
                { text: '箱码', value: 2 },
                { text: '溯源码', value: 3 },
                { text: '产品码 + 溯源码', value: 4 },
            ],
            options: []
        }
    },
    components: {
        uploadLogo
    },
    props: {
        codingTemplateId: String,
    },
    watch: {
        codingTemplateId() {
            if (this.codingTemplateId) {
                this.getInfo()
            }
        }
    },
    created() {
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.codingTemplateId) {
                    this.getInfo()
                }
            })
        },
        handleClose() {
            this.ruleForm = {
                templateName: '',
                templateType: '',
                templatePic: '',
                templateFile: '',
                tenantId: ''
            }
            this.dialogVisible = false
            this.$refs.form.resetFields();
        },
        getInfo() {
            codingTemplateInfo({
                codingTemplateId: this.codingTemplateId
            }).then(res => {
                const data = res.result
                this.ruleForm = {
                    templateName: data.templateName,
                    templateType: data.templateType,
                    templateFile: data.templateFile,
                    templatePic: data.templatePic,
                    tenantId: data.tenantId
                }
                this.remoteMethod(data.companyName)
            })
        },
        handleSubmit(){
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (this.codingTemplateId) {
                        this.checkEditData()
                    } else {
                        this.checkAddData()
                    }
                }
            });
        },
        checkAddData() {
            codingTemplateAdd({
                ...this.ruleForm
            }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('打码模版创建成功')
                    this.handleClose()
                    this.$emit('getList')
                }
            })
        },
        checkEditData() {
            codingTemplateUpdate({
                ...this.ruleForm,
                codingTemplateId: this.codingTemplateId
            }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('打码模版编辑成功')
                    this.handleClose()
                    this.$emit('getList')
                }
            })
        },

        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                enterpriseList({
                    pageNum: 1,
                    pageSize: 1000,
                    companyType: 2,
                    'companyTypeList[0]': 2,
                    companyName: query
                }).then(res => {
                    this.options = res.rows
                })
            } else {
                this.options = [];
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.title{
    font-size: 20px;
    font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
    font-weight: 500;
    color: #1D2129;
    span{
        margin-left: 10px
    }
    i {
        color: #F85300;
    }
}
</style>