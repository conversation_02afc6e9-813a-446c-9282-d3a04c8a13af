<template>
    <div class="app-container">
        <el-table
            :data="tableData"
            stripe
            style="width: 100%"
            v-loading="loading"
            border
        >
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="traceCode" align="center" label="溯源码"></el-table-column>
            <el-table-column prop="productCode" align="center" label="产品编码"></el-table-column>
            <el-table-column prop="productName" align="center" label="产品名称"></el-table-column>
            <el-table-column prop="updateUserName" align="center" label="激活人"></el-table-column>
            <el-table-column prop="updateTime" align="center" label="激活时间"></el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
    </div>
</template>
  
<script>
import {
    pageTraceCodeList,
} from "@/api/source/index.js";
export default {
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
            detailStatus: false,
            tableData: [],
            loading: true,
            total: 0,
        };
    },
    components: {
    },
    props: {
        codingTraceId: String
    },
    created() {
        this.getList()
    },
    methods: {
        //列表查询
        getList() {
            pageTraceCodeList({
                ...this.queryParams,
                codingTraceId: this.codingTraceId
            }).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list
                    this.total = Number(res.result.total);
                }
            })
        },
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  