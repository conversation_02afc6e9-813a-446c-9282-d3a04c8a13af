<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            class="dialog_box"
            width="30%"
            :modal='false'
            title="溯源码生成"
            :before-close="handleClose">
            <p class="source-title mt20">
                <i class="el-icon-warning"></i>
                <span>申请数量</span>
            </p>
            <el-form ref="form" :model="ruleForm" :rules="rules" label-width="30px"> 
                <el-form-item prop="actualNum">
                    <el-input
                        maxlength="8"
                        placeholder="请输入生成数量"
                        v-model="ruleForm.actualNum"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" class="add_fill_btn" @click="handleSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    traceCodeCreate,
} from '@/api/source/index.js'
export default {
    data() {
        return {
            ruleForm: {
                actualNum: '',
            },
            dialogVisible: false,
            rules: {
                actualNum: [
                    { required: true, message: '请填写生成数量', trigger: 'blur' },
                    {
                        pattern: /^\+?[1-9][0-9]*$/,
                        message: "请输入大于0的纯数字",
                        trigger: 'blur',
                    },
                ]
            }
        }
    },
    props: {
        traceCodeApplyId: String,
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true,
            this.ruleForm.actualNum = ''
        },
        handleClose() {
            this.dialogVisible = false
            this.$refs.form.resetFields();
        },
        handleSubmit(){
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.checkData()
                }
            });
        },
        checkData() {
            this.$modal.loading("正在生成溯源码，请稍候...");
            traceCodeCreate({
                actualNum: this.ruleForm.actualNum,
                traceCodeApplyId: this.traceCodeApplyId
            }).then((res) => {
                this.$modal.closeLoading();
                if (res.code == 200) {
                    this.$message.success('溯源码生成成功')
                    this.handleClose()
                    this.$emit('getList')
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.source-title{
    font-size: 20px;
    font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
    font-weight: 500;
    color: #1D2129;
    span{
        margin-left: 10px
    }
    i {
        color: #F85300;
    }
}
</style>