<template>
  <div style="padding-bottom: 80px" class="source_mains">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-row>
                <el-col :span="12" class="mt20">
                    <el-form-item label="溯源模板" prop="templateName">
                        <el-input v-model="ruleForm.templateName" maxlength="25" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <div v-for="(item, index) in moduleList" :key="index">
                <div class="fc">
                    <span class="point_icon">{{ item.moduleName }}</span>
                </div>
                <el-descriptions class="mt20" :contentStyle='{width: "500px",height: "50px"}' :column="2" size="medium" border>
                    <el-descriptions-item  v-for="(i, ind) in item.detailList" :key="ind" :label="i.paramKey">
                        <div v-if="i.paramType == 1">
                            <el-form-item >
                                <el-input v-model="i.paramValue" maxlength="25" placeholder="请输入"></el-input>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 2">
                            <el-form-item >
                                <el-input
                                type="textarea"
                                v-model="i.paramValue"
                                :autosize="{ minRows: 3, maxRows: 6 }"
                                :placeholder="'必填，请输入'"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 3">
                            <el-form-item >
                                <uploadLogo v-model="i.paramValue"></uploadLogo>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 4">
                            <el-form-item >
                                <el-date-picker
                                    style="width: 100%"
                                    v-model="i.paramValue"
                                    type="date"
                                    value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 5">
                            <el-form-item >
                                <el-date-picker
                                    style="width: 100%"
                                    v-model="i.paramValue"
                                    type="date"
                                    value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 6">
                            <el-form-item >
                                <uploadCard v-model="i.paramValue"
                                    :fileType="['png', 'jpg', 'jpeg']"
                                    :fileSize="10"
                                    :limit="10"
                                    :isShowTip="true">
                                </uploadCard>
                            </el-form-item>
                        </div>
                        <div v-if="i.paramType == 7">
                            <el-form-item >
                                <editor v-model="i.paramValue" :min-height="102"/>
                            </el-form-item>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
                </div>
            <el-form-item class="fcc footer_btn">
                <el-button type="primary" size="small" @click="submitForm('ruleForm')">提 交</el-button>
                <el-button size="small" class="grey_fill_btn" @click="colse()">取 消</el-button>
            </el-form-item>
        </el-form>
  </div>
</template>

<script>
import {
    codingTraceInfo,
    batchUpdate
} from "@/api/source/index.js";
import uploadLogo from '../../../components/FileUpload/uploadLogo.vue'
import uploadCard from '../../../components/FileUpload/uploadCard.vue'

export default {
    data() {
        return {
            systemCode: true,
            loading: false,
            ruleForm: {
                templateName: ''
            },
            rules: {
                templateName: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                ],
            },
            moduleList: []
        }
    },
    props: {
        codingTraceRelationIds: Array,
        codingTraceId: String
    },
    components: {
        uploadLogo,
        uploadCard
    },
    watch: {
        codingTraceId() {
            this.getInfo()
        }
    },
    created() {
        this.getInfo()
    },
    methods: {
        getInfo() {
            codingTraceInfo({
                codingTraceId: this.codingTraceId
            }).then(res => {
                this.moduleList = res.result.moduleList;
                this.ruleForm = {
                    templateName: res.result.templateName
                }
            })
        },
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    const moduleList = []
                    this.moduleList.forEach(item => {
                        let obj = {
                            moduleName: item.moduleName, //模块名称
                            systemModule: item.systemModule, //是否是系统模块（1是 0否）
                            moduleCode: item.moduleCode,
                            detailList: []
                        }
                        item.detailList.forEach(i => {
                            obj.detailList.push({
                                paramType: i.paramType, //字段类型（1文本 2文本域 3上传文件 4时间 5时间范围 6多图片 7富文本）
                                paramKey: i.paramKey, //字段名称
                                paramValue: i.paramValue, //字段值
                                keyCode: i.keyCode, //字段值
                                systemField: i.systemField //是否是系统字段（1是 0否）
                            })
                        })
                        moduleList.push(obj)
                    })
                    batchUpdate({
                        codingTraceIdList: this.codingTraceRelationIds,
                        ...this.ruleForm,
                        moduleList: moduleList
                    }).then(res => {
                        this.$message.success('编辑成功')
                        this.$emit('getList')
                        this.resetForm('ruleForm')
                    })
                }
            });
        },
        resetForm() {
            this.ruleForm = {
                templateName: '',
            }
            this.moduleList = []
        },
        colse() {
            this.$emit('colse')
        }
    }
}
</script>

<style lang="scss" scoped>
.el-col-8{
    padding-right: 20px;
}
.uploader_box{
    display: flex;
    align-items: center;
}
.uploader{
    width: 74px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    border: 1px solid #EEEEEE;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    color: #BBBBBB;
    line-height: 14px;
    i{
        font-size: 20px;
        color: #BBBBBB;
        margin-bottom: 9px;
    }
}
.uploader1{
    font-size: 12px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #5672FA;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    i{
        font-size: 20px;
        color: #5672FA;
        margin-right: 5px;
    }
}
.uploader2{
    font-size: 12px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #12AE63;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    i{
        font-size: 20px;
        color: #12AE63;
        margin-right: 5px;
    }
}
.uploader3{
    width: 74px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 2px 2px 2px 2px;
    margin-right: 23px;
    img{
        width: 74px;
        height: 74px;
    }
}
.footer_btn{
    width: 90%;
}
:deep(.el-descriptions-row .el-descriptions-item__label) {
    width: 300px;
}
</style>