<template>
    <div class="app-container">
        <el-card shadow="never" class="mb10 box-card form-card">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box">
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item prop="templateName">
                            <el-input
                            v-model="queryParams.templateName"
                            placeholder="模版名称"
                            clearable
                            />
                        </el-form-item>
                        <el-form-item prop="acidTime">
                            <el-date-picker
                                v-model="acidTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="创建开始日期"
                                end-placeholder="创建结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
        <el-row :gutter="10" class="mb8 form_btn">
            <el-col class="fend">
                <el-button size="mini" class="default_btn" icon="el-icon-plus" @click="addForm">新增</el-button>
            </el-col>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
            <el-table
                :data="tableData"
                stripe
                style="width: 100%" border
                v-loading="loading" :max-height="tableHeight"
            >
                <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
                <el-table-column prop="item.prop" show-overflow-tooltip  label="模版名称">
                    <template slot-scope="scope">
                        <span>{{ scope.row.templateName }}</span>
                        <label v-if="scope.row.defaultFlag" class="tip">默认标签</label>
                    </template>
                </el-table-column>
                <el-table-column   show-overflow-tooltip  v-for="(item, index) in tableColumn" :key="index" :prop="item.prop" :sortable="item.sortable" :align="item.align" :label="item.label" :min-width="item.width"></el-table-column>
                <el-table-column label="操作" fixed="right" align="center">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-edit" class="edit_text_btn" size="mini" v-if="!scope.row.systemFlag" @click="editForm(scope.row)" type="text" >编辑</el-button>
                        <el-button icon="el-icon-delete" class="delete_text_btn" size="mini" v-if="!scope.row.systemFlag" @click="deleteCutApart(scope.row)" type="text" >删除</el-button>
                        <el-button icon="el-icon-setting" class="text_btn" size="mini" v-if="!scope.row.defaultFlag" @click="setDefault(scope.row)" type="text" >设为默认</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
        </el-card>
        <el-drawer
            class="drawer_box drawer_box_1"
            :title='dataRow.traceTemplateId ? "编辑模版" : "新建模板"'
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="90%"
            :wrapperClosable="false">
            <Form @getList="colseCutApart" @colse="colseModel" :traceTemplateId="dataRow.traceTemplateId"></Form>
        </el-drawer>
    </div>
</template>
  
<script>
import {
    traceTemplateList,
    traceTemplateDelete,
    traceTemplateSetDefault,
} from "@/api/source/index";
import Form from './components/form.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                templateName: "",
            },
            detailStatus: false,
            tableColumn: [
                { check: true, prop: "traceCodeNum", label: "绑码量", align: 'right' },
                { check: true, prop: "updateUserName", label: "创建人", align: 'center' },
                { check: true, prop: "updateTime", label: "创建时间", align: 'center', sortable: true },
            ],
            tableData: [],
            acidTime: [],
            loading: true,
            total: 0,
            dataRow: {},
        };
    },
    components: {
        Form
    },
    created() {
        this.getList()
    },
    methods: {
        //列表查询
        getList() {
            traceTemplateList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                }
            })
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.acidTime = []
        },
        handleDetails(row) {
            this.acidTaskId = row.acidTaskId
            this.detailStatus = true
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.acidTime);
            this.getList();
        },
        addForm() {
            this.dataRow = {}
            this.detailStatus = true
        },
        //编辑
        editForm(row) {
            this.dataRow = row
            this.detailStatus = true
        },
        //关闭
        colseCutApart() {
            this.detailStatus = false;
            this.resetQuery()
        },
        deleteCutApart(row) {
            this.$confirm('删除模板有可能会导致部分溯源码显示内容为空，是否确定删除？', '操作确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                traceTemplateDelete({
                    traceTemplateId: row.traceTemplateId
                }).then(res => {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                })
            }).catch(() => {        
            });
        },
        setDefault(row) {
            traceTemplateSetDefault({
                traceTemplateId: row.traceTemplateId
            }).then(res => {
                this.getList()
                this.$message({
                    type: 'success',
                    message: '设置成功!'
                });
            })
        },
        colseModel() {
            this.detailStatus = false;
        },
    }, 
};
</script>
  
<style lang="scss" scoped>
.tip{
    font-size: 12px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 350;
    color: #F85300;
    line-height: 12px;
    background: #FFE2D3;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    border: 1px solid #F85300;
    padding: 1px 5px;
    margin-left: 6px;
}
</style>
  