<template>
    <div class="app-container">
        <el-card class="box-card form-card mb10" shadow="never">
            <el-row :gutter="10">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
                    <el-row class=" form_row">
                        <el-row class="form_col">
                            <el-form-item prop="inventoryCheckName">
                                <el-input v-model="queryParams.inventoryCheckName" placeholder="任务名称" clearable />
                            </el-form-item>
                            <el-form-item prop="inventoryCheckCode">
                                <el-input v-model="queryParams.inventoryCheckCode" placeholder="盘点单号" clearable />
                            </el-form-item>
                            <el-form-item  prop="warehouseId">
                                <el-select v-model="queryParams.warehouseId" placeholder='盘点仓库' clearable>
                                <el-option
                                    v-for="(item,index) in warehouseList"
                                    :label="item.warehouseName"
                                    :value="item.warehouseId"
                                    :key="index"
                                />
                                </el-select>
                            </el-form-item>
                            <el-form-item  prop="inventoryCheckStatus">
                                <el-select v-model="queryParams.inventoryCheckStatus" placeholder='盘点状态' clearable>
                                <el-option
                                    v-for="(item,index) in inventoryCheckStatusList"
                                    :label="item.label"
                                    :value="item.value"
                                    :key="index"
                                />
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                v-model="dateEnter"
                                style="width: 240px"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="盘点开始日期"
                                end-placeholder="盘点结束日期"
                                ></el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </el-row>
        </el-card>
        
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="fend  mb8 form_btn">
                <el-button class="default_btn" icon="el-icon-plus" size="mini" @click="addForm">新建</el-button>
                <el-button class="default_btn" icon="el-icon-download" size="mini" @click="exportList">导出数据</el-button>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table :data="tableData" stripe style="width: 100%" border :max-height="tableHeight" v-loading="loading">
                    <el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="inventoryCheckName" min-width="180" label="盘点任务名称"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="inventoryCheckCode" min-width="140" label="盘点单号"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="warehouseName"  min-width="140" label="盘点仓库"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="inventoryCheckStatus" min-width='140' label="盘点状态" align="center">
                        <template slot-scope="scope"><span :class="{
                            orange: scope.row.inventoryCheckStatus == 2 || scope.row.inventoryCheckStatus == 3,
                            blue: scope.row.inventoryCheckStatus == 4,
                            green: scope.row.inventoryCheckStatus == 1,
                            red: scope.row.inventoryCheckStatus == 5,
                            grey: scope.row.inventoryCheckStatus == 0,
                        }">{{ handeltext(scope.row.inventoryCheckStatus) }}</span></template>
                    </el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="updateTime" min-width="140" sortable label="盘点日期"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="managerUserName" min-width='120' label="盘点人"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="createTime" min-width="160" sortable label="创建日期"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" label="操作" width='280' fixed="right">
                        <template slot-scope="scope">
                            <el-button class="text_btn" icon="el-icon-warning-outline" size="mini" type="text" @click="detail(scope.row)">查看</el-button>
                            <el-button icon="el-icon-edit" class="edit_text_btn" size="mini" type="text" @click="editForm(scope.row)" v-if="scope.row.inventoryCheckStatus == 2">编辑</el-button>
                            <el-button icon="el-icon-s-check" class="check_text_btn" size="mini" type="text" @click="checkForm(scope.row)" v-if="scope.row.inventoryCheckStatus == 4">审核</el-button>
                            <el-button icon="el-icon-refresh-left" class="edit_text_btn" size="mini" type="text" @click="editForm(scope.row)" v-if="scope.row.inventoryCheckStatus == 5">重盘</el-button>
                            <el-button icon="el-icon-circle-close" class="delete_text_btn" size="mini" type="text" @click="closeItem(scope.row)" v-if="scope.row.inventoryCheckStatus == 2 || scope.row.inventoryCheckStatus == 3">关闭</el-button>
                            <el-button icon="el-icon-video-play" class="edit_text_btn" size="mini" type="text" @click="openItem(scope.row)" v-if="scope.row.inventoryCheckStatus == 0">启用</el-button>
                            <el-button icon="el-icon-delete" class="delete_text_btn" size="mini" type="text" @click="deleteItem(scope.row)" v-if="scope.row.inventoryCheckStatus == 0">删除</el-button>
                            <el-dropdown @command="handleCommand" ref="dropdown" :dateRow="scope.row" v-if="scope.row.inventoryCheckStatus == 2" >
                                <span class="more-icon">
                                    <i class="el-icon-more"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item :command="{
                                        type: 'delete',
                                        row: scope.row
                                    }">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
		<el-drawer
			class="drawer_box"
			:visible.sync="formModelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="60%"
			:title='inventoryCheckId ? "编辑盘点" : "新建盘点"'
			:wrapperClosable="false">
			<formInventory ref="formInventory" :inventoryCheckId='inventoryCheckId' @close='closeFormModel'></formInventory>
		</el-drawer>
		<el-drawer
			class="drawer_box"
			:visible.sync="detailModelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			:title='isCheck ? "盘点审核" : "盘点详情"'
			:wrapperClosable="false">
            <InventoryDetail :isCheck='isCheck' @getList="refresh" :inventoryCheckId='inventoryCheckId' @edit='editForm' @checkForm='checkForm'></InventoryDetail>
		</el-drawer>
    </div>
</template>
      
      <script>
import {
    inventoryCheckPage,
    inventoryCheckExport,
    inventoryCheckClose,
    inventoryCheckOpen,
    inventoryCheckDelete,
} from "@/api/stock.js";
import { warehouseList } from "@/api/basics/index.js";
import { exportExcel } from "@/utils/east";
import formInventory from './components/form_inventory.vue'
import InventoryDetail from './components/inventoryDetail.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1, //页数
                pageSize: 10, //行数
                inventoryCheckName: '', //盘点任务名称
                inventoryCheckCode: '', //盘点任务编号
                inventoryCheckStatus: '', //盘点任务状态：0已关闭 1审核通过 2待盘点 3待审核 4审核驳回
                warehouseId: '', //仓库ID
            },
            warehouseList: [], //仓库列表
            inventoryCheckStatusList: [
                { label: '待盘点', value: '2' },
                { label: '待审核', value: '4' },
                { label: '审核通过', value: '1' },
                { label: '进行中', value: '3' },
                { label: '审核不通过', value: '5' },
                { label: '已关闭', value: '0' },
            ], //产品类型
            tableData: [],
            loading: true,
            dateEnter: [],
            total: 0,
            currentItem: {},
            formModelShow: false,
            detailModelShow: false,
            inventoryCheckId: '',
            isCheck: false,
            sreachShow: false,
        };
    },
    components: {
        formInventory,
        InventoryDetail
    },
    computed: {
        handeltext(){
            return (value)=>{
                let name=''
                this.inventoryCheckStatusList.forEach(item=>{
                    if(item.value==value){
                        name=item.label
                    }
                })
                return name
            }
        },
    },
    created() {
		this.queryParams.requestRole = this.$route.query.requestRole
        this.getSelectList();
        this.getList();
    },
    methods: {
        handleCommand(data) {
            if (data.type == 'delete') {
                this.deleteItem(data.row)
            }
        },
        refresh() {
            this.getList();
        },
        getSelectList() {
            warehouseList({
                requestRole: this.$route.query.requestRole,
                pageNum: 1,
                pageSize: 10000,
			    warehouseType: -1,
            }).then((res) => {
                if (res.code == 200) {
                    this.warehouseList = res.result.list || [];
                }
            });
        },
        //列表查询
        getList() {
            inventoryCheckPage(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset(){
            this.resetForm("queryForm");
        },
        //重置
        resetQuery() {
            this.dateEnter = [];
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.dateEnter);
            this.getList();
        },
        addForm() {
            this.inventoryCheckId = ''
            this.formModelShow = true
        },
        editForm(row) {
            this.inventoryCheckId = row.inventoryCheckId
            this.formModelShow = true
            this.detailModelShow = false
        },
        checkForm(row) {
            this.inventoryCheckId = row.inventoryCheckId
            this.isCheck = true
            this.detailModelShow = true;
        },
        closeFormModel() {
            this.formModelShow = false
            this.refreshList()
        },
        detail(row) {
            this.inventoryCheckId = row.inventoryCheckId
            this.isCheck = false
            this.detailModelShow = true;
        },
        openItem(row) {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定启用盘点任务？</div><div style='padding-left:22px'>启用后系统刷新当前任务，盘点人员重新执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckOpen({
                    inventoryCheckId: row.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getList()
                        this.$message.success('操作成功')
                    }
                })
            });
        },
        deleteItem(row) {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定删除盘点任务？</div><div style='padding-left:22px'>删除后盘点人员不可再执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckDelete({
                    inventoryCheckId: row.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getList()
                        this.$message.success('操作成功')
                    }
                })
            });
        },
        closeItem(row) {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定关闭盘点任务？</div><div style='padding-left:22px'>关闭后盘点人员不可再执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckClose({
                    inventoryCheckId: row.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getList()
                        this.$message.success('操作成功')
                    }
                })           

            });
        },
        exportList(){exportExcel(inventoryCheckExport,this.queryParams,'盘点列表')},
    },
};
</script>
      
<style lang="scss" scoped>
</style>
      