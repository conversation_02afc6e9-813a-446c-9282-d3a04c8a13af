<template>
    <div class="main">
        <div style="display:flex; margin-bottom: 10px">
            <ProductList @selectList='getSelectList' :warehouseInfo='warehouseInfo' :defaultData='tableData'></ProductList>
            <el-button style="margin-left: 10px" class="delete_btn" size="mini" @click="removeData">删除</el-button>
        </div>
      
        <el-table :data="tableData" :show-summary="true"
                    max-height="550" border
                    @select-all="handleSelectionChange">
            <el-table-column type="selection" align="center" width="80">
                <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.check"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="name" align="center" label="内部产品编码">
                <template slot-scope="scope">
                    <span>{{ scope.row.productCode }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称"></el-table-column>
            <el-table-column prop="weightingTypeName" align="center" label="称重类型"></el-table-column>
            <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
            <el-table-column prop="inventoryNum" align="right" label="库存数量"></el-table-column>
            <el-table-column prop="productNum" align="center" :sum-text="'合计'">
                <template slot="header">
                    <span>
                        计划出库数量
                        <span style="color:red">*</span>
                    </span>
                </template>
                <template slot-scope="scope">
                    <el-input v-model="scope.row.productNum" type="number" :min="1" :max="scope.row.inventoryNum" maxlength="5" placeholder="请输入" @input='changeNum(scope.row, scope.$index)'></el-input>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="name" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" placeholder="请输入内容"></el-input>
                </template>
            </el-table-column> -->
        </el-table>
    </div>
</template>

<script>
import ProductList from './productList.vue'
export default {
    data() {
        return {
            tableData: [],
            flag: false
        }
    },
    components: {
        ProductList
    },
    props: {
        productList: Array,
        warehouseInfo: Object
    },
    watch: {
        productList() {
            if (this.productList && this.productList.length > 0) {
                this.getSelectList(this.productList)
            }
        },
        warehouseInfo() {
            this.tableData = []
        }
    },
    created() {
        if (this.productList && this.productList.length > 0) {
            this.getSelectList(this.productList)
        }
    },
    methods: {
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        getSelectList(data) {
            const data1 = JSON.parse(JSON.stringify(data))
            data1.forEach(item => {
                item.note = item.note
                item.check = false
                this.tableData.push(item)
            })
        },
        changeNum(item, index) {
            if (item.weightingType == 2) {
                item.productWeight = ((item.unitWeight * item.productNum).toFixed(2) * 1)
                item.productWeightEnd = ((item.unitWeightEnd * item.productNum).toFixed(2) * 1)
            } else {
                item.productWeight = (item.unitWeight * item.productNum).toFixed(2) * 1
            }
            this.tableData.splice(index, 1, item);
        },
        removeData() {
            const indexs = []
            const tableData = []
            this.tableData.forEach((item, index) => {
                if(item.check) {
                    indexs.push(index)
                } else {
                    tableData.push(item)
                }
            })



            if(indexs.length > 0) {
                this.$confirm('点击后列表删除已选内容', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData = tableData
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
            } else {
                this.$message.info('请先勾选列表中想要删除的数据')
            }
        }
    }
}
</script>

<style>

</style>