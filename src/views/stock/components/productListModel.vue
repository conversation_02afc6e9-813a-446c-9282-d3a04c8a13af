<template>
    <div class="dialog_box">
        <el-button size="mini" class="text_btn border_theme" @click="showModel">编辑盘点产品</el-button>
        <el-dialog
            title="选择产品"
            :visible.sync="dialogVisible"
            width="1200px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <div v-if="selectList.length > 0" class="select_box border_theme mt20">
                    <el-tag
                        v-for="(tag, index) in selectList"
                        :key="tag.productName"
                        @close='deleteItem(tag, index)'
                        closable>
                        {{tag.productName}}
                    </el-tag>
                </div>
                <el-form ref="ruleForm" label-width="80px" class="mt20">
                    <el-row>
                        <el-col :span="4">
                            <el-form-item label="产品编码" prop="productCode">
                                <el-input size="mini" v-model="ruleForm.productCode"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="产品名称" prop="productName">
                                <el-input size="mini" v-model="ruleForm.productName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="产品类型" prop="productTypeId">
                                <el-select size="mini" v-model="ruleForm.productTypeId" style="width: 100%">
                                    <el-option v-for="(item, index) in prodTypeList" :key='index' :label="item.productTypeName" :value="item.productTypeId" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="称重类型" prop="weightingTypeList">
                                <el-select size="mini" v-model="ruleForm.weightingType" style="width: 100%">
                                    <el-option v-for="(item, index) in weightingTypeList" :key='index' :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" class="btn">
                            <el-form-item>
                                <el-button size="mini" type="primary" @click="handleQuery" >搜索</el-button
                                >
                                <el-button size="mini" @click="resetQuery"
                                >重置</el-button
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div class="tabel">
                    <el-table :data="tableData" max-height="350" border>
                        <el-table-column width="50" align="center">
                            <template slot-scope="scope">
                                <el-checkbox @change='changeItem(scope.row)' :disabled='scope.row.disalbel' v-model="scope.row.check"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column type="index" align="center" label="序号"></el-table-column>
                        <el-table-column prop="productCode" align="center" label="内部产品编码"></el-table-column>
                        <el-table-column prop="productName" label="产品名称"></el-table-column>
                        <el-table-column prop="productTypeName" align="center" label="产品类型"></el-table-column>
                        <el-table-column prop="weightingTypeName" align="center" label="称重类型"></el-table-column>
                        <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
                    </el-table>
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="ruleForm.pageNum"
                        :limit.sync="ruleForm.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button size="mini" type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { productTypeList } from "@/api/basics/index.js";
import { inventoryCheckDetailPage } from '@/api/stock.js'
export default {
    data() {
        return {
            dialogVisible: false,
            sreachValue: '',
            prodTypeList: [],
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
                { text: "不限", value: '' },
            ],
            ruleForm: {
                pageNum: 1,
                pageSize: 10,
                productCode: '', //产品编码
                productName: '', //产品名称
                productTypeId: '', //产品类型主键ID
                weightingType: '', //称重类型（1定重，2抄码）
            },
            tableData: [],
            total: 0,
            selectList: []
        }
    },
    props: {
        defaultData: Array,
        warehouseId: String
    },
    watch: {
        defaultData() {
            this.selectList = this.defaultData
        }
    },
    created() {
        this.getSelectType()
        this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.getList();
        },
        handleClose() {
            this.dialogVisible = false
        },
        //重置
        resetQuery() {
            this.$refs.ruleForm.resetFields();
            this.ruleForm = {
                pageNum: 1,
                pageSize: 10,
                productCode: '', //产品编码
                productName: '', //产品名称
                productTypeId: '', //产品类型主键ID
                weightingType: '', //称重类型（1定重，2抄码）
            }
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.ruleForm.pageNum = 1;
            this.getList();
        },
        changeItem(row) {
            if (row.check) {
                this.selectList.push(row)
            } else {
                const selectList = []
                this.selectList.forEach(item => {
                    if (item.check) {
                        selectList.push(item)
                    }
                })
                this.selectList = selectList
            }
        },
        deleteItem(item, index) {
            this.tableData.map(i => {
                if (item.productId == i.productId) {
                    i.check =  false
                }
                return i
            })
            this.selectList.splice(index, 1);
        },
        handleSelectionChange(data) {
            const arr = []
            this.tableData.map((item) => {
                if (!item.disalbel) {
                    item.check = data.length > 0 ? true : false;
                    if (data.length > 0) {
                        arr.push(item)
                    }
                }
                return item
            })
            setTimeout(() => {
                if (arr.length > 0) {
                    if (this.selectList.length > 0) {
                        let selectHash = {}
                        this.selectList.forEach((item) => {
                            selectHash[item.productId] = item
                        })
                        arr.forEach(i => {
                            if (!selectHash[i.productId]) {
                                selectHash[i.productId] = i
                            }
                        })
                        this.selectList = Object.values(selectHash)
                    } else {
                        this.selectList = arr
                    }
                } else {
                    const deleteArr = []
                    this.selectList.forEach((item, index) => {
                        this.tableData.forEach(i => {
                            if (item.productId == i.productId) {
                                deleteArr.push(item)
                            }
                        })
                    })
                    setTimeout(() => {
                        const selectList = []
                        this.selectList.forEach((item) => {
                            deleteArr.forEach(i => {
                                if (item.productId != i.productId) {
                                    selectList.push(item)
                                }
                            })
                        })
                        setTimeout(() => {this.selectList = selectList})
                    })
                }
            })
        },
        getList() {
            inventoryCheckDetailPage({
                warehouseId: this.warehouseId,
                ...this.ruleForm
            }).then(res => {
                if (res.code == 200) {
                    if (this.selectList && this.selectList.length > 0) {
                        this.tableData = res.result.list.map((item) => {
                            item.note = ''
                            item.disalbel = false 
                            item.check =  false
                            this.weightingTypeList.forEach((i) => {
                                if (i.value == item.weightingType) {
                                    item.weightingTypeName = i.text;
                                }
                            });
                            this.selectList.forEach(i => {
                                if (i.productCode == item.productCode) {
                                    item.check = true 
                                }
                            });
                            return item
                        })
                    }  else {
                        this.tableData = res.result.list.map((item) => {
                            item.note = ''
                            item.check =  false
                            item.disalbel = false
                            this.weightingTypeList.forEach((i) => {
                                if (i.value == item.weightingType) {
                                    item.weightingTypeName = i.text;
                                }
                            });
                            return item
                        });
                    }
                    this.total = Number(res.result.total);
                }
            })
        },
        getSelectType() {
            productTypeList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                if (res.code == 200) {
                    this.prodTypeList = res.result.list;
                }
            });
        },
        submitForm() {
            const selectList = [];
            this.tableData.forEach(item => {
                if (item.check) {
                    selectList.push(item)
                }
            })
            this.$emit('selectList', selectList)
            this.handleClose()
        }
    }
}
</script>

<style scoped >
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.btn{
    display: flex;
    padding-left: 10px;
}
.select_box{
    padding: 15px 0 0 15px;
}
.el-tag {
    margin-bottom: 10px;
    margin-right: 10px;
}
:deep(.el-tag){
    margin-bottom: 10px;
    margin-right: 10px;
}
</style>