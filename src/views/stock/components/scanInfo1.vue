<template>
    <div class="dialog_box">
        <el-dialog
            title="扫描明细"
            :visible.sync="dialogVisible"
            width="900px"
            :modal='false'
            :before-close="handleClose">
            <div class="popup_content">
                <el-row class="scan-info-title">
                    <el-col :span='12'>产品名称：{{dataInfo.productName}}</el-col>
                    <el-col :span='12'>产品编码：{{dataInfo.productCode}}</el-col>
                </el-row>
                <el-row style="margin:20px 0" class="scan-info-title">
                    <el-col :span='12'>总计重量（kg）：{{dataDetail.inventoryWeight}}</el-col>
                    <el-col :span='12'>总计数量：{{dataDetail.inventoryNum}}</el-col>
                </el-row>
                <div class="tabel">
                    <el-table :data="tableData" max-height="380" v-if="outType" border>
                        <el-table-column show-overflow-tooltip type="index" label="序号"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="inventoryWeight" width="120" label="出库重量（kg）"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="inventoryNum" label="出库数量"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="createTime" label="出库时间"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="createUserName" label="出库人"></el-table-column>
                    </el-table>
                    <el-table :data="tableData" max-height="380" v-else border>
                        <el-table-column show-overflow-tooltip type="index" align="center" label="序号"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="inventoryWeight" width="120"  align="right" label="入库重量（kg）"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="inventoryNum"  align="right" label="入库数量"></el-table-column>
                        <el-table-column show-overflow-tooltip label="关联任务" align="center" width="350">
                            <template slot-scope="scope" >
                                <div v-if="!isShowInfo">
                                    <el-button class="text_btn" @click="handleDetails(scope.row)" type="text" >{{scope.row.divisionTaskCode}}</el-button>
                                    <el-button class="text_btn" @click="handleButcherDetails(scope.row)" type="text" >{{scope.row.butcherCode}}</el-button>
                                </div>
                                <div v-else>
                                    <span>
                                        {{scope.row.divisionTaskCode}} <el-button class="text_btn" @click="handleButcherDetails(scope.row)" type="text" >{{scope.row.butcherCode}}</el-button>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip prop="createTime" align="center" label="入库时间"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="createUserName"  align="center" label="入库人"></el-table-column>
                    </el-table>
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="ruleForm.pageNum"
                        :limit.sync="ruleForm.pageSize"
                        @pagination="getList"
                    />
                </div>
            </div>
        </el-dialog>
        <el-drawer
            class="drawer_box"
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            title="生产任务详情"
            :wrapperClosable="false">
            <Details isfooter="1" :taskId='taskId' @closeCutApart='closeCutApart' @edit='editEnclosure'></Details>
        </el-drawer>
        <el-drawer 
            class="drawer_box"
            title="屠宰详情" 
            :visible.sync="detailButcherStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :modal="true"
            :destroy-on-close="true"
            size="80%"
            :wrapperClosable="false">
                <EntranceDetail isfooter="1" ref="entranceDetail" :butcherId="butcherCode"></EntranceDetail>
        </el-drawer>
    </div>
</template>

<script>
import { scanRecord } from "@/api/stock.js";
import Details from '../../production/components/detail.vue'
import EntranceDetail from '../../acquisition/components/entranceDetail'
export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            dataDetail: {},
            total: 0,
            ruleForm: {
                pageNum: 1,
                pageSize: 10,
            },
            detailStatus: false,
            detailButcherStatus: false,
            taskId: '',
            butcherCode: ''
        }
    },
    props: {
        dataInfo: Object,
        outType: String,
        isShowInfo: String
    },
    components: {
        Details,
        EntranceDetail
    },
    created() {
        // this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.$nextTick(() => {
                this.getList();
            })
        },
        handleClose() {
            this.dialogVisible = false
        },
        getList() {
            scanRecord({
                ...this.ruleForm,
                productId: this.dataInfo.productId,
                inventoryId: this.dataInfo.inventoryId
            }).then(res => {
                if (res.code == 200) {
                    this.dataDetail = res.result
                    this.tableData = res.result.inventoryRecordList.list
					this.total = Number(res.result.inventoryRecordList.total);
                }
            })
        },
        handleDetails(row) {
            this.taskId = row.carcassDivisionTaskId
            this.detailStatus = true
        },
        handleButcherDetails(row) {
            this.butcherCode = row.butcherId
            this.detailButcherStatus = true
        },
        closeCutApart() {
            this.detailStatus = false
            this.getList();
        },
        editEnclosure(row) {
            this.detailStatus = false
            this.getList();
        },
    }
}
</script>

<style scoped>
:deep(.el-dialog__body){
    padding-top: 0;
}
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.scan-info-title{
    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    line-height: 24px;
    margin-top: 30px;
}
</style>