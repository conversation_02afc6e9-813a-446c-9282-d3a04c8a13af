<template>
    <div style="padding-bottom: 80px;">
        <div style="border-bottom: 10px solid #F5F7FA;">
            <el-descriptions class="" :title="dataInfo.inventoryCheckName" :column="3" size="medium" border>
                <template slot="extra">
                    <!-- <span>{{divisionTaskStatusHash[dataInfo.divisionTaskStatus]}}</span> -->
                    <img class="img" v-if="dataInfo.inventoryCheckStatus == 2 || dataInfo.inventoryCheckStatus == 3" src="../../../assets/images/daipandian_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.inventoryCheckStatus == 4" src="../../../assets/images/daishenhe_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.inventoryCheckStatus == 1" src="../../../assets/images/yitongguo_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.inventoryCheckStatus == 5" src="../../../assets/images/yibohui_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.inventoryCheckStatus == 0" src="../../../assets/images/yiguanbi_icon.png" alt="" srcset="">
                </template>
            </el-descriptions>
        </div>
        <!-- <el-row :gutter="20" class="mt20 f">
            <el-col :span="20">
                <el-descriptions :contentStyle='{width: "250px"}' :column="3" size="medium" border></el-descriptions>
            </el-col>
            <el-col :span="4" class="fcc">
                <div class="status"></div>
                <span :class="{
                    orange_full: dataInfo.inventoryCheckStatus == 2 || dataInfo.inventoryCheckStatus == 3,
                    blue_full: dataInfo.inventoryCheckStatus == 4,
                    green_full: dataInfo.inventoryCheckStatus == 1,
                    red_full: dataInfo.inventoryCheckStatus == 5,
                    grey_full: dataInfo.inventoryCheckStatus == 0,
                }"> {{inventoryCheckStatusHash[dataInfo.inventoryCheckStatus]}}</span>
            </el-col>
        </el-row> -->
        <div>
            <div class="fc">
                <span class="point_icon">盘点信息</span>
            </div>
            <el-descriptions :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                <el-descriptions-item label='盘点单号：'>{{ dataInfo.inventoryCheckCode }}</el-descriptions-item>
                <el-descriptions-item label='盘点日期：'>{{ dataInfo.inventoryCheckDate }}</el-descriptions-item>
                <el-descriptions-item label='盘点仓库：'>{{ dataInfo.warehouseName }}</el-descriptions-item>
                <el-descriptions-item label='盘点方式：'>{{ inventoryCheckTypeHash[dataInfo.inventoryCheckType] }}</el-descriptions-item>
                <el-descriptions-item label='盘点人员：'>{{ dataInfo.managerUserName }}</el-descriptions-item>
                <el-descriptions-item label='创建人：'>{{ dataInfo.createUserName }}</el-descriptions-item>
                <el-descriptions-item label='创建时间：'>{{ dataInfo.createTime }}</el-descriptions-item>
                <el-descriptions-item label='备注：'>{{ dataInfo.remark }}</el-descriptions-item>
            </el-descriptions>
            <div class="fc card-title">
                <span class="point_icon">盘点物品</span>
            </div>
            <el-table :data="tableData" border>
                <el-table-column type="index" align="center" label="序号"></el-table-column>
                <el-table-column prop="productName" show-overflow-tooltip label="产品名称"></el-table-column>
                <el-table-column prop="productCode" show-overflow-tooltip align="center" label="内部产品编码"></el-table-column>
                <el-table-column width="80"  align="center" label="称重类型">
                    <template slot-scope="scope">
                        <span>{{weightingTypeHash[scope.row.weightingType]}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="specification" show-overflow-tooltip align="center" width="140" label="规格单位"></el-table-column>
                <el-table-column prop="productNum" align="right" label="账面库存"></el-table-column>
                <el-table-column prop="inventoryNum" align="right" label="实盘数量"></el-table-column>
                <el-table-column prop="inventoryWeight" align="right" label="实盘重量（kg）"></el-table-column>
                <el-table-column prop="differenceNum" align="right" label="盈亏数量">
                    <template slot-scope="scope">
                        <span
                            :class="{
                                greenColor: scope.row.differenceNum > 0,
                                redColor: scope.row.differenceNum < 0,
                            }"
                        >{{scope.row.differenceNum}}</span>
                    </template>
                
                </el-table-column>
            </el-table>
            <div class="fc card-title" v-if="dataInfo.auditTime">
                <span class="point_icon">审核信息</span>
            </div>
            <el-descriptions v-if="dataInfo.auditTime" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                <el-descriptions-item label='审核结果：'>{{ inventoryCheckStatusHash[dataInfo.inventoryCheckStatus] }}</el-descriptions-item>
                <el-descriptions-item label='审核时间：'>{{ dataInfo.auditTime }}</el-descriptions-item>
                <el-descriptions-item label='审核人：'>{{ dataInfo.auditUserName }}</el-descriptions-item>
                <el-descriptions-item label='审核说明：'>{{ dataInfo.reason }}</el-descriptions-item>
            </el-descriptions>
            <el-form label-width="120px" class="mt20" v-if="dataInfo.inventoryCheckStatus == 4 || isCheck">
                <!-- <el-form-item label="审核说明" prop="remark">
                    <el-input v-model="reason"
                        type="textarea"
                        rows='3'
                        maxlength="100"
                        placeholder="输入内容，不通过时为必填项"
                        show-word-limit></el-input>
                        
                </el-form-item> -->
                <!-- <el-form-item class="footer_btn fcc">
                </el-form-item> -->
            </el-form>
        </div>
        <InventoryCheck ref='inventoryCheck' @submit='getList' :options="options"></InventoryCheck>
        <div class="footer_btn fcc">
            <el-button type="primary" class="edit_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 2" @click="edit">编辑</el-button>
            <el-button type="primary" class="grey_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 2 || dataInfo.inventoryCheckStatus == 3" @click="closeitem">关闭</el-button>
            <el-button class="check_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 4 && !isCheck" @click="checkForm">审核</el-button>
            <el-button size="small" class="add_fill_btn" v-if="dataInfo.inventoryCheckStatus == 4 && isCheck" @click="checkReset('ruleForm')">驳 回</el-button>
            <el-button type="primary" size="small" v-if="dataInfo.inventoryCheckStatus == 4 && isCheck" @click="checkOk('ruleForm')">审核通过</el-button>
            <el-button class="edit_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 5" @click="edit">重盘</el-button>
            <el-button class="edit_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 0" @click="openItem">启用</el-button>
            <el-button class="delete_fill_btn" size="small" v-if="dataInfo.inventoryCheckStatus == 0 || dataInfo.inventoryCheckStatus == 2" @click="deleteItem">删除</el-button>
        </div>
    </div>
</template>

<script>
import {
    inventoryCheckInfo,
    inventoryCheckClose,
    inventoryCheckOpen,
    inventoryCheckDelete,
} from '@/api/stock.js';
import InventoryCheck from './inventoryCheck.vue'
export default {
    data() {
        return {
            dataInfo: {},
            inventoryCheckStatusHash: {
                2: '待盘点',
                3: '进行中',
                1: '审核通过',
                4: '待审核',
                5: '审核不通过',
                0: '已关闭',
            },
            inventoryCheckTypeHash: {
                1: '按仓库盘点',
                2: '按产品盘点',
            },
            weightingTypeHash: {
                1: "定重",
                2: "抄码",
            },
            reason: '',
            tableData: [],
            options: {}
        }
    },
    props: {
        inventoryCheckId: String,
        isCheck: Boolean
    },
    components: {
        InventoryCheck
    },
    watch: {
        inventoryCheckId() {
            if (this.inventoryCheckId) {
                this.getInfo()
            }
        }
    }, 
    created() {
        if (this.inventoryCheckId) {
            this.getInfo()
        }
    },
    methods: {
        getInfo() {
            inventoryCheckInfo({
                inventoryCheckId: this.inventoryCheckId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                    this.tableData = res.result.inventoryProductList
                }
            })
        },
        getList() {
            this.getInfo()
            this.$emit('getList')
        },
        openItem() {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定启用盘点任务？</div><div style='padding-left:22px'>启用后系统刷新当前任务，盘点人员重新执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckOpen({
                    inventoryCheckId: this.dataInfo.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getInfo()
                        this.$message.success('操作成功')
                    }
                })
            });
        },
        deleteItem() {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定删除盘点任务？</div><div style='padding-left:22px'>删除后盘点人员不可再执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckDelete({
                    inventoryCheckId: this.dataInfo.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getInfo()
                        this.$message.success('操作成功')
                    }
                })
            });
        },
        closeitem() {
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定关闭盘点任务？</div><div style='padding-left:22px'>关闭后盘点人员不可再执行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryCheckClose({
                    inventoryCheckId: this.dataInfo.inventoryCheckId
                }).then((res) => {
                    if (res.code == 200) {
                        this.getInfo()
                        this.$message.success('操作成功')
                    }
                })           

            });
        },
        edit() {
            this.$emit('edit', this.dataInfo)
        },
        checkForm() {
            this.$emit('checkForm', this.dataInfo)
        },
        checkReset() {
            this.options = {
                title: '审核驳回',
                subTitle: '必填，请输入审核意见',
                inventoryCheckId: this.dataInfo.inventoryCheckId,
                inventoryCheckStatus: 5
            }
            this.$refs.inventoryCheck.handleOpen()
        },
        checkOk() {
            this.options = {
                title: '审核通过',
                subTitle: '选填，请输入审核意见',
                inventoryCheckId: this.dataInfo.inventoryCheckId,
                inventoryCheckStatus: 1
            }
            this.$refs.inventoryCheck.handleOpen()
        }
    }
}
</script>

<style lang="scss" scoped>
.title{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.title div {
    font-size: 18px;
}
.el-row {
    font-size: 14px !important;
    .el-col-7{
        margin-top: 20px;
    }
}
.card-title {
  margin-bottom: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.model {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  &-text {
    color: rgb(102, 102, 102);
  }
  .header {
    &-title {
      font-size: 18px;
    }
  }
}
.greenColor{
    color: rgb(19, 198, 82);
}
.redColor{
    color: rgb(223, 55, 32);
}
.footer_btn{
    width: 80%;
}
.img{
    width: 79px;
    height: 77px;
}
.el-descriptions .el-descriptions__header{
    margin: 0;
}
:deep(.el-descriptions__header) {
    margin-bottom: 0px;
}
</style>