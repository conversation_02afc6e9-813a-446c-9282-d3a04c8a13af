<template>
    <div class="dialog_box">
        <el-dialog
            title="库存分布"
            :visible.sync="dialogVisible"
            width="600"
            :before-close="handleClose">
            <div class="popup_content">
                <div class="title1">
                    <span>产品名称：{{dataInfo.productName}}</span>
                    <span style="margin-left: 100px">产品编码：{{dataInfo.productCode}}</span>
                </div>
                <div class="tabel" style="margin-top: 40px">
                    <el-table :data="tableData" :show-summary="true" border max-height="350">
                        <el-table-column type="index" align="center" label="序号"></el-table-column>
                        <el-table-column prop="warehouseName" align="center" label="仓库"></el-table-column>
                        <el-table-column prop="inventoryNum" align="right" label="当前库存"></el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { selectByProduct } from "@/api/stock.js";
export default {
    data() {
        return {
            dialogVisible: false,
            tableData: [],
        }
    },
    props: {
        dataInfo: Object
    },
    created() {
        // this.getList()
    },
    methods: {
        showModel() {
            this.dialogVisible = true
            this.$nextTick(() => {
                this.getList();
            })
        },
        handleClose() {
            this.dialogVisible = false
        },
        getList() {
            selectByProduct({
                requestRole: this.$route.query.requestRole,
                productId: this.dataInfo.productId
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result
                }
            })
        },
    }
}
</script>

<style scoped>
:deep(.el-dialog__body){
    padding-top: 0;
}
.content{
    min-height: 120px;
    border: 1px solid #DCDFE6;
    padding: 10px;
}
.title1{
    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    line-height: 24px;
    margin-top: 30px;
}
</style>