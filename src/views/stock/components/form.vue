<template>
  <div style="padding-bottom: 80px">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" style="padding-right: 50px">
            <div class="fc">
                <span class="point_icon">出库信息</span>
            </div>
            <el-row>
                  <el-col :span="12">
                    <el-form-item label="出库类型" prop="inventoryType">
                        <el-select v-model="ruleForm.inventoryType" style="width: 100%">
                            <el-option v-for="(item, index) in outWarehouseType" :key='index' :label="item.text" :value="item.value" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="ruleForm.carcassDivisionTaskId">
                    <el-form-item label="关联生产任务">
                        <el-input disabled v-model="ruleForm.carcassDivisionTaskId"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-else>
                    <el-form-item label="出库日期" prop="inventoryDate">
                        <el-date-picker
                            style="width: 100%"
                            v-model="ruleForm.inventoryDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptions"
                            placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="出库仓库" required>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item prop="warehouseType">
                                    <el-select v-model="ruleForm.warehouseType" style="width: 100%" @change='getWarehouseList'>
                                        <el-option v-for="(item, index) in warehouseTypeList" :key='index' :label="item.text" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item prop="warehouseId">
                                    <el-select v-model="ruleForm.warehouseId" style="width: 100%" @change='changeWarehouse'>
                                        <el-option v-for="(item, index) in warehouseList" :key='index' :label="item.warehouseName" :value="item.warehouseId" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form-item>
                  </el-col>
                  
            </el-row>
            <!-- 生产人员 -->
            <!-- <el-row style="padding: 0 100px">
                <Personnel></Personnel>
            </el-row> -->
            <div class="fc">
                <span class="point_icon">出库明细</span>
            </div>
            <el-row>
                <div>
                    <Generate ref="generate" :warehouseInfo='warehouseInfo'></Generate>
                </div>
            </el-row>
            
            <el-form-item class="fcc footer_btn">
                <el-button type="primary" size="small" @click="submitForm('ruleForm')">提 交</el-button>
                <el-button size="small" @click="resetForm('ruleForm')">取 消</el-button>
            </el-form-item>
        </el-form>
        <Confirm ref="confim" @submit="submit"></Confirm>
  </div>
</template>

<script>
import { inventoryAdd } from '@/api/stock.js'
import Generate from './generate1.vue'
import Confirm from './confirm.vue'
import { warehouseList } from "@/api/basics/index.js";
export default {
    dicts: ['mes_bt_flag'],
    data() {
        return {
            warehouseTypeList: [
                // { text: "排酸库", value: 1 },
                { text: "急冻库", value: 2 },
                { text: "成品库", value: 3 },
                { text: "其他", value: 4 },
            ],
            warehouseList: [],
            outWarehouseType: [
                { text: "销售出库", value: 22 },
            ],
            warehouseInfo: {},
            ruleForm: {
                warehouseType: '',
                warehouseId: '',
                inventoryDate: '',
                inventoryType: '',
                carcassDivisionTaskId: ''
            },

            rules: {
                inventoryType: [
                    { required: true, message: '请选择出库类型', trigger: 'change' },
                ],
                warehouseType: [
                    { required: true, message: '请选择仓库类型', trigger: 'change' },
                ],
                warehouseId: [
                    { required: true, message: '请选择出库仓库', trigger: 'change' },
                ],
                inventoryDate: [
                    { required: true, message: '请选择出库日期', trigger: 'blur' },
                ]
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 86400000;
                },
            },
        }
    },
    props: {
        taskId: String,
        dataInfo: Object
    },
    components: {
        Generate,
        Confirm
    },
    watch: {
        dataInfo() {
            if (this.dataInfo) {
                this.ruleForm.carcassDivisionTaskId = this.dataInfo.carcassDivisionTaskId
                this.outWarehouseType = [
                    { text: "领料出库", value: 21 }
                ]
            }
        }
    },
    created() {
        if (this.dataInfo && this.dataInfo.carcassDivisionTaskId) {
            this.ruleForm.carcassDivisionTaskId = this.dataInfo.carcassDivisionTaskId
            this.outWarehouseType = [
                { text: "领料出库", value: 21 }
            ]
        }
    },
    methods: {
        getWarehouseList() {
            this.ruleForm.warehouseId = ''
            warehouseList({
                pageNum: 1,
                pageSize: 10000,
                requestRole: this.$route.query.requestRole,
                // requestRole: 2,
                warehouseType: this.ruleForm.warehouseType
            }).then((res) => {
                if (res.code == 200) {
                    this.warehouseList = res.result.list
                }
            });
        },
        changeWarehouse(value) {
            const warehouseInfo = this.warehouseList.filter(item => {
                return value == item.warehouseId
            })
            this.warehouseInfo = warehouseInfo[0]
        },


        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {

                    const generate = this.$refs.generate.tableData
                    if (generate.length < 1) {
                        this.$message.error('请添加出库明细')
                        return
                    }
                    this.generate = generate
                    this.addSubmit()
                }
            });
            
        },
        getFormList() {
            const productList = []
            if(this.generate.length > 0) {
                this.generate.forEach(item => {
                    productList.push({
                        warehouseDetailId: item.warehouseDetailId, //产品编号
                        productNum: item.productNum, 
                    })
                })
            }
            this.validProductList = productList;
        },
        submit(data) {
            this.collectFlag = data.collectFlag
            this.addSubmit()
        },
        addSubmit() {
            const generate = this.$refs.generate.tableData
            if (generate.length < 1) {
                this.$message.error('请添加出库明细')
                return
            }
            this.generate = generate
            this.getFormList()
            let obj = {
                inventoryType: this.ruleForm.inventoryType,
                warehouseId: this.ruleForm.warehouseId,
            }
            if (this.ruleForm.inventoryType == 21) {
                obj.carcassDivisionTaskId = this.ruleForm.carcassDivisionTaskId
            }
            if (this.ruleForm.inventoryType == 22) {
                obj.inventoryDate = this.ruleForm.inventoryDate
            }
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确认提交出库?</div><div style='padding-left:22px'>提交后库管人员可根据单据执行出库操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                inventoryAdd({
                    ...obj,
                    // collectFlag: this.divisionTaskStatus == -1 ? 0 : this.collectFlag ? 1 : 0,
                    inventoryProductList: this.validProductList,
                }).then(res => {
                    this.$message.success('出库成功')
                    this.$refs.ruleForm.resetFields();
                    this.$emit('close')
                })
            });
            
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding-left: 20px
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
.footer_btn{
    width: 80%;
}
</style>