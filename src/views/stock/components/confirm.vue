<template>
    <div class="dialog_box">
        <el-dialog
            title="操作确认"
            :visible.sync="dialogVisible"
            :modal='false'
            width="30%">
           <el-row style="padding-left: 45px">
                <div style="font-size: 18px" class="mt20">
                    是否确认提交出库?
                </div>
                <div class="mt20">
                    <el-checkbox v-model="collectFlag">
                        提交后库管人员可根据单据执行出库操作
                    </el-checkbox>
                </div>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" size="mini" @click="addSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            collectFlag: true,
        }
    },
    methods: {
        addSubmit() {
            this.dialogVisible = false
            this.$emit('submit', {
                collectFlag: this.collectFlag,
            })
        },
        showModel() {
            this.dialogVisible = true
        }
    }
}
</script>

<style>

</style>