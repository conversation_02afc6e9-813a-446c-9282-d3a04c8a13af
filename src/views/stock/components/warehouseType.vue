<template>
    <div class="main">
        <el-table :data="tableData" max-height="380" border>
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="productName" label="产品名称"></el-table-column>
            <el-table-column prop="productCode" align="center" label="内部产品编码"></el-table-column>
            <el-table-column prop="weightingTypeName" align="center" label="称重类型"></el-table-column>
            <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
            <el-table-column prop="inventoryNum" align="right" label="账面库存"></el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getSelectList"
        />
    </div>
</template>

<script>
import { inventoryCheckDetailPage } from '@/api/stock.js'
export default {
    data() {
        return {
            tableData: [],
            total: 0,
            queryParams: {
                pageNum: 1, //页
                pageSize: 10, //行数
            },
            weightingTypeHash: {
                1: "定重",
                2: "抄码",
            },
        }
    },
    components: {
    },
    props: {
        warehouseId: String
    },
    watch: {
        warehouseId() {
            if (this.warehouseId) {
                this.getSelectList()
            }
        }
    },
    created() {
        if (this.warehouseId) {
            this.getSelectList()
        }
    },
    methods: {
        getSelectList(data) {
            inventoryCheckDetailPage({
                ...this.queryParams,
                warehouseId: this.warehouseId, //仓库ID
            }).then(res => {
                this.tableData = res.result.list.map(item => {
                    item.weightingTypeName = this.weightingTypeHash[item.weightingType]
                    return item
                })
                this.total = Number(res.result.total);
                this.$emit('inventoryCheckTypeTotal', this.total)
            })
        },
    }
}
</script>

<style>

</style>