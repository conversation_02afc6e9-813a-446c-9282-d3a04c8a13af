<template>
  <div style="padding-bottom: 60px">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" style="padding-right: 50px">
        <div class="steps">
            <el-steps :active="activeSteps">
                <el-step title="盘点信息" description="请填写任务基本信息"></el-step>
                <el-step
                    :title="ruleForm.inventoryCheckType == 1 ? '产品预览' : '产品选择'"
                    :description="ruleForm.inventoryCheckType == 1 ? '预览盘点内容' : '请选择盘点产品'"
                >
                </el-step>
                <el-step title="提交成功" description="盘点任务提交成功"></el-step>
            </el-steps>
        </div>
        <div v-if='activeSteps == 1' class='mt20'>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="任务名称" prop="inventoryCheckName">
                        <el-input v-model="ruleForm.inventoryCheckName" maxlength="25" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="盘点日期" prop="inventoryCheckDate">
                        <el-date-picker
                            style="width: 100%"
                            v-model="ruleForm.inventoryCheckDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期">
                        </el-date-picker>
                        <!-- :picker-options="pickerOptions" -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="盘点仓库" prop="warehouseId">
                        <el-select v-model="ruleForm.warehouseId" style="width: 100%" @change='changeWarehouse' placeholder="请选择">
                            <el-option v-for="(item, index) in warehouseList" :key='index' :label="item.warehouseName" :value="item.warehouseId" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="盘点方式" prop="inventoryCheckType">
                        <el-select v-model="ruleForm.inventoryCheckType" style="width: 100%" placeholder="请选择">
                            <el-option v-for="(item, index) in inventoryCheckTypeList" :key='index' :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="盘点人员" prop="managerUserId">

                        <!-- remote
                                filterable
                                reserve-keyword
                                :remote-method="remoteMethod"
                                @change='changeManagerUser'
                                :loading='loading' -->
                            <el-select
                                v-model="ruleForm.managerUserId"
                                placeholder="请选择盘点人员"
                                style="width: 100%">
                                <el-option
                                    v-for="item in managerList"
                                    :key="item.managerUserId"
                                    :label="item.managerUserName"
                                    :value="item.managerUserId">
                                </el-option>
                            </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="ruleForm.remark" type="textarea" rows='3' maxlength="100" placeholder="输入内容"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </div>
        <div v-if='activeSteps == 2 && ruleForm.inventoryCheckType == 1' class='mt20'>
            <WarehouseType :warehouseId='ruleForm.warehouseId' @inventoryCheckTypeTotal='getinventoryCheckTypeTotal'></WarehouseType>
        </div>
        <div v-show='activeSteps == 2 && ruleForm.inventoryCheckType == 2' class='mt20'>
            <ProductType @selectList='getSelectList' :productList='selectList' :warehouseId='ruleForm.warehouseId'></ProductType>
        </div>
        <div v-show='activeSteps == 3' class='mt20'>
            <div class="step3">
                <img src="../../../assets/images/success_icon.png" alt="">
                <p>盘点任务提交成功</p>
                <div class="btn">
                    <el-button size="small" @click="resetForm()">关 闭</el-button>
                    <el-button type="primary" size="small" @click="again('ruleForm')">再次新建任务</el-button>
                </div>
            </div>
        </div>
        <el-form-item class="fcc mt20 footer_btn" v-if='activeSteps == 1'>
            <el-button type="primary" size="small" @click="nextStep('ruleForm')">下一步</el-button>
        </el-form-item>

        <el-form-item class="fcc mt20 footer_btn" v-if='activeSteps == 2'>
            <el-button type="primary" size="small" @click="upStep">上一步</el-button>
            <el-button type="primary" size="small" @click="submitForm('ruleForm')">提交</el-button>
        </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
    listUser
} from "@/api/production/index.js";
import { 
    inventoryCheckAdd,
    inventoryCheckInfo,
    inventoryCheckUpdate,
    selectManager
} from '@/api/stock.js'
import { warehouseList } from "@/api/basics/index.js";
import WarehouseType from './warehouseType.vue'
import ProductType from './productType.vue'
export default {
    data() {
        return {
            inventoryCheckTypeList: [
                { label: "按仓库盘点", value: 1 },
                { label: "按产品盘点", value: 2 },
            ],
            ruleForm: {
                inventoryCheckName: '', //盘点任务名称
                inventoryCheckDate: '', //盘点时间
                inventoryCheckType: '', //盘点方式：1仓库 2产品
                warehouseId: '', //仓库ID
                managerUserId: '', //库管员userId
                remark: '', //备注
            },
            warehouseList: [],
            managerList: [],
            loading: false,
            activeSteps: 1,
            rules: {
                inventoryCheckName: [
                    { required: true, message: '请输入盘点任务名称', trigger: 'blur' },
                ],
                inventoryCheckDate: [
                    { required: true, message: '请输选择盘点时间', trigger: 'change' },
                ],
                inventoryCheckType: [
                    { required: true, message: '请输选择盘点方式', trigger: 'change' },
                ],
                warehouseId: [
                    { required: true, message: '请选择盘点仓库', trigger: 'change' },
                ],
                managerUserId: [
                    { required: true, message: '请选择盘点人员', trigger: 'change' },
                ]
            },
            selectList: [],
            inventoryCheckTypeTotal: 0,
            weightingTypeHash: {
                1: "定重",
                2: "抄码",
            },
            dataInfo: {}
        }
    },
    props: {
        inventoryCheckId: String
    },
    components: {
        WarehouseType,
        ProductType
    },
    watch: {
        inventoryCheckId() {
            if (this.inventoryCheckId) {
                this.getInfo()
            }
        },
    },
    created() {
        this.getWarehouseList()
        if (this.inventoryCheckId) {
            this.getInfo()
        }
    },
    methods: {
        getWarehouseList() {
            warehouseList({
                pageNum: 1,
                pageSize: 10000,
                warehouseType: -1,
                requestRole: this.$route.query.requestRole
            }).then((res) => {
                if (res.code == 200) {
                    this.warehouseList = res.result.list
                }
            });
        },
        changeWarehouse() {
            this.selectList = []
            this.remoteMethod(this.ruleForm.warehouseId)
            this.ruleForm.managerUserId = ''
            this.$refs.ruleForm.clearValidate(['managerUserId'])
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                // const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
                selectManager({
                    pageNum: 1,
                    pageSize: 999,
                    warehouseId: query,
                }).then(res => {
                    this.loading = false;
                    if(res.code == 200) {
                        this.managerList = res.result
                    } else {
                        this.managerList = [];
                    }
                })
            } else {
                this.managerList = [];
            }
        },
        changeManagerUser(value, row) {
            this.managerList.forEach(item => {
                if (value == item.userId) {
                    this.ruleForm.managerName = item.corprateName || item.nickName //负责人名称
                    this.ruleForm.managerDepartmentId = item.dept.deptId //负责人所属部门ID
                    this.ruleForm.managerDepartment = item.dept.deptName //负责人所属部门
                }
            });
        },
        nextStep(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.activeSteps = 2
                }
            });
        },
        upStep() {
            this.activeSteps = 1
        },
        getinventoryCheckTypeTotal(total) {
            this.inventoryCheckTypeTotal = total
        },
        submitForm() {
            const selectList = []
            if (this.ruleForm.inventoryCheckType == 2 && this.selectList.length > 0) {
                this.selectList.forEach(item => {
                    selectList.push({
                        warehouseDetailId: item.warehouseDetailId
                    })
                })
            }
            if (this.ruleForm.inventoryCheckType == 2 && this.selectList.length <= 0) {
                this.$message.error('请选择盘点产品') 
                return
            }
            if (this.ruleForm.inventoryCheckType == 1 && this.inventoryCheckTypeTotal <= 0) {
                this.$message.error('选择的仓库暂无盘点产品') 
                return
            }
            this.$confirm(
                "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否提交盘点任务？</div><div style='padding-left:22px'>提交后库管人员可进行盘点操作</div>",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                if (this.inventoryCheckId && this.dataInfo.inventoryCheckStatus != 4 && this.dataInfo.inventoryCheckStatus != 5) {
                    this.updateForm(selectList)
                } else {
                    this.addForm(selectList)
                }
            });
        },
        addForm(selectList) {
            inventoryCheckAdd({
                ...this.ruleForm,
                warehouseDetailList: selectList
            }).then((res) => {
                if (res.code == 200) {
                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                    this.activeSteps = 3
                    this.resetForm()
                }
            });
        },
        updateForm(selectList) {
            inventoryCheckUpdate({
                ...this.ruleForm,
                warehouseDetailList: selectList,
                inventoryCheckId: this.inventoryCheckId
            }).then((res) => {
                if (res.code == 200) {
                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                    this.activeSteps = 3
                    this.resetForm()
                }
            });
        },
        getSelectList(dataList) {
            this.selectList = dataList
        },
        getInfo() {
            inventoryCheckInfo({
                inventoryCheckId: this.inventoryCheckId
            }).then(res => {
                if(res.code == 200) {
                    const dataInfo = res.result
                    this.dataInfo = dataInfo
                    // this.remoteMethod(dataInfo.managerUserName)
                    this.ruleForm = {
                        inventoryCheckName: dataInfo.inventoryCheckName, //盘点任务名称
                        inventoryCheckDate: dataInfo.inventoryCheckDate, //盘点时间
                        inventoryCheckType: dataInfo.inventoryCheckType, //盘点方式：1仓库 2产品
                        warehouseId: dataInfo.warehouseId, //仓库ID
                        managerUserId: dataInfo.managerUserId, //库管员userId
                        remark: dataInfo.remark, //备注
                    }
                    this.selectList = dataInfo.inventoryProductList.map(item => {
                        item.weightingTypeName = this.weightingTypeHash[item.weightingType]
                        return item
                    })
                }
            })
        },
        resetForm() {
            this.$refs.ruleForm.resetFields();
            this.dialogVisible = false
            this.$emit('close')
        },
        again(formName) {
            this.ruleForm = {
                inventoryCheckName: '', //盘点任务名称
                inventoryCheckDate: '', //盘点时间
                inventoryCheckType: '', //盘点方式：1仓库 2产品
                warehouseId: '', //仓库ID
                managerUserId: '', //库管员userId
                remark: '', //备注
            }
            this.$refs[formName].resetFields();
            this.selectList = []
            this.activeSteps = 1
        }
    }
}
</script>

<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding: 0 100px;
    padding-left: 20px
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
.steps{
    margin-top: 40px;
    padding-bottom: 20px;
    border-bottom: 10px solid #F5F7FA;
}
.el-col-12{
    &:nth-child(odd) {
        padding-right: 20px;
    }
    &:nth-child(even) {
        padding-left: 20px;
    }
}
:deep(.el-form-item .el-form-item__label){
    line-height: 25px;
}
.step3{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 164px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #1D2129;
    line-height: 50px;
    img{
        width: 40px;
    }
}
</style>