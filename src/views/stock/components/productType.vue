<template>
    <div class="main">
        <div style="display:flex; margin-bottom: 10px">
            <ProductListModel @selectList='getSelectList' :warehouseId='warehouseId' :defaultData='tableData'></ProductListModel>
        </div>
      
        <el-table :data="tableData" max-height="380" border>
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="productName" label="产品名称"></el-table-column>
            <el-table-column prop="productCode" align="center" label="内部产品编码"></el-table-column>
            <el-table-column prop="weightingTypeName" align="center" label="称重类型"></el-table-column>
            <el-table-column prop="specification" align="center" label="规格单位"></el-table-column>
            <el-table-column prop="inventoryNum" align="right" label="账面库存"></el-table-column>
        </el-table>
    </div>
</template>

<script>
import ProductListModel from './productListModel.vue'
export default {
    data() {
        return {
            tableData: [],
            flag: false
        }
    },
    components: {
        ProductListModel
    },
    props: {
        productList: Array,
        warehouseId: String
    },
    watch: {
        productList() {
            this.tableData = this.productList
        }
    },
    created() {
        this.tableData = this.productList
    },
    methods: {
        getSelectList(data) {
            this.tableData = JSON.parse(JSON.stringify(data))
            this.$emit('selectList', this.tableData)
            // data1.forEach(item => {
                // item.note = item.note
                // item.check = false
                // this.tableData.push(item)
            // })
        }
    }
}
</script>

<style>

</style>