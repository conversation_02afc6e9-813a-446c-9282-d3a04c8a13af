<template>
	<div class="app-container tabs_box">
        <el-card class="box-card form-card mb10" shadow="never">
            <el-row :gutter="10">
				<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
                    <el-row class=" form_row">
                        <el-row class="form_col">
							<el-form-item  prop="inventoryCode">
								<el-input v-model="queryParams.inventoryCode" placeholder="出库单号" clearable />
							</el-form-item>
							<el-form-item  prop="inventoryType">
								<el-select v-model="queryParams.inventoryType" placeholder="出库类型">
									<el-option
										v-for="(item,index) in warehouseTypeList"
										:label="item.text"
										:value="item.value"
										:key="index"
									/>
								</el-select>
							</el-form-item>
							<el-form-item  prop="warehouseId">
								<el-select v-model="queryParams.warehouseId" placeholder="出库仓库">
								<el-option
									v-for="(item,index) in warehouseList"
									:label="item.warehouseName"
									:value="item.warehouseId"
									:key="index"
								/>
								</el-select>
							</el-form-item>
							<el-form-item  prop="inventoryStatus">
								<el-select v-model="queryParams.inventoryStatus" placeholder="单据状态">
									<el-option
										v-for="(item,index) in inventoryStatusList"
										:label="item.text"
										:value="item.value"
										:key="index"
									/>
								</el-select>
							</el-form-item>
							<el-form-item >
								<el-date-picker
								v-model="dateEnter"
								style="width: 240px"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="-"
								start-placeholder="出库开始日期"
								end-placeholder="出库结束日期"
								></el-date-picker>
							</el-form-item>
						</el-row>
					</el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
				</el-form>
		    </el-row>
		</el-card>
		<el-card class="table_box" shadow="never">
			<el-row  class="mb8 form_btn">
				<el-col class="tabs-box" :span="12">
					<el-tabs v-model="listType">
						<el-tab-pane label="按单据" name="1">
						</el-tab-pane>
						<el-tab-pane label="按明细" name="2">
						</el-tab-pane>
					</el-tabs>
				</el-col>
				<el-col :span='12' class="fend">
					<el-button class="default_btn" icon="el-icon-plus" size="mini" v-if="listType == 1" @click="addForm">新建出库单</el-button>
					<el-button class="default_btn" icon="el-icon-download" size="mini" @click="exportList">导出数据</el-button>
				</el-col>
				<!-- <div class="tab fend">
					<span :class="{text_btn: listType == 1}" @click="changeList(1)">按单据</span>
					<span :class="{text_btn: listType == 2}" @click="changeList(2)">按明细</span>
				</div> -->
			</el-row>

			<!-- 表格数据 -->
			<div :style="{height: tableHeight + 'px'}">
				<el-table :data="tableData" border ref="myTable" v-if="listType == 1" style="width: 100%" :max-height="tableHeight" v-loading="loading">
					<el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="inventoryCode" label="出库单号"  width="140">
						<template slot-scope="scope" >{{ scope.row.inventoryCode }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="出库类型" width="140">
						<template slot-scope="scope" >{{ handeltext(scope.row.inventoryType) }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="warehouseName" width="140" label="出库仓库">
						<template slot-scope="scope" >{{ scope.row.warehouseName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryNum" width="140" sortable label="出库数量">
						<template slot-scope="scope" >{{ scope.row.inventoryNum }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryWeight" width="160" sortable label="出库重量（kg）">
						<template slot-scope="scope" >{{ scope.row.inventoryWeight }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="单据状态">
						<template slot-scope="scope">
							<span :class="{
									orange: scope.row.inventoryStatus == 2,
									blue: scope.row.inventoryStatus == 3,
									green: scope.row.inventoryStatus == 1,
								}">{{ handelInventoryStatustext(scope.row.inventoryStatus) }}
							</span></template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createUserName" width="140" label="操作人">
						<template slot-scope="scope" >{{ scope.row.createUserName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createTime" width="160" sortable label="出库日期">
						<template slot-scope="scope" >{{ scope.row.createTime }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="操作" fixed="right"  width="100">
						<template slot-scope="scope">
							<el-button class="text_btn" size="mini" icon="el-icon-warning-outline" @click="stockInfo(scope.row)" type="text" >查看</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-table :data="tableData1" border ref="myTable" v-if="listType == 2" style="width: 100%" :max-height="tableHeight" v-loading="loading">
					<el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="出库类型" width="140">
						<template slot-scope="scope">{{ handeltext(scope.row.inventoryType) }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="warehouseName" label="出库仓库" width="140">
						<template slot-scope="scope" >{{ scope.row.warehouseName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="productName" label="产品名称" width="140">
						<template slot-scope="scope" >{{ scope.row.productName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="specification" label="规格单位" width="140">
						<template slot-scope="scope" >{{ scope.row.specification }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryNum" sortable label="实际出库数量" width="140">
						<template slot-scope="scope" >{{ scope.row.inventoryNum }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryWeight" sortable width="160" label="实际出库重量（kg）">
						<template slot-scope="scope">
							{{scope.row.inventoryWeight}}
						</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createTime" label="出库时间">
						<template slot-scope="scope" >{{ scope.row.createTime }}</template>
					</el-table-column>
				</el-table>
			</div>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				@pagination="getList"
			/>
		</el-card>
		<el-drawer
			class="drawer_box"
			:visible.sync="modelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			:title='handeltext(currentItem.inventoryType)'
			:wrapperClosable="false">
			<OutWarehouseDetail ref="outWarehouseDetail" :dataInfo='currentItem'></OutWarehouseDetail>
		</el-drawer>
		<el-drawer
			class="drawer_box"
			:visible.sync="formModelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			title='新建出库单'
			:wrapperClosable="false">
			<Form ref="form" @close='closeFormModel'></Form>
		</el-drawer>
	</div>
</template>
      
      <script>
import { inventoryPage, inventoryExport, pageProduct, pageProductExport } from "@/api/stock.js";
import { exportExcel } from "@/utils/east";
import { warehouseList } from "@/api/basics/index.js";
import OutWarehouseDetail from './components/outWarehouseDetail.vue'
import Form from './components/form.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                inventoryCode: "",
                materialsId:'',
                warehouseId:'',
				inventoryStatus: 1,
                businessType: 2,
                pageNum: 1,
                pageSize: 10,
            },
            materialsList: [], //原料名称
            warehouseList: [], //原料等级
            tableData: [],
            tableData1: [],
            loading: true,
            dateEnter: [],
            total: 0,
            warehouseTypeList: [
                { text: "领料出库", value: 21 },
                { text: "销售出库", value: 22 },
                { text: "盘亏出库", value: 23 },
                { text: "急冻出库", value: 24 },
            ],
            inventoryStatusList: [
                { text: "已出库", value: 1 },
                { text: "待出库", value: 2 },
                { text: "进行中", value: 3 },
            ],
            currentItem: {},
            modelShow: false,
            listType: '1',
            formModelShow: false,
            sreachShow: false,
        };
    },
    components: {
      OutWarehouseDetail,
      Form
    },
    computed: {
        handeltext(){
            return (value)=>{
                let name=''
                this.warehouseTypeList.forEach(item=>{
                    if(item.value==value){
                        name=item.text
                    }
                })
                return name
            }
        },
        handelInventoryStatustext(){
            return (value)=>{
                let name=''
                this.inventoryStatusList.forEach(item=>{
                    if(item.value==value){
                        name=item.text
                    }
                })
                return name
            }
        },
    },
	watch: {
		listType() {
			this.getList()
		}
	},
    created() {
		this.queryParams.requestRole = this.$route.query.requestRole
        this.getMaterial();
        this.getList();
    },
    methods: {
    refresh() {
        this.getList();
    },
    getMaterial() {
        warehouseList({
            requestRole: this.$route.query.requestRole,
            pageNum: 1,
            pageSize: 10000,
			warehouseType: -1,
        }).then((res) => {
            if (res.code == 200) {
                this.warehouseList = res.result.list || [];
            }
        });
    },
    //列表查询
    getList() {
		if (this.listType == 1) {
			inventoryPage(this.queryParams).then((res) => {
				if (res.code == 200) {
					this.tableData = res.result.list;
					this.total = Number(res.result.total);
					this.loading = false;
					this.$nextTick(() => {
						this.$refs.myTable.doLayout();
					});
				}
			});
		} else {
			pageProduct(this.queryParams).then((res) => {
				if (res.code == 200) {
					this.tableData1 = res.result.list;
					this.total = Number(res.result.total);
					this.loading = false;
					this.$nextTick(() => {
						this.$refs.myTable.doLayout();
					});
				}
			});
		}
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
        this.dateEnter = [];
        this.reset();
        this.handleQuery();
    },
    //刷新页面
    refreshList() {
        this.getList();
    },
    handelData(startTime, endTime, list) {
        if (list?.length > 0) {
            this.queryParams[startTime] = list[0];
            this.queryParams[endTime] = list[1];
        } else {
            delete this.queryParams[startTime];
            delete this.queryParams[endTime];
        }
    },
    //搜索
    handleQuery() {
        this.queryParams.pageNum = 1;
        this.handelData("startTime", "endTime", this.dateEnter);
        this.getList();
    },
    stockInfo(row) {
		this.currentItem = row
		this.modelShow = true
    },
	changeList(listType) {
		this.listType = listType
		this.queryParams.pageNum = 1
		this.queryParams.pageSize = 10
        this.getList();
	},
    addForm() {
        this.formModelShow = true
    },
    closeFormModel() {
        this.formModelShow = false
        this.getList();
    },
    exportList(){
		if (this.listType == 1) {
			exportExcel(inventoryExport,this.queryParams,'出库记录--按单据')
		} else {
			exportExcel(pageProductExport,this.queryParams,'出库记录--按明细')
		}
	},
  },
};
</script>
      
<style lang="scss" scoped>
    .tab{
		padding: 0 20px;
		color: #333333;
		span{
			margin: 0 10px;
			cursor: pointer;
		}
    }
</style>
      