<template>
	<div class="app-container tabs_box">
        <el-card class="box-card form-card mb10" shadow="never">
            <el-row :gutter="10">
				<el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  class="form_box">
                    <el-row class=" form_row">
                        <el-row class="form_col">
							<el-form-item  prop="inventoryCode">
								<el-input v-model="queryParams.inventoryCode" placeholder="入库单号" clearable />
							</el-form-item>
							<el-form-item  prop="inventoryType">
								<el-select v-model="queryParams.inventoryType" placeholder="入库类型">
								<el-option
									v-for="(item,index) in warehouseTypeList"
									:label="item.text"
									:value="item.value"
									:key="index"
								/>
								</el-select>
							</el-form-item>
							<el-form-item  prop="warehouseId">
								<el-select v-model="queryParams.warehouseId" placeholder="入库仓库">
									<el-option
										v-for="(item,index) in warehouseList"
										:label="item.warehouseName"
										:value="item.warehouseId"
										:key="index"
									/>
								</el-select>
							</el-form-item>
							<el-form-item>
								<el-date-picker
								v-model="dateEnter"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="-"
								start-placeholder="入库开始日期"
								end-placeholder="入库结束日期"
								></el-date-picker>
							</el-form-item>
						</el-row>
					</el-row>
					<el-row>
						<el-form-item label=" ">
							<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
							<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
						</el-form-item>
					</el-row>
				</el-form>
		</el-row>

		</el-card>
		<el-card class="table_box" shadow="never">
			<el-row  class="mb8 form_btn">
				<el-col :span="12" class="tabs-box">
					<el-tabs v-model="listType">
						<el-tab-pane label="按单据" name="1">
						</el-tab-pane>
						<el-tab-pane label="按明细" name="2">
						</el-tab-pane>
					</el-tabs>
				</el-col>
				<el-col :span='12' class="fend">
					<el-button class="default_btn" icon="el-icon-download" size="mini" @click="exportList">导出数据</el-button>
				</el-col>
				<!-- <div class="tab fend">
					<span :class="{text_btn: listType == 1}" @click="changeList(1)">按单据</span>
					<span :class="{text_btn: listType == 2}" @click="changeList(2)">按明细</span>
				</div> -->
			</el-row>
			<!-- 表格数据 -->
			<div :style="{height: tableHeight + 'px'}">
				<el-table :data="tableData" v-if="listType == 1" stripe style="width: 100%" border :max-height="tableHeight" v-loading="loading">
					<el-table-column  show-overflow-tooltip align="center" width="55" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="inventoryCode" min-width="160" label="入库单号">
						<template slot-scope="scope">{{ scope.row.inventoryCode }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="入库类型" min-width="140">
						<template slot-scope="scope">{{ handeltext(scope.row.inventoryType) }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="warehouseName" label="入库仓库">
						<template slot-scope="scope">{{ scope.row.warehouseName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryNum" min-width="140" sortable label="总数量">
						<template slot-scope="scope">{{ scope.row.inventoryNum }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryWeight" min-width="140" sortable label="总重量（kg）">
						<template slot-scope="scope">{{ scope.row.inventoryWeight }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createUserName" label="操作人">
						<template slot-scope="scope">{{ scope.row.createUserName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createTime" min-width="160" sortable label="入库时间">
						<template slot-scope="scope">{{ scope.row.createTime }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip  align="center" label="操作" fixed="right">
						<template slot-scope="scope">
							<el-button class="text_btn" size="mini" icon="el-icon-warning-outline" @click="stockInfo(scope.row)" type="text" >查看</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-table :data="tableData" v-if="listType == 2" stripe style="width: 100%" border :max-height="tableHeight" v-loading="loading">
					<el-table-column  show-overflow-tooltip align="center" width="55" type="index" label="序号"></el-table-column>
					<el-table-column  show-overflow-tooltip align="center" label="入库类型" min-width="140">
						<template slot-scope="scope">{{ handeltext(scope.row.inventoryType) }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="warehouseName" min-width="140" label="入库仓库">
						<template slot-scope="scope">{{ scope.row.warehouseName }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="productName" min-width="160" label="产品名称">
						<template slot-scope="scope">{{ scope.row.productName }}</template>
					</el-table-column>
					<!-- <el-table-column  show-overflow-tooltip prop="butcherCode" label="产品类型"></el-table-column> -->
					<el-table-column  show-overflow-tooltip align="center" prop="specification" min-width="140" label="规格单位">
						<template slot-scope="scope">{{ scope.row.specification }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryNum" min-width="180" sortable label="实际入库数量">
						<template slot-scope="scope">{{ scope.row.inventoryNum }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="right" prop="inventoryWeight" min-width="180" sortable label="实际入库重量（kg）">
						<template slot-scope="scope">{{ scope.row.inventoryWeight }}</template>
					</el-table-column>
					<el-table-column  show-overflow-tooltip align="center" prop="createTime" min-width="160" sortable label="入库时间">
						<template slot-scope="scope">{{ scope.row.createTime }}</template>
					</el-table-column>
				</el-table>
			</div>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				@pagination="getList"
			/>
		</el-card>
		<el-drawer
			class="drawer_box"
			:visible.sync="modelShow" 
			:show-close="true" 
			:append-to-body="true" 
			:destroy-on-close="true"
			size="80%"
			:title='handeltext(currentItem.inventoryType)'
			:wrapperClosable="false">
			<InWarehouseDetail ref="inWarehouseDetail" :dataInfo='currentItem'></InWarehouseDetail>
		</el-drawer>
	</div>
</template>
      
      <script>
import { inventoryPage, inventoryExport, pageProduct, pageProductExport } from "@/api/stock.js";
import { exportExcel } from "@/utils/east";
import { warehouseList } from "@/api/basics/index.js";
import InWarehouseDetail from './components/inWarehouseDetail.vue'
export default {
    data() {
        return {
            queryParams: {
                inventoryCode: "",
                materialsId:'',
                warehouseId:'',
				businessType: 1,
                pageNum: 1,
                pageSize: 10,
				inventoryStatus: 1
            },
            materialsList: [], //原料名称
            warehouseList: [], //原料等级
            tableData: [],
            loading: true,
            dateEnter: [],
            total: 0,
            warehouseTypeList: [
                { text: "急冻入库", value: 11 },
                { text: "成品入库", value: 12 },
                { text: "结余入库", value: 13 },
                { text: "盘盈入库", value: 14 },
            ],
            currentItem: {},
            modelShow: false,
			listType: '1',
            windowHeight: 0,
            tableHeight: 0,
            tableBoxHeight: 0
        };
    },
    components: {
      InWarehouseDetail
    },
    computed: {
        handeltext(){
            return (value)=>{
                let name=''
                this.warehouseTypeList.forEach(item=>{
                    if(item.value==value){
                        name=item.text
                    }
                })
                return name
            }
        }
    },
	watch: {
		listType() {
			this.getList()
		}
	},
    created() {
		this.queryParams.requestRole = this.$route.query.requestRole
        this.getMaterial();
        this.getList();
        this.setTable()
    },
    methods: {
        setTable() {
            this.$nextTick(() => {
                const formBox = this.$el.querySelector('.form_box')
                this.windowHeight = window.innerHeight;
                this.tableHeight = this.windowHeight - formBox.offsetHeight - 240
                if(this.tableHeight < 0) { this.tableHeight = 0 }
                this.tableBoxHeight = this.windowHeight - formBox.offsetHeight - 108
                // min-height: calc(100vh - 50px - 48px) !important;
                window.addEventListener('resize', () => {
                    this.windowHeight = window.innerHeight;
                    this.tableHeight = this.windowHeight - formBox.offsetHeight - 240
                    this.tableBoxHeight = this.windowHeight - formBox.offsetHeight - 108
                    if(this.tableHeight < 0) { this.tableHeight = 0 }
                }); 
            })
        },
    refresh() {
        this.getList();
    },
    getMaterial() {
        warehouseList({
            requestRole: this.$route.query.requestRole,
            pageNum: 1,
            pageSize: 10000,
			warehouseType: -1,
        }).then((res) => {
            if (res.code == 200) {
                this.warehouseList = res.result.list || [];
            }
        });
    },
    //列表查询
    getList() {
		if (this.listType == 1) {
			inventoryPage(this.queryParams).then((res) => {
				if (res.code == 200) {
					this.tableData = res.result.list;
					this.total = Number(res.result.total);
					this.loading = false;
				}
			});
		} else {
			pageProduct(this.queryParams).then((res) => {
				if (res.code == 200) {
					this.tableData = res.result.list;
					this.total = Number(res.result.total);
					this.loading = false;
				}
			});
		}
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
        this.dateEnter = [];
        this.reset();
        this.handleQuery();
    },
    //刷新页面
    refreshList() {
        this.getList();
    },
    handelData(startTime, endTime, list) {
        if (list?.length > 0) {
            this.queryParams[startTime] = list[0];
            this.queryParams[endTime] = list[1];
        } else {
            delete this.queryParams[startTime];
            delete this.queryParams[endTime];
        }
    },
    //搜索
    handleQuery() {
        this.queryParams.pageNum = 1;
        this.handelData("startTime", "endTime", this.dateEnter);
        this.getList();
    },
    stockInfo(row) {
		this.currentItem = row
		this.modelShow = true
    },
	changeList(listType) {
		this.listType = listType
		this.queryParams.pageNum = 1
		this.queryParams.pageSize = 10
        this.getList();
	},
    exportList(){
		if (this.listType == 1) {
			exportExcel(inventoryExport,this.queryParams,'入库记录--按单据')
		} else {
			exportExcel(pageProductExport,this.queryParams,'入库记录--按明细')
		}
	},
  },
};
</script>
      
<style lang="scss" scoped>
    .tab{
		padding: 0 20px;
		color: #333333;
		span{
			margin: 0 10px;
			cursor: pointer;
		}
    }
</style>
      