<template>
    <div class="app-container">
        <el-card class="mb10 box-card form-card" shadow="never">
            <el-row :gutter="10">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  class="form_box">
                    <el-row class=" form_row">
                        <el-row class="form_col">
                            <el-form-item prop="productName">
                                <el-input v-model="queryParams.productName" placeholder="产品名称" clearable />
                            </el-form-item>
                            <el-form-item prop="productCode">
                                <el-input v-model="queryParams.productCode" placeholder="产品编码" clearable />
                            </el-form-item>
                            <el-form-item prop="warehouseId">
                                <el-select v-model="queryParams.warehouseId" placeholder='仓库名称' clearable>
                                <el-option
                                    v-for="(item,index) in warehouseList"
                                    :label="item.warehouseName"
                                    :value="item.warehouseId"
                                    :key="index"
                                />
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="productTypeId">
                                <el-select v-model="queryParams.productTypeId" placeholder='产品类型' clearable>
                                <el-option
                                    v-for="(item,index) in productTypeList"
                                    :label="item.productTypeName"
                                    :value="item.productTypeId"
                                    :key="index"
                                />
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                v-model="dateEnter"
                                style="width: 240px"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="更新开始日期"
                                end-placeholder="更新结束日期"
                                ></el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </el-row>
        </el-card>
        
        <el-card shadow="never" class="table_box card_radius_b">
            <el-row :gutter="10" class="fend mb8 form_btn">
                <el-button class="default_btn" icon="el-icon-download" size="mini" @click="exportList">导出数据</el-button>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading" :max-height="tableHeight">
                    <el-table-column  show-overflow-tooltip type="index" width="55" align="center" label="序号"></el-table-column>
                    <el-table-column  show-overflow-tooltip align="center" prop="productName" label="产品名称"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="productCode" align="center" label="产品编码"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="productTypeName" align="center" label="产品类型"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="specification" align="center" label="规格单位"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="inventoryNum" align="right" sortable label="库存数量"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="inventoryWeight" align="right" min-width="140" sortable label="库存重量（kg）"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="updateTime" align="center" min-width="160" sortable label="更新时间"></el-table-column>
                    <el-table-column  show-overflow-tooltip  label="操作" align="center" fixed="right">
                        <template slot-scope="scope">
                            <el-button class="text_btn" @click="stockInfo(scope.row)" type="text" >库存分布</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <StockModel ref="stockModel" :dataInfo='currentItem'></StockModel>
    </div>
</template>
      
      <script>
import { groupByProduct, groupByProductExport } from "@/api/stock.js";
import { warehouseList, productTypeList } from "@/api/basics/index.js";
import { exportExcel } from "@/utils/east";
import StockModel from './components/stockModel.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                productName: "",
                productCode: '',
                warehouseId:'',
                productTypeId:'',
                pageNum: 1,
                pageSize: 10,
            },
            warehouseList: [], //仓库列表
            productTypeList: [], //产品类型
            tableData: [],
            loading: true,
            dateEnter: [],
            total: 0,
            currentItem: {},
            sreachShow: false,
        };
    },
    components: {
        StockModel
    },
    created() {
		this.queryParams.requestRole = this.$route.query.requestRole
        this.getSelectList();
        this.getList();
    },
    methods: {
        refresh() {
            this.getList();
        },
        getSelectList() {
            warehouseList({
                requestRole: this.$route.query.requestRole,
                pageNum: 1,
                pageSize: 10000,
                warehouseType: -1,
            }).then((res) => {
                if (res.code == 200) {
                    this.warehouseList = res.result.list || [];
                }
            });
            productTypeList({
                pageNum: 1,
                pageSize: 10000,
            }).then((res) => {
                if (res.code == 200) {
                    this.productTypeList = res.result.list || [];
                }
            });
        },
        //列表查询
        getList() {
            groupByProduct(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset(){
            this.resetForm("queryForm");
        },
        //重置
        resetQuery() {
            this.dateEnter = [];
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.dateEnter);
            this.getList();
        },
        stockInfo(row) {
            this.currentItem = row
            this.$refs.stockModel.showModel()
        },
        exportList(){exportExcel(groupByProductExport,this.queryParams,'库存列表')},
    },
};
</script>
      
<style lang="scss" scoped>
</style>
      