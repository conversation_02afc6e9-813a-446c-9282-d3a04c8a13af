<template>
  <el-dialog
    title="仓库分布图"
    :visible.sync="dialogVisible"
    width="80%"
    :destroy-on-close="true"
    class="warehouse-modal"
    @close="handleClose"
  >
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="请输入仓库名称搜索"
        prefix-icon="el-icon-search"
        clearable
        @input="handleSearch"
        class="search-input"
      />
      <div class="legend">
        <div class="legend-item">
          <span class="legend-color high"></span>
          <span class="legend-text">高库存 (>80%)</span>
        </div>
        <div class="legend-item">
          <span class="legend-color medium"></span>
          <span class="legend-text">中等库存 (50-80%)</span>
        </div>
        <div class="legend-item">
          <span class="legend-color low"></span>
          <span class="legend-text">低库存 (<50%)</span>
        </div>
      </div>
    </div>

    <div class="warehouse-grid-container">
      <div v-if="paginatedWarehouses.length > 0" class="warehouse-grid">
        <div
          v-for="warehouse in paginatedWarehouses"
          :key="warehouse.warehouseId"
          class="warehouse-card"
          :class="getWarehouseClass(warehouse.inventoryLevel)"
          @click="handleWarehouseClick(warehouse)"
        >
          <div class="warehouse-name">{{ warehouse.warehouseName }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无匹配的仓库数据"></el-empty>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <pagination
        v-show="filteredWarehouses.length > 0"
        :total="filteredWarehouses.length"
        :page.sync="currentPage"
        :limit.sync="pageSize"
        @pagination="handlePagination"
        :page-sizes="[24, 48, 72, 96]"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'WarehouseDistributionModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    warehouses: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchKeyword: '',
      currentPage: 1,
      pageSize: 24, // 每页显示24个（3行 * 8列）
      filteredWarehouses: []
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    paginatedWarehouses() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredWarehouses.slice(start, end);
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initData();
      }
    },
    warehouses: {
      handler() {
        this.initData();
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      this.filteredWarehouses = [...this.warehouses];
      this.searchKeyword = '';
      this.currentPage = 1;
    },
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredWarehouses = [...this.warehouses];
      } else {
        this.filteredWarehouses = this.warehouses.filter(warehouse =>
          warehouse.warehouseName.toLowerCase().includes(this.searchKeyword.toLowerCase())
        );
      }
      this.currentPage = 1;
    },
    handlePagination({ page, limit }) {
      this.currentPage = page;
      this.pageSize = limit;
    },
    handleWarehouseClick(warehouse) {
      this.$emit('warehouse-click', warehouse);
    },
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    getWarehouseClass(stockLevel) {
      if (stockLevel > 80) return 'high-stock';
      if (stockLevel >= 50) return 'medium-stock';
      return 'low-stock';
    }
  }
};
</script>

<style lang="scss" scoped>
.warehouse-modal {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .search-input {
    width: 300px;
  }
  
  .legend {
    display: flex;
    gap: 20px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        
        &.high {
          background: #F53F3F;
        }
        
        &.medium {
          background: #FF7D00;
        }
        
        &.low {
          background: #00B42A;
        }
      }
      
      .legend-text {
        font-size: 14px;
        color: #4E5969;
      }
    }
  }
}

.warehouse-grid-container {
  height: 400px; /* 固定高度，3行 * 128px + 间距 */
  margin-bottom: 20px;
  overflow-y: auto; /* 超过3行显示滚动条 */
}

.warehouse-grid {
  display: grid;
  grid-template-columns: repeat(8, 120px); /* 固定8列，每列120px */
  gap: 16px;
  justify-content: center; /* 居中显示 */

  .warehouse-card {
    position: relative;
    width: 120px;
    height: 128px; /* 修改为128px高度 */
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.high-stock {
      background: rgba(245, 63, 63, 0.1);
      border: 1px solid rgba(245, 63, 63, 0.3);
      
      &:hover {
        background: rgba(245, 63, 63, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(245, 63, 63, 0.2);
      }
    }
    
    &.medium-stock {
      background: rgba(255, 125, 0, 0.1);
      border: 1px solid rgba(255, 125, 0, 0.3);
      
      &:hover {
        background: rgba(255, 125, 0, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 125, 0, 0.2);
      }
    }
    
    &.low-stock {
      background: rgba(0, 180, 42, 0.1);
      border: 1px solid rgba(0, 180, 42, 0.3);
      
      &:hover {
        background: rgba(0, 180, 42, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 180, 42, 0.2);
      }
    }
    
    .warehouse-name {
      font-size: 16px;
      font-weight: 500;
      color: #1D2129;
      text-align: center;
      margin-bottom: 8px;
    }
    
    .warehouse-level {
      font-size: 14px;
      color: #4E5969;
      font-weight: 400;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #E5E6EB;
}
</style>
