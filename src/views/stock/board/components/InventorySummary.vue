<template>
  <div class="inventory-summary">
    <el-row :gutter="20" class="summary-row">
      <!-- 库存总量 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">库存总量</div>
          <div class="chart-container">
            <div id="inventoryChart" class="chart">
            </div>
            <div class="chart-data">
              <div class="data-item">
                <span class="data-label">入库总数</span>
                <span class="data-value">{{ summaryData.inCnt || 0 }} 条</span>
              </div>
              <div class="data-item">
                <span class="data-label">入库总重量</span>
                <span class="data-value">{{ summaryData.inWeightNum || 0 }} kg</span>
              </div>
              <div class="data-item">
                <span class="data-label">低库龄预警</span>
                <span class="data-value warning">{{ summaryData.lowInventory || 0 }} 条</span>
              </div>
              <div class="data-item">
                <span class="data-label">呆滞料预警</span>
                <span class="data-value">{{ summaryData.stagnantInventory || 0 }} 条</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 批次分布 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">批次分布</div>
          <div class="chart-container">
            <div id="batchChart" class="chart"></div>
            <div class="batch-legend">
              <div
                v-for="(item, index) in batchLegendData"
                :key="index"
                class="legend-item"
              >
                <span
                  class="legend-color"
                  :style="{ background: item.color }"
                ></span>
                <span class="legend-label">{{ item.label }}</span>
                <span class="legend-value">{{ item.value }} %</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'InventorySummary',
  props: {
    summaryData: {
      type: Object,
      default: () => ({})
    },
    batchData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inventoryChart: null,
      batchChart: null,
      chartAvailable: false,
      inventoryConfig: [
        { type: 1, label: '低库龄预警', color: '#165DFF', unit: '条' },
        { type: 2, label: '中库龄库存', color: '#00B42A', unit: '条' },
        { type: 3, label: '高库龄库存', color: '#FF7D00', unit: '条' }
      ],
      batchConfig: [
        { type: 1, label: '近1个月入库', color: '#165DFF' },
        { type: 2, label: '近1-3个月入库', color: '#00B42A' },
        { type: 3, label: '近3-6个月入库', color: '#FF7D00' },
        { type: 4, label: '近6个月以上', color: '#F53F3F' }
      ]
    };
  },

  computed: {
    // 库存总量图表数据
    inventoryChartData() {
      const storeAgeList = this.summaryData.storeAgeList || [];
      return this.inventoryConfig.map(config => {
        const item = storeAgeList.find(store => store.storeAgeType === config.type) || {};
        return {
          value: item.storeAgeNum || 0,
          name: config.label,
          itemStyle: { color: config.color }
        };
      });
    },

    // 批次分布图例数据
    batchLegendData() {
      const batchList = this.batchData.batchList || [];
      return this.batchConfig.map(config => {
        const item = batchList.find(batch => batch.batchType === config.type) || {};
        return {
          color: config.color,
          label: config.label,
          value: Math.round(item.batchPercent || 0)
        };
      });
    },

    // 批次分布图表数据
    batchChartData() {
      const batchList = this.batchData.batchList || [];
      return this.batchConfig.map(config => {
        const item = batchList.find(batch => batch.batchType === config.type) || {};
        return {
          value: item.batchNum || 0,
          name: config.label,
          itemStyle: { color: config.color }
        };
      });
    }
  },

  mounted() {
    this.initCharts();
  },
  watch: {
    summaryData: {
      handler() {
        this.updateInventoryChart();
      },
      deep: true
    },
    batchData: {
      handler() {
        this.updateBatchChart();
      },
      deep: true
    }
  },
  beforeDestroy() {
    this.disposeCharts();
  },
  methods: {
    // 获取ECharts实例
    getEchartsInstance() {
      return echarts || this.$echarts;
    },

    // 初始化所有图表
    initCharts() {
      const echartsInstance = this.getEchartsInstance();
      this.chartAvailable = !!echartsInstance;

      if (this.chartAvailable) {
        this.$nextTick(() => {
          this.initInventoryChart();
          this.initBatchChart();
        });
      }
    },

    // 清理所有图表实例
    disposeCharts() {
      [this.inventoryChart, this.batchChart].forEach(chart => {
        if (chart) {
          chart.dispose();
        }
      });
    },



    // 初始化库存图表
    initInventoryChart() {
      const chartDom = document.getElementById('inventoryChart');
      if (!chartDom) return;

      const echartsInstance = this.getEchartsInstance();
      if (!echartsInstance) return;

      if (this.inventoryChart) {
        this.inventoryChart.dispose();
      }

      try {
        this.inventoryChart = echartsInstance.init(chartDom);
        this.updateInventoryChart();
      } catch (error) {
        console.error('初始化库存图表失败:', error);
      }
    },

    // 更新库存图表
    updateInventoryChart() {
      if (!this.inventoryChart) return;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}：{c} 条'
        },
        series: [{
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          data: this.inventoryChartData,
          label: { show: false },
          emphasis: { disabled: false }
        }]
      };

      this.inventoryChart.setOption(option);
    },

    // 初始化批次图表
    initBatchChart() {
      const chartDom = document.getElementById('batchChart');
      if (!chartDom) return;

      const echartsInstance = this.getEchartsInstance();
      if (!echartsInstance) return;

      if (this.batchChart) {
        this.batchChart.dispose();
      }

      try {
        this.batchChart = echartsInstance.init(chartDom);
        this.updateBatchChart();
      } catch (error) {
        console.error('初始化批次图表失败:', error);
      }
    },

    // 更新批次图表
    updateBatchChart() {
      if (!this.batchChart) return;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}：{c} 条'
        },
        series: [{
          type: 'pie',
          radius: '70%',
          center: ['50%', '50%'],
          data: this.batchChartData,
          label: { show: false },
          emphasis: { disabled: false }
        }]
      };

      this.batchChart.setOption(option);
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-summary {
  .summary-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    height: 300px;
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #165DFF;
      margin-bottom: 20px;
    }
    
    .chart-container {
      display: flex;
      height: 240px;
      
      .chart {
        flex: 1;
        height: 100%;
        position: relative;

        .chart-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .placeholder-circle {
            width: 120px;
            height: 120px;
            border: 20px solid #165DFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .placeholder-inner {
              text-align: center;

              .placeholder-text {
                font-size: 12px;
                color: #4E5969;
                margin-bottom: 4px;
              }

              .placeholder-value {
                font-size: 18px;
                font-weight: 500;
                color: #165DFF;
              }
            }
          }

          .placeholder-pie {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #165DFF 25%, #00B42A 25% 50%, #FF7D00 50% 75%, #F53F3F 75%);
            display: flex;
            align-items: center;
            justify-content: center;

            .pie-text {
              background: white;
              padding: 8px 12px;
              border-radius: 4px;
              font-size: 12px;
              color: #4E5969;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
          }
        }
      }
      
      .chart-data {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        padding-right: 80px;
        
        .data-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .data-label {
            font-size: 14px;
            color: #4E5969;
          }
          
          .data-value {
            font-size: 16px;
            font-weight: 500;
            color: #165DFF;
            
            &.warning {
              color: #FF7D00;
            }
          }
        }
      }
      
      .batch-legend {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        padding-right: 80px;
        
        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 12px;
          }
          
          .legend-label {
            flex: 1;
            font-size: 14px;
            color: #4E5969;
          }
          
          .legend-value {
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
          }
        }
      }
    }
  }
}
</style>
