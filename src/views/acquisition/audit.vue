<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
      <el-row :gutter="10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  class="form_box">
          <el-row class=" form_row">
            <el-row class="form_col">
              <el-form-item prop="companyName">
                <el-input v-model="queryParams.companyName" placeholder="企业名称" clearable />
              </el-form-item>
              <el-form-item prop="purchasePlanCode">
                <el-input v-model="queryParams.purchasePlanCode" placeholder="采购编码" clearable />
              </el-form-item>
              <!-- <el-form-item label="审核人" prop="updateUserName">
                <el-input v-model="queryParams.updateUserName" placeholder="请输入审核人" clearable />
              </el-form-item> -->
              <el-form-item prop="planStatus">
                <el-select v-model="queryParams.planStatus" placeholder="状态" @change="seleTab">
                  <el-option
                    v-for="(item, index) in status"
                    :key="index"
                    :label="item.text"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="审核开始日期"
                  end-placeholder="审核结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-row>
          </el-row>
          <el-row>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <template v-if="toggleSearchDom">
                    <el-button type="text" @click="packUp">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                </template>
            </el-form-item>
          </el-row>
        </el-form>
      </el-row>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row class="mb8 form_btn">
        <el-col class="tabs-box">
          <el-tabs class="mb20" v-model="queryParams.auditStatus" type="card" @tab-click="handleClick">
            <el-tab-pane label="待审核" name="2"></el-tab-pane>
            <el-tab-pane label="已审核" name="4"></el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading"
                :max-height="tableHeight">
          <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="companyName" label="企业名称" min-width="140" />
          <el-table-column show-overflow-tooltip align="center" prop="purchasePlanCode" label="收购价编码" min-width="140" />
          <el-table-column show-overflow-tooltip align="center" prop="effectiveTime" label="生效日期" min-width="140"  sortable></el-table-column>
          <el-table-column prop="planStatus" label="状态" align="center" min-width="140" >
              <template slot-scope="scope">
                  <span v-show="scope.row.auditStatus==1||scope.row.auditStatus==0">{{ formatStatus(scope.row.planStatus,status) }}</span>
                  <span v-show="scope.row.auditStatus==2||scope.row.auditStatus==3">{{ formatStatus(scope.row.auditStatus,auditStatusList) }}</span>
              </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip v-if="queryParams.auditStatus == 2" align="center" min-width="140" prop="createUserName" label="创建人"></el-table-column>
          <el-table-column show-overflow-tooltip v-if="queryParams.auditStatus == 2" align="center" min-width="140"  prop="createTime" label="维护时间" sortable></el-table-column>
          <el-table-column show-overflow-tooltip v-if="queryParams.auditStatus == 4" align="center" min-width="140" prop="updateUserName" label="审核人"></el-table-column>
          <el-table-column show-overflow-tooltip v-if="queryParams.auditStatus == 4" align="center" min-width="140"  prop="updateTime" label="审核时间" sortable></el-table-column>
          <el-table-column label="操作" align="center" min-width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                :icon="scope.row.auditStatus == 2 ? 'el-icon-s-check' : 'el-icon-warning-outline'"
                size="mini"
                @click="handleDetails(scope.row.purchasePlanId)"
                type="text"
              >{{scope.row.auditStatus == 2 ? '审核' : '详情'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 详情 -->
    <el-drawer 
      class="drawer_box drawer_box1"
      title="采购价查看" 
      :visible.sync="drawerOpen" 
      :show-close="true" 
      :append-to-body="true" 
      :modal="true"
      :destroy-on-close="true"
      size="80%"
      :wrapperClosable="false">
        <modelAudit ref="modelInfo" @refresh="refresh" @getList="getList"></modelAudit>
    </el-drawer>
  </div>
</template>
    <script>
import {
  purchasePlanList,
  purchasePlanClose,
} from "@/api/acquisition/index.js";
import modelAudit from "./components/modelAudit.vue";
import {mapState} from 'vuex'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  components: {
    modelAudit,
  },
  data() {
    return {
      drawer: {
        open: false,
        title: "价格维护",
      },
      drawerOpen: false,
      status: [
        { text: "已失效", value: 0 },
        { text: "生效中", value: 1 },
        { text: "待生效", value: 2 },
        { text: "待审核", value: 3 },
        { text: "已驳回", value: 4 },
      ],
      auditStatusList:[
        { text: "驳回", value: 3 },
        { text: "待审核", value: 2 },
      ],
      queryParams: {
        auditStatus: "2",
        companyName: "",
        purchasePlanCode: "",
        updateUserName:'',
        planStatus: "",
        platformId:'',
        pageNum: 1,
        pageSize: 10,
        tenantId:''
      },
      dateRange: [],
      tableData: [],
      loading: true,
      total: 0,
    };
  },
  computed: {
    ...mapState({
        platformId:(state)=>state.user.user.platformId
    }),
    formatStatus() {
      return ( val,list) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == val) {
            name = item.text;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getList();
  },
  methods: {
    seleTab() {
      if (this.queryParams.planStatus == 3) {
        this.queryParams.auditStatus = "2";
      }else{
        this.queryParams.auditStatus = "4";
      }
      
      this.getList();
    },

    handleClick({ name }) {
      this.queryParams.auditStatus = name;
      this.getList();
    },
    refresh() {
      this.getList();
      this.drawerOpen = false
    },
    //列表查询
    getList() {
        this.queryParams.platformId=this.platformId
      purchasePlanList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },

    //重置
    resetQuery() {
      this.dateRange = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange?.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        delete this.queryParams.startTime;
        delete this.queryParams.endTime;
      }
      this.getList();
    },
    /** 查看详情按钮操作 */
    handleDetails(id) {
      this.drawerOpen = true
      this.$nextTick(() => {
        this.$refs.modelInfo.id = id;
        this.$refs.modelInfo.getInfo();
      })
    },
  },
};
</script>
<style lang="scss">
.drawer_box1{
    .el-drawer__body{
        padding: 0 !important;
    }
}
</style>
  