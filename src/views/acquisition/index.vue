<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
          <el-row class=" form_row">
            <el-row class="form_col">
                <el-form-item prop="purchasePlanCode">
                  <el-input v-model="queryParams.purchasePlanCode" placeholder="采购编码" clearable />
                </el-form-item>
                <!-- <el-form-item label="维护人" prop="updateUserName">
                  <el-input v-model="queryParams.updateUserName" placeholder="请输入维护人" clearable />
                </el-form-item> -->
                <el-form-item prop="planStatus">
                  <el-select v-model="queryParams.planStatus" placeholder="状态">
                    <el-option
                      v-for="(item, index) in status"
                      :key="index"
                      :label="item.text"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item prop="effectiveTime">
                  <el-date-picker
                    v-model="dateRangeeffectiveTime"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="生效开始日期"
                    end-placeholder="生效结束日期"
                  ></el-date-picker>
                </el-form-item>

                <el-form-item>
                  <el-date-picker
                    v-model="dateRange"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="维护开始日期"
                    end-placeholder="维护结束日期"
                  ></el-date-picker>
                </el-form-item>
            </el-row>
          </el-row> 
          <el-row>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <template v-if="toggleSearchDom">
                    <el-button type="text" @click="packUp">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                </template>
            </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="fend mb8 form_btn">
        <el-button size="mini" icon="el-icon-plus" class="default_btn" @click="addFn()">新 建</el-button>
        <el-button size="mini" class="default_btn" @click="purchasePlanAdd('2')">规格管理</el-button>
        <el-button plain icon="el-icon-download" class="default_btn" size="mini" @click="exportData" >导出数据</el-button>
      </el-row>
      <!-- <el-tabs v-model="queryParams.auditStatus" type="card" @tab-click="handleClick">
        <el-tab-pane label="审核通过" name="1"></el-tab-pane>
        <el-tab-pane label="审核中" name="2"></el-tab-pane>
        <el-tab-pane label="审核未通过" name="3"></el-tab-pane>
      </el-tabs> -->
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" style="width: 100%" border v-loading="loading"
                :max-height="tableHeight">
          <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
          <el-table-column show-overflow-tooltip align="center" prop="purchasePlanCode" min-width="120" label="采购价编码" />
          <el-table-column show-overflow-tooltip prop="planStatus" label=" 原料类型" min-width="120" align="center" >
              <template slot-scope="scope">
                  <span>{{ formatStatus(scope.row.materialsType,materialsTypeList) }}</span>
              </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltipprop="planStatus" label="状态" align="center" min-width="120" >
              <template slot-scope="scope">
                  <span v-show="scope.row.auditStatus==1||scope.row.auditStatus==0">{{ formatStatus(scope.row.planStatus,status) }}</span>
                  <span v-show="scope.row.auditStatus==2||scope.row.auditStatus==3">{{ formatStatus(scope.row.auditStatus,auditStatusList) }}</span>
              </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="effectiveTime" align="center" min-width="140" label="生效日期" sortable></el-table-column>
          <el-table-column show-overflow-tooltip prop="expiredTime" align="center" min-width="140" label="截止日期" sortable></el-table-column>
          <el-table-column min-width="120" label="是否同步平台" align="center">
            <template slot-scope="scope">
                  <span>{{ scope.row.planPublicFlag ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="createUserName" align="center" label="创建人"></el-table-column>
          <el-table-column show-overflow-tooltip prop="createTime" align="center" min-width="140" label="维护时间" sortable></el-table-column>
          <el-table-column label="操作" fixed="right" width="160">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-warning-outline"
                size="mini"
                @click="handleDetails(scope.row.purchasePlanId)"
                type="text"
                class="text_btn"
              >查看</el-button>
              <el-button
                  v-show="scope.row.auditStatus==1&&scope.row.planStatus!=0"
                icon="el-icon-delete-solid"
                class="delete_text_btn"
                @click="delData(scope.row.purchasePlanId,scope.row.appointmentFlag)"
                type="text"
                size="mini"
              >失效</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 详情 -->
    <el-drawer 
      class="drawer_box drawer_box1"
      title="采购计划查看" 
      :visible.sync="showInfo" 
      :show-close="true" 
      :append-to-body="true" 
      :modal="true"
      :destroy-on-close="true"
      size="80%"
      :wrapperClosable="false">
        <modelInfo ref="modelInfo" @getList="getList"></modelInfo>
    </el-drawer>
    <el-drawer 
      class="drawer_box"
      :title="drawer.title" 
      :visible.sync="drawer.open" 
      :show-close="true" 
      :append-to-body="true" 
      :modal="true"
      :destroy-on-close="true"
      size="80%"
      :wrapperClosable="false">
      <!-- <modelPricePlan  @close="close" @refreshList="refreshList"></modelPricePlan> -->
      <!-- 规格 -->
      <modelStandard v-if='drawer.type==2' @close="close" @refreshList="refreshList"></modelStandard>
    </el-drawer>
    <el-drawer 
      class="drawer_box drawer_box1"
      title="新建采购计划" 
      :visible.sync="drawerShow" 
      :show-close="true" 
      :append-to-body="true" 
      :modal="true"
      :destroy-on-close="true"
      size="80%"
      :wrapperClosable="false">
      <!-- 价格 -->
      <PlanForm @close="close" @getList="refreshList"></PlanForm>
    </el-drawer>
  </div>
</template>
  <script>
  import {exportExcel} from '@/utils/east-mind.js'
  import modelPricePlan from './components/modelPricePlan.vue'
  import modelStandard from './components/modelStandard.vue'
  import PlanForm from './components/planForm.vue'
import {
  purchasePlanList,
  purchasePlanClose,
  purchasePlanExport
} from "@/api/acquisition/index.js";
import modelInfo from "./components/modelInfo.vue";
import {mapState} from 'vuex'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  components: {
    modelInfo,
    modelPricePlan,
    modelStandard,
    PlanForm
  },
  data() {
    return {
      drawer:{
        type:'1',//1表示价格2表示规格
        open:false,
        title:'新建采购计划'
      },
      status: [
        { text: "已失效", value: 0 },
        { text: "生效中", value: 1 },
        { text: "待生效", value: 2 },
        { text: "待审核", value: 3 },
        { text: "已驳回", value: 4 },
      ],
      auditStatusList:[
        { text: "已驳回", value: 3 },
        { text: "待审核", value: 2 },
      ],
      materialsTypeList:[
        { text: "羊", value: 1 },
        { text: "牛", value: 2 },
      ],
      queryParams: {
        purchasePlanCode: "",
        effectiveTime: "",
        purchasePlanCode: "",
        // planStatus: "",
        updateUserName: "",
        tenantId:"",
        requestRole: 2,
        pageNum: 1,
        pageSize: 10,
      },
      dateRange: [],
      dateRangeeffectiveTime: [],
      tableData: [],
      loading: true,
      total: 0,
      drawerShow: false,
      showInfo: false,
    };
  },
  computed: {
    ...mapState({
        tenantId:(state) => state.user.user.tenantId,
    }),
    formatStatus() {
      return (val,list) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == val) {
            name = item.text;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getList();
  },
  methods: {
    //导出数据
    exportData(){
        exportExcel(purchasePlanExport,this.queryParams,'采购价管理')
    },
    close(){
        this.drawer.open=false
        this.drawerShow = false
    },
    //价格维护
    purchasePlanAdd(type){
        this.drawer.title='规格管理'
        this.drawer.type=type
        this.drawer.open=true
    },
    addFn() {
      this.drawerShow = true
    },
    handleClick({ name }) {
      this.queryParams.auditStatus = name;
      this.getList();
    },
    seleTab(){
      // if( this.queryParams.planStatus==3){
      //   this.queryParams.auditStatus='2'
      // }if(this.queryParams.planStatus==4){
      //   this.queryParams.auditStatus='3'
      // }if(this.queryParams.planStatus!=3&&this.queryParams.planStatus!=4){
      //   this.queryParams.auditStatus='1'
      // }
      this.getList();
    },
    //列表查询
    getList() {
        this.queryParams.tenantId=this.tenantId
      purchasePlanList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },

    //重置
    resetQuery() {
      this.dateRange = [];
      this.dateRangeeffectiveTime = []
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
      this.drawerShow = false;
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange?.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        delete this.queryParams.startTime;
        delete this.queryParams.endTime;
      }
      if (this.dateRangeeffectiveTime?.length > 0) {
        this.queryParams.startTime2 = this.dateRangeeffectiveTime[0];
        this.queryParams.endTime2 = this.dateRangeeffectiveTime[1];
      } else {
        delete this.queryParams.startTime2;
        delete this.queryParams.endTime2;
      }
      
      this.getList();
    },
    /** 查看详情按钮操作 */
    handleDetails(id) {
      this.showInfo = true
      this.$nextTick(() => {
        this.$refs.modelInfo.id = id;
        this.$refs.modelInfo.getInfo();
      })
    },
    //删除appointmentFlag 0表示无1表示有
    delData(id,appointmentFlag) {
        let title='是否手动变更价格状态为已失效？变更后不可恢复'
        // appointmentFlag==0?title='是否手动变更价格状态为已失效？变更后不可恢复':title='该采购计划有关联的预约信息，不可变更为失效状态，请提前沟通牧民取消预约'
      this.$confirm(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // if(appointmentFlag==0){
            purchasePlanClose({ purchasePlanId: id }).then((res) => {
              if (res.code == 200) {
                this.getList();
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
              }
            });
        // }
      });
    },
  },
};
</script>
<style lang="scss">
.drawer_box1{
    .el-drawer__body{
        padding: 0 !important;
    }
}
</style>
  