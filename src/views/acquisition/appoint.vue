<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
      <el-row :gutter="10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
          <el-row class=" form_row">
            <el-row class="form_col">
              <el-form-item prop="appointmentCode">
                <el-input v-model="queryParams.appointmentCode" placeholder="预约编号" clearable />
              </el-form-item>
              <el-form-item prop="supplierContactName">
                <el-input v-model="queryParams.supplierContactName" placeholder="姓名" clearable />
              </el-form-item>
              <el-form-item prop="supplierContactPhone">
                <el-input v-model="queryParams.supplierContactPhone" placeholder="联系电话" clearable />
              </el-form-item>
            </el-row>
          </el-row>
          <el-row>
            <el-form-item label=" ">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </el-row>
    </el-card>
    <el-card shadow="never" class="table_box">
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading"
            :max-height="tableHeight">
          <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
          <el-table-column
            v-for="(item,index) in tableColumn"
            :key="index" show-overflow-tooltip
            :prop="item.prop"
            :label="item.label"
            :align="item.align"
            :sortable="item.sortable"
            :width="item.width"
          ></el-table-column>
          <!-- <el-table-column label="计划编号">
              <template slot-scope="scope">
                  <span style="color: #409EFF; cursor: pointer;" @click="goPlan(scope.row.purchasePlanCode )">{{ scope.row.purchasePlanCode }}</span>
              </template>
          </el-table-column> -->
          <!-- <el-table-column >
              <template slot-scope="scope">
                  <el-button
                icon="el-icon-warning-outline"
                size="mini"
                @click="goPlan(scope.row.purchasePlanId)"
                type="text"
                class="text_btn"
              >查看</el-button>
              </template>
          </el-table-column> -->
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <modelAppoint ref="modelAppoint"></modelAppoint>
    <!-- <modelInfo ref="modelInfo"></modelInfo> -->
    <!-- 详情 -->
    <el-drawer 
      class="drawer_box"
      title="预约单查看" 
      :visible.sync="showInfo" 
      :show-close="true" 
      :append-to-body="true" 
      :modal="true"
      :destroy-on-close="true"
      size="80%"
      :wrapperClosable="false">
        <modelInfo ref="modelInfo"></modelInfo>
    </el-drawer>
  </div>
</template>
      
      <script>
import { appointList } from "@/api/acquisition/index";
import modelAppoint from './components/modelAppoint.vue'
import modelInfo from './components/modelInfo.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    components:{
        modelAppoint,
        modelInfo
    },
  data() {
    return {
      tableColumn: [
        { check: true, prop: "appointmentCode", label: "预约编号", align: 'center' },
        { check: true, prop: "supplierContactName", label: "姓名", align: 'center' },
        { check: true, prop: "supplierContactPhone", label: "联系电话", align: 'center' },
        { check: true, prop: "appointmentDate", label: "预约入厂日期", align: 'center' },
        { check: true, prop: "appointmentTimeName", label: "时段", align: 'center' },
        { check: true, prop: "materialsTypeName", label: "原料类型", align: 'center' },
        { check: true, prop: "appointmentStatusName", label: "状态", align: 'center' },
        { check: true, prop: "createTime", label: "预约时间", align: 'center', width: 140, sortable: true },
        // { check: true, prop: "purchasePlanCode", label: "计划编号" },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestRole: 2,
        supplierContactName: '',
        appointmentCode: '',
        supplierContactPhone: '',
      },
      appointmentTimeHash: {
        1: '上午',
        2: '下午',
      },
      materialsTypeHash: {
        1: '羊',
        2: '牛',
      },
      tableData: [],
      loading: true,
      total: 0,
      showInfo: false,
      tableHeight: 0,
    };
  },

  created() {
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    getList() {
      appointList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list.map(item => {
            item.appointmentTimeName = this.appointmentTimeHash[item.appointmentTime]
            item.materialsTypeName = this.materialsTypeHash[item.materialsType]
            switch(item.appointmentStatus) {
              case 0: 
                item.appointmentStatusName = '已取消';
              break;
              case 1: 
                item.appointmentStatusName = '已入厂';
              break;
              case 2: 
                item.appointmentStatusName = '待入厂';
              break;
              case 3: 
                item.appointmentStatusName = '已爽约';
              break;
            }
            return item
          });
          this.total = Number(res.result.total);
        }
        this.loading = false;
      });
    },
    //重置
    resetQuery() {
      this.dateRange = [];
      this.reset();
      this.handleQuery();
    },
    reset() {
      this.resetForm("queryForm");
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //查看详情
    handleDetails(){
        this.$refs.modelAppoint.dialogVisible=true
    },
    //采购计划详情
    goPlan(id){
      this.showInfo = true
      this.$nextTick(() => {
        this.$refs.modelInfo.id = id;
        this.$refs.modelInfo.getInfo();
      })
    }
  },
};
</script>
      
      <style lang="scss" scoped>
</style>
      