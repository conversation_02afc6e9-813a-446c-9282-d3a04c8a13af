<template>
  <div style="padding-bottom: 20px">
    <div style="border-bottom: 10px solid #F5F7FA;padding-left: 30px;">
        <el-descriptions class="" :title="'采购计划编码：' + info.purchasePlanCode" :column="3" size="medium" border>
          <template slot="extra" v-if="info.auditStatus==1||info.auditStatus==0">
                <img class="img" v-if="info.planStatus == 0" src="../../../assets/images/yishixiao.png" alt="" srcset="">
                <img class="img" v-if="info.planStatus == 1" src="../../../assets/images/shengxiaozhong.png" alt="" srcset="">
                <img class="img" v-if="info.planStatus == 2" src="../../../assets/images/daishengxiao.png" alt="" srcset="">
            </template>
            <template slot="extra" v-if="info.auditStatus==2||info.auditStatus==3">
                <img class="img" v-if="info.auditStatus == 2" src="../../../assets/images/daishenhe.png" alt="" srcset="">
                <img class="img" v-if="info.planStatus == 3" src="../../../assets/images/yibohui.png" alt="" srcset="">
            </template>
        </el-descriptions>
    </div>
    <div class="sync">
        <div class="sync-box">
          <div>
            <div class="swich" v-if="isEdit || !planPublicFlag">
                  <label>同步到畜牧帮</label>
                    <el-switch
                    active-color="#12AE63"
                    inactive-color="#DCDCDC"
                    :disabled="isEdit ? false : true"
                    v-model="planPublicFlag"></el-switch>
              </div>
              <div class="swich" v-else>已同步到畜牧帮</div>
              <div class="tips">同步计划到畜牧帮平台，需由平台进行审核，审核通过后，牧民可以在畜牧帮查看、预约已经生效的采购计划</div>
              <el-checkbox class="mt20" :disabled="isEdit ? false : true" v-model="pricePublicFlag" label="同步时隐藏采购价"></el-checkbox>
          </div>
          <div>
            <el-button size="mini" type="text" class="edit_text_btn" v-if="!isEdit && info.planStatus == 1" @click="isEdit = true">编辑</el-button>
            <el-button size="mini" type="text" class="grey_text_btn" v-if="isEdit" @click="cancelEdit">取消</el-button>
            <el-button size="mini" type="text" class="text_btn" v-if="isEdit" @click="changeFlag">确认</el-button>
          </div>
        </div>

    </div>
    <div style="padding: 0 20px 0 56px">
      <div class="point_icon">
        <span>入厂信息</span>
      </div>
      <el-descriptions :contentStyle='{width: "250px"}' :column="3" size="medium" border>
          <el-descriptions-item label='采购原料：'>{{ materialsTypeHash[info.materialsType] }}</el-descriptions-item>
          <el-descriptions-item label='生效日期：'>{{ info.effectiveTime }}</el-descriptions-item>
          <el-descriptions-item label='截至日期：'>{{ info.expiredTime }}</el-descriptions-item>
          <el-descriptions-item label='负责人：'>{{ info.companyContactName }}</el-descriptions-item>
          <el-descriptions-item label='联系电话：'>{{ info.companyContactPhone }}</el-descriptions-item>
          <el-descriptions-item label='创建人：'>{{ info.createUserName }}</el-descriptions-item>
          <el-descriptions-item label='备注：'>{{ info.remark }}</el-descriptions-item>
        </el-descriptions>

      <div class="fbc">
        <div class="point_icon" style="margin-top:30px">
          <span>采购价格</span>
        </div>
        <div style="color:#F85300; font-size: 12px;" v-show="info.standardFlag">* 标准外拒收</div>
      </div>
      <div v-for="(item, index) in info.detailGroupList" :key="index">
        <div class="table-title mt20">
            <div class="drop"></div>
            <label class="plan-title">{{item.materialsName}}</label>
            <div class="plan-num">
                <label>每日计划采购数量：</label>
                <span style="color: #F85300">{{ item.purchaseNum }}</span>
                {{ item.materialsType == 1 ? '只' : '头' }}
            </div>
        </div>
        <el-row  class="mt20">
          <el-table :data="item.planDetailList" stripe style="width: 100%">
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column prop="materialsLevel" align="center" label="胴体等级"/>
            <el-table-column prop="weightSection" align="center" label="单只重量区间(kg)"></el-table-column>
            <el-table-column prop="weightingStandard" align="center" label="过秤标准"></el-table-column>
            <el-table-column prop="unitPrice" align="center" label="价格(元/kg)"></el-table-column>
          </el-table>
        </el-row>
      </div>
      <div v-show="info.auditStatus!=2">
        <div class="point_icon">
          <span>审核信息</span>
        </div>
        <div>
        <el-descriptions :contentStyle='{width: "250px"}' :column="2" size="medium" border>
            <el-descriptions-item label='审核时间：'>{{ info.updateTime }}</el-descriptions-item>
            <el-descriptions-item label='审核结果：'>{{ info.auditStatus==1?'审核通过':( info.auditStatus==3?'驳回':'') }}</el-descriptions-item>
            <el-descriptions-item label='审核说明：'>{{ info.reason }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
      </div>
  </div>
</template>

<script>
import { purchasePlanInfo, publicFlag } from "@/api/acquisition/index.js";
export default {
  data() {
    return {
      dialogVisible: false,
      id: "",
      info: {},
      type: [
        { text: "羊", value: 1 },
        { text: "牛", value: 2 },
      ],
      materialsTypeHash: {
        1: '羊',
        2: '牛'
      },
      pricePublicFlag: '',
      planPublicFlag: '',
      isEdit: false
    };
  },
  computed: {
    formatStatus() {
      return (row, com, val) => {
        let name = "";
        this.type.forEach((item) => {
          if (item.value == val) {
            name = item.text;
          }
        });
        return name;
      };
    },
  },

  mounted() {},

  methods: {
    getInfo() {
      purchasePlanInfo({ purchasePlanId: this.id }).then((res) => {
        if (res.code == 200) {
          this.info = res.result;
          this.planPublicFlag = this.info.planPublicFlag ? true : false
          this.pricePublicFlag = this.info.pricePublicFlag ? true : false
          this.dialogVisible = true;
        }
      });
    },
    changeFlag() {
      if (!this.planPublicFlag) {
        this.$confirm(
          `
              <div style='font-size:18px'>
                  <i class='el-icon-warning' style='color:#FF9900'></i>
                  取消同步到畜牧帮？
              </div>
              <div style='padding-left:22px'>取消同步后，畜牧帮将不再展示采购信息 </div>
          `,
          "提示",
          {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              dangerouslyUseHTMLString: true,
          }
        ).then(() => {
            this.continueFlag = 1
            this.changeStatus()
        })
      } else {
        this.$confirm(
          `
              <div style='font-size:18px'>
                  <i class='el-icon-warning' style='color:#FF9900'></i>
                  确定同步到畜牧帮？
              </div>
              <div style='padding-left:22px'>
                  ${this.info.reason ? '同步后，畜牧帮将展示生效中的采购信息' : '平台审核通过后牧民可在畜牧帮浏览、预约生效中的采购计划'}
              </div>
          `,
          "提示",
          {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              dangerouslyUseHTMLString: true,
          }
        ).then(() => {
            this.changeStatus()
        })
      }
    },
    cancelEdit() {
      this.planPublicFlag = this.info.planPublicFlag ? true : false
      this.pricePublicFlag = this.info.pricePublicFlag ? true : false
      this.isEdit = false
    },
    changeStatus() {
      publicFlag({
        purchasePlanId: this.info.purchasePlanId,
        planPublicFlag: this.planPublicFlag ? 1 : 0,
        pricePublicFlag: this.pricePublicFlag ? 1 : 0,
        }).then(res => {
            this.getInfo()
            this.$message.success('修改成功')
            this.isEdit = false
            this.$emit('getList')
        })
    },
  },
};
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.rectangle {
  width: 8px;
  height: 16px;
  background: #1890ff;
  margin-right: 5px;
}
.title {
  font-size: 16px;
  font-weight: 700;
}
.sync{
    padding-left: 30px;
    padding-right: 20px;
    margin-top: 10px;
    .sync-box{
        height: 100px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        padding-left: 10px;
        padding-right: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .swich{
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #333333;
            label{
                margin-right: 10px;
            }
            .el-form-item{
                margin: 0 !important;
            }
        }
        .tips{
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #999999;
            line-height: 22px;      
        }
    }
}
.table-title{
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #333333;
    line-height: 22px;
    .drop{
        width: 6px;
        height: 6px;
        background: #5672FA;
        border-radius: 100px 100px 100px 100px;
        margin-right: 8px;
    }
    .plan-title{
        min-width: 160px;
    }
    .plan-num{
        display: flex;
        align-items: center;
        label{
            margin-right: 10px;
        }
        .el-form-item{
            margin: 0 !important;
            width: 300px;
        }
    }
}
</style>
<style lang="scss">
.sync-box{
    .el-checkbox__label,.el-checkbox__input.is-checked + .el-checkbox__label{
        color: #333333 !important;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background: #5672FA;
        border-color: #5672FA;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner{
        border-color: #5672FA;
    }
}
</style>