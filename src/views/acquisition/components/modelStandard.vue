<template>
  <div class="modelStandard mt20">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
      <el-form-item label="原料类型" prop="materialsType" :rules="rules.materialsType" >
        <el-radio-group v-model="ruleForm.materialsType" size="mini" @change="selectList" >
          <el-radio-button label="1">羊</el-radio-button>
          <el-radio-button label="2">牛</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-table :data="ruleForm.tableData" style="width: 100%">
        <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
        <el-table-column align="center">
          <template slot="header">
            <span>
              原料名称
              <span style="color:red">*</span>
            </span>
          </template>
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.materialsId'"
              :rules="rules.materialsId"
            >
              <el-select
                v-model="scope.row.materialsId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词"
                :remote-method="remoteMethodName"
              >
                <el-option
                  v-for="item in optionsName"
                  :key="item.materialsId"
                  :label="item.materialsName"
                  :value="item.materialsId"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template slot="header">
            <span>
              等级
              <span style="color:red">*</span>
            </span>
          </template>
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.materialsLevelId'"
              :rules="rules.materialsLevelId"
            >
              <el-select
                v-model="scope.row.materialsLevelId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词"
                :remote-method="remoteMethodLevel"
              >
                <el-option
                  v-for="item in optionsLevel"
                  :key="item.materialsLevelId"
                  :label="item.levelName"
                  :value="item.materialsLevelId"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template slot="header">
            <span>
              单只重量区间(kg)
              <span style="color:red">*</span>
              <el-tooltip class="item" effect="dark" content="填写时需注意重量区间不重叠" placement="top-start">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </span>
          </template>
          <template slot-scope="scope">
            <row class="fcc">
              <el-col :span="11">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.weightStart'"
                  :rules="rules.weightStart"
                >
                  <el-input type="number"  v-model="scope.row.weightStart" placeholder="请输入内容" maxlength="6"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2">-</el-col>
              <el-col :span="11">
                  <el-form-item
                  :prop="'tableData.' + scope.$index + '.weightEnd'"
                  :rules="rules.weightEnd"
                >
                  <el-input type="number" v-model="scope.row.weightEnd" placeholder="请输入内容" maxlength="6"></el-input>
                </el-form-item>
              </el-col>
            </row>
          </template>
        </el-table-column>
        <el-table-column label="过秤标准" width="180">
          <template slot-scope="scope">
            <el-form-item>
              <el-input v-model="scope.row.weightingStandard" placeholder="请输入内容"></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-delete"
              class="delete_text_btn"
              @click="delFrom(scope.$index)"
              type="text"
              size="mini"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        icon="el-icon-plus"
        @click="addFrom"
        style="width:100% ;color:rgb(64, 158, 255);"
      >新增</el-button>
      <!-- <el-form-item>
         <el-checkbox v-model="ruleForm.standardFlag">
            <span style="color:rgb(245, 154, 35)">注：标准外拒收</span>
         </el-checkbox>
      </el-form-item> -->
    </el-form>
    <div class="footer_btn fcc">
      <el-button type="primary" size="mini" @click="submitForm">提交</el-button>
      <el-button @click="close" size="mini">取消</el-button>
    </div>
  </div>
</template>

<script>
import {
  materialsList,
  materialsLevelList,
  purchaseStandardAdd,
  purchaseStandardList,
} from "@/api/acquisition/index.js";
export default {
  name: "EastMindMesappUiModelStandard",
  data() {
    var numVerification=(rule, value, callback)=>{
        if (!( /^\d+(\.\d{1,2})?$/.test(value))) {
        callback(new Error("小数位不能超过两位"));
      } else {
        callback();
      }
    }
    return {
      optionsName: [],
      optionsLevel: [],
      rules: {
        // materialsType: [
        //   {
        //     required: true,
        //     message: "请选择原料类型",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        materialsId: [
          {
            required: true,
            message: "请选择原料名称",
            trigger: ["blur", "change"],
          },
        ],
        materialsLevelId: [
          {
            required: true,
            message: "请选择原料等级",
            trigger: ["blur", "change"],
          },
        ],
        weightStart: [
          {
            required: true,
            message: "请输入最小重量",
            trigger: 'blur',
          },
          {
            validator: numVerification,
            trigger: 'blur',
          },
        ],
        weightEnd: [
          {
            required: true,
            message: "请输入最大重量",
            trigger: 'blur',
          },
          {
            validator: numVerification,
            trigger: 'blur',
          },
        ],
      },
      ruleForm: {
        // standardFlag: "", //是否配置标准外显示
        materialsType: "1", //原料类型
        tableData: [
          {
            materialsId: "", //原料主键
            materialsLevelId: "", //原料等级主键ID
            weightStart: "",
            weightEnd: "",
            weightingStandard: "",
          },
        ],
      },
    };
  },
  created(){
    this.selectList()
    this.getList()
  },
  methods: {
    selectList(){
        this.remoteMethodName('')
        this.remoteMethodLevel('')
        this.getList()
    },
    //查询数据
    getList(){
        purchaseStandardList({materialsType:this.ruleForm.materialsType}).then(res=>{
            this.ruleForm.tableData=[]
            if(res.code==200&&res.result?.list?.length>0){
                res.result.list.forEach(item=>{
                    let obj={
                        materialsId:item.materialsId,
                        materialsLevelId:item.materialsLevelId,
                        weightStart:item.weightStart,
                        weightEnd:item.weightEnd,
                        weightingStandard:item.weightingStandard,
                    }
                    this.ruleForm.tableData.push(obj)
                })
            } else {
              this.ruleForm.tableData = []
              setTimeout(() => {
                this.addFrom()
              })
            }
        })
    },
    addFrom() {
      let obj = {
        materialsId: "", //原料主键
        materialsLevelId: "", //原料等级主键ID
        weightStart: "",
        weightEnd: "",
        weightingStandard: "",
      };
      this.ruleForm.tableData.push(obj);
    },

    delFrom(index) {
      this.ruleForm.tableData.splice(index, 1);
    },
    //原料名称
    remoteMethodName(query) {
      if (!this.ruleForm.materialsType) {
        this.$message({
          message: "请先选择原料类型",
          type: "warning",
        });
        return;
      }
      materialsList({
        materialsType: this.ruleForm.materialsType,
        materialsName: query,
      }).then((res) => {
        if (res.code == 200) {
          this.optionsName = res.result.list;
        }
      });
    },
    //原料等级
    remoteMethodLevel(query) {
      if (!this.ruleForm.materialsType) {
        this.$message({
          message: "请先选择原料类型",
          type: "warning",
        });
        return;
      }
      materialsLevelList({
        materialsType: this.ruleForm.materialsType,
        levelName: query,
      }).then((res) => {
        if (res.code == 200) {
          this.optionsLevel = res.result.list;
        }
      });
    },
    submitForm() {
      console.log(this.ruleForm);
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          purchaseStandardAdd({
            // standardFlag: this.ruleForm.standardFlag ? 1 : 0,
            materialsType:this.ruleForm.materialsType,
            list: this.ruleForm.tableData,
          }).then((res) => {
            if(res.code==200){
                this.$message({
              message: "添加成功",
              type: "success",
            });
            this.$emit("refreshList");
          this.close();
            }
          });
         
        }
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.modelStandard{
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
</style>
<style lang="scss" scoped>
.modelStandard {
  padding-bottom: 80px;
  padding-right: 36px;
}
.el-form-item {
  margin-bottom: 0 !important;
}

::v-deep.el-form-item {
  height: 74px;
  display: flex;
  align-items: center;
}

.submit {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.footer_btn{
  width: 80%;
}
</style>