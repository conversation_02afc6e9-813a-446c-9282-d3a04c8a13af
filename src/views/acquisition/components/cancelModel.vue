<template>
    <div style="padding-bottom: 60px">
        <el-dialog
            :visible.sync="dialogVisible"
            width="30%"
            :modal='false'
            class="dialog_box"
            :title="options.title"
            :before-close="handleClose">
            <p class="title1">
                <i class="el-icon-warning"></i>
                <span>{{options.tips}}</span>
            </p>
            <el-form ref="form" :model="ruleForm" :rules="rules" label-width="30px"> 
                <el-form-item prop="reason">
                    <el-input
                        type="textarea"
                        maxlength="100"
                        rows="4"
                        show-word-limit
                        :placeholder="options.placeholder"
                        v-model="ruleForm.reason"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" class="add_fill_btn" @click="handleSubmit">确 定</el-button>
            </span>
            </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            ruleForm: {
                reason: '',
            },
            dialogVisible: false,
            rules: {
                reason: [
                    { required: true, message: '请填写取消原因', trigger: 'blur' }
                ]
            }
        }
    },
    props: {
        inventoryCheckId: String,
        options: Object
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true,
            this.ruleForm.reason = ''
        },
        handleClose() {
            this.dialogVisible = false
            this.$refs.form.resetFields();
        },
        handleSubmit(){
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.$emit('submit', this.ruleForm)
                    this.handleClose()
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.title1{
    font-size: 16px;
    font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
    color: #1D2129;
    display: flex;
    span{
        margin-left: 10px
    }
    i {
        color: #F85300;
    }
}
</style>