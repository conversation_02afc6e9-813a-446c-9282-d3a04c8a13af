<template>
    <div style="padding-bottom: 80px;">
        <div style="border-bottom: 10px solid #F5F7FA;">
            <el-descriptions class="" :title="(dataInfo.checkInTime || '') + dataInfo.supplierName || ''" :column="3" size="medium" border>
                <template slot="extra">
                    <img class="img" v-if="dataInfo.checkInStatus == 0" src="../../../assets/images/yiquxiao.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 1" src="../../../assets/images/yituzai.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 4" src="../../../assets/images/daifenpei.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 2" src="../../../assets/images/dairuchang.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 3" src="../../../assets/images/daiqueren.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 5" src="../../../assets/images/daituzai.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkInStatus == 6" src="../../../assets/images/yituzai.png" alt="" srcset="">
                </template>
            </el-descriptions>
        </div>
        <div class="fc">
            <span class="point_icon">登记信息</span>
        </div>
        <el-row class="fcc">
            <el-col :span="24">
                <el-descriptions class="mt20" :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='入厂方式：'>{{ checkInTypeHash[dataInfo.checkInType] }}</el-descriptions-item>
                    <el-descriptions-item label='预约单：'>{{ dataInfo.appointmentCode }}</el-descriptions-item>
                    <el-descriptions-item label='分配圈舍：'>{{ dataInfo.houseName }}</el-descriptions-item>
                    <el-descriptions-item label='入厂编号：'>{{ dataInfo.checkInCode }}</el-descriptions-item>
                    <el-descriptions-item label='供应商编号：'>{{ dataInfo.supplierCode }}</el-descriptions-item>
                    <el-descriptions-item label='登记人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                    <el-descriptions-item label='入厂时间：'>{{ dataInfo.checkInTime }}</el-descriptions-item>
                </el-descriptions>
            </el-col>
        </el-row>
        <div class="fc">
            <span class="point_icon">入厂信息</span>
        </div>
        <el-row class="fcc">
            <el-col :span="24">
                <el-descriptions class="mt20" :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='联系电话：'>{{ dataInfo.supplierContactPhone }}</el-descriptions-item>
                    <el-descriptions-item label='类别：'>{{ typesHash[dataInfo.supplierType] }}</el-descriptions-item>
                    <el-descriptions-item label='供应商名称：'>{{ dataInfo.supplierName }}</el-descriptions-item>
                    <el-descriptions-item label='联系人：'>{{ dataInfo.supplierContactName }}</el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            {{dataInfo.supplierType == 1 ? "营业执照号：" : "身份证号："}}
                        </template>
                        {{ dataInfo.supplierCertNo }}
                    </el-descriptions-item>
                    <el-descriptions-item label='所属地区：'>{{ dataInfo.supplierAddress }}</el-descriptions-item>
                    <el-descriptions-item label='入厂车牌：'>{{ dataInfo.licensePlateNumber }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='检疫证明：'>
                        <div
                            :key="file.fileUrl"
                            v-for="(file) in quarantineCert"
                            class="filelistbx-item"
                            style="padding:10px;"
                            >
                            <!-- <el-link :href="`${file.fileUrl}`" :underline="false" target="_blank"> -->
                                <span class="el-icon-document cert-file" @click="showFile(file.fileUrl)">{{ file.fileName || '附件' }}</span>
                            <!-- </el-link> -->
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='车辆备案：'>
                        <div class="f">
                            <div
                                :key="file.fileUrl"
                                v-for="(file) in carRecords"
                                class="filelistbx-item"
                                >
                                <div>
                                    <img :src="file" alt="" @click="showImg(file)">
                                    <i class="el-icon-zoom-in" @click="showImg(file)"></i>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-col>
        </el-row>

        <div class="fc">
            <span class="point_icon">到厂明细</span>
        </div>
        <div style="font-size: 14px;color: #333333;">
            采购原料：{{ materialsTypeHash[dataInfo.materialsType] }}
        </div>

        <el-table :data="dataInfo.details" :show-summary="true" border class="mt20" stripe max-height="300">
            <el-table-column label="序号" align="center" width="300">
                <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                </template>
            </el-table-column>
            <el-table-column prop="materialsName" align="center" width="300" label="原料名称"></el-table-column>
            <el-table-column prop="checkInNum" align="right" label="入厂数量" sortable :sum-text="'合计'"></el-table-column>
        </el-table>
        <div class="fc" v-if="dataInfo.quarantineCertAfter">
            <span class="point_icon">检疫信息</span>
        </div>
        <el-row class="fcc" v-if="dataInfo.quarantineCertAfter">
            <el-col :span="24">
                <el-descriptions class="mt20" :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='操作人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                    <el-descriptions-item label='维护时间：'>{{ dataInfo.updateTime }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                    <el-descriptions-item label='宰后检疫证明：'>
                        <div
                            :key="file.fileUrl"
                            v-for="(file) in quarantineCertAfter"
                            class="filelistbx-item"
                            >
                            <!-- <el-link :href="`${file.fileUrl}`" :underline="false" target="_blank"> -->
                                <span class="el-icon-document cert-file" @click="showFile(file.fileUrl)">{{ file.fileName || '附件' }}</span>
                            <!-- </el-link> -->
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-col>
        </el-row>
        <div class="fc" v-if="dataInfo.checkInStatus == 0">
            <span class="point_icon">取消说明</span>
        </div>
        <el-row class="fcc" v-if="dataInfo.checkInStatus == 0">
            <el-col :span="24">
                <el-descriptions class="mt20" :labelStyle="{width: '230px'}" :contentStyle='{width: "250px"}' :column="2" size="medium" border>
                    <el-descriptions-item label='操作人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                    <el-descriptions-item label='操作时间：'>{{ dataInfo.closeTime }}</el-descriptions-item>
                    <el-descriptions-item label='取消原因：'>{{ dataInfo.reason }}</el-descriptions-item>
                </el-descriptions>
            </el-col>
        </el-row>
        <div class="footer_btn fcc" v-if="dataInfo.checkInStatus != 0 && !isfooter">
            <el-button type="primary" size="small" v-if="dataInfo.checkInStatus == 3" @click="showInfo = true">确认入厂</el-button>
            <el-button type="primary" size="small" v-if="dataInfo.checkInStatus == 4" @click="cancelModelFn(1)">取消入厂</el-button>
            <el-button type="primary" size="small" v-if="dataInfo.checkInStatus == 4" @click="handleOpen(1)">补录检疫证明</el-button>
            <el-button class="edit_fill_btn" size="small" v-if="dataInfo.checkInStatus == 4" @click="tasksFn">分配任务</el-button>
            <el-button class="delete_fill_btn" size="small" v-if="dataInfo.checkInStatus == 5" @click="cancelModelFn(2)">取消任务</el-button>
            <el-button class="delete_fill_btn" size="small" v-if="dataInfo.checkInStatus == 6 || dataInfo.checkInStatus == 5" @click="handleOpen(2)">补录宰前检疫</el-button>
            <el-button class="delete_fill_btn" size="small" v-if="dataInfo.checkInStatus == 6" @click="handleOpen(3)">维护宰后检疫</el-button>
        </div>
        <UploadProve ref='uploadProve' :title="uploadTypeTitles[uploadType]" @submit="updataFn"></UploadProve>
        <Tasks ref="tasks" :butcherId="dataInfo.butcherId"  :checkInTime="dataInfo.checkInTime" :checkInNum="dataInfo.checkInNum" :materialsType="dataInfo.materialsType" @getInfo="refreshList"></Tasks>
        <CancelModel ref="cancelModel" :options="cancelOptions" @submit="cancelData"></CancelModel>
        
        <el-drawer 
            class="drawer_box drawer_box1"
            title="确认入厂" 
            :visible.sync="showInfo" 
            :show-close="true" 
            :append-to-body="true" 
            :modal="true"
            :destroy-on-close="true"
            size="80%"
            :wrapperClosable="false">
                <EntranceForm ref="entranceForm" :checkInId="dataInfo.checkInId" :checkInTime="dataInfo.checkInTime" @getList="refreshList"></EntranceForm>
        </el-drawer>

      <el-dialog  width="30%" :modal="false" :visible.sync="dialogVisible">
        <img width="100%" style="height: 500px;" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
</template>

<script>
import {
    checkInInfo,
    checkInCert,
    butcherCert,
    butcherClose,
    checkInClose
} from "@/api/acquisition/index.js";
import UploadProve from './uploadProve.vue'
import Tasks from './tasks.vue'
import CancelModel from './cancelModel.vue'
import EntranceForm from './entranceForm.vue'
import { suffix } from '@/utils/validate'
export default {
    data() {
        return {
            activeName: '1',
            dataInfo: {},
            checkInTypeHash: {
                1: '直接入厂',
                2: '预约入厂',
            },
            typesHash: {
                1: '企业',
                2: '中间商',
                3: '养殖户',
            },
            materialsTypeHash: {
                1: '羊',
                2: '牛',
            },
            carRecords: [],
            quarantineCert:[],
            quarantineCertAfter: [],
            uploadType: '',
            uploadTypeTitles: {
                1: '补录检疫证明',
                2: '补录宰前检疫',
                3: '维护宰后检疫',
            },
            cancelOptions: {},
            showInfo: false,
            dialogVisible: false,
            dialogImageUrl: ''
        }
    },
    props: {
        checkInId: String,
        butcherId: String,
        isfooter: String
    },
    components: {
        UploadProve,
        Tasks,
        CancelModel,
        EntranceForm
    },
    watch: {
        checkInId() {
            if (this.checkInId || this.butcherId) {
                this.getInfo()
            }
        }
    }, 
    created() {
        if (this.checkInId || this.butcherId) {
            this.getInfo()
        }
    },
    methods: {
        getInfo() {
            checkInInfo({
                checkInId: this.checkInId,
                butcherId: this.butcherId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                    this.carRecords = this.dataInfo.carRecords && this.dataInfo.carRecords.split(',');
                    this.quarantineCert = this.dataInfo.quarantineCert && JSON.parse(this.dataInfo.quarantineCert)
                    this.quarantineCertAfter = this.dataInfo.quarantineCertAfter && JSON.parse(this.dataInfo.quarantineCertAfter)
                }
            })
        },
        refreshList() {
            this.getInfo();
            this.showInfo = false
            this.$emit('refreshList')
        },
        tasksFn() {
            this.$refs.tasks.handleOpen()
        },
        handleOpen(uploadType) {
            this.uploadType = uploadType
            this.$refs.uploadProve.handleOpen()
        },
        updataFn(data) {
            if (this.uploadType == 3) {
                butcherCert({
                    quarantineCert: JSON.stringify(data),
                    butcherId: this.dataInfo.butcherId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('补录成功')
                        this.getInfo()
                        this.$emit('refreshList')
                    }
                })
            } else {
                checkInCert({
                    quarantineCert: JSON.stringify(data),
                    checkInId: this.dataInfo.checkInId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('补录成功')
                        this.getInfo()
                    }
                })
            }
        },
        showImg(url) {
            this.dialogVisible = true
            this.dialogImageUrl = url
        },
        showFile(url) {
            const fileSuffix = suffix(url)
            if (fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'jpeg') {
                this.showImg(url)
            } else {
                window.open(url, '_blank')
            }
        },
        cancelModelFn(cancelType) {
            this.cancelType = cancelType
            if (cancelType == 1) {
                this.cancelOptions = {
                    title: '取消入厂',
                    tips: '取消入厂后，入厂信息将作废且不可再次操作，请确保与牧民协商后进行',
                    placeholder: '请填写入厂取消原因'
                }
            } else {
                this.cancelOptions = {
                    title: '取消任务',
                    tips: '取消后屠宰任务状态变更，原入厂信息将作废且不可再次操作，请确保与牧民协商后进行',
                    placeholder: '请填写任务取消原因'
                }
            }
            this.$refs.cancelModel.handleOpen()
        },
        cancelData(data) {
            if (this.cancelType == 1) {
                checkInClose({
                    ...data,
                    checkInId: this.dataInfo.checkInId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('取消成功')
                        this.getInfo()
                        this.$emit('refreshList')
                    }
                })
            } else {
                butcherClose({
                    ...data,
                    butcherId: this.dataInfo.butcherId
                }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('取消成功')
                        this.getInfo()
                        this.$emit('refreshList')
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.title{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.title div {
    font-size: 18px;
}
.el-row {
    font-size: 14px !important;
    .el-col-7{
        margin-top: 20px;
    }
}
.card-title {
  margin-bottom: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.model {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  &-text {
    color: rgb(102, 102, 102);
  }
  .header {
    &-title {
      font-size: 18px;
    }
  }
}
.footer_btn{
    width: 80%;
}
.img{
    width: 79px;
    height: 77px;
}
.el-descriptions-item__label{
    min-width: 200px;
}

.filelistbx-item {
    display: flex;
    justify-content: space-between;
    a{
        color: #0052D9;
    }
    div{
        position: relative;
        margin-right: 15px;
        i{
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform:  translate(-50%, -50%);
            color: #fff;
        }
        &:hover{
            i{
                display: block;
            }
        }
        img{
            width: 74px;
            height: 74px;
            border-radius: 2px 2px 2px 2px;
            &:hover{
                background: rgba(0,0,0,0.3);
            }
        }
    }
}
.drawer_box1{
    .el-drawer__body{
        padding-left: 0 !important;
    }
}
.cert-file{
    color: #0C36FF;
    cursor: pointer;
}
</style>