<template>
  <div class="add-plan-main" style="padding-bottom: 80px">
    <el-alert
        title="为避免价格混淆，在同一个时间段内同种原料类型仅有一条采购计划生效，新增计划生效时，原有计划默认变更为失效状态。"
        type="warning"
        show-icon>
    </el-alert>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <div class="sync">
            <div class="sync-box">
                <div class="swich">
                    <label>同步到移动端</label>
                    <el-form-item>
                        <el-switch
                        active-color="#12AE63"
                        inactive-color="#DCDCDC"
                        @change="changeSwitch"
                        v-model="ruleForm.planPublicFlag"></el-switch>
                    </el-form-item>
                </div>
                <div class="tips">同步计划到畜牧帮平台/养殖助手，需由平台进行审核，审核通过后，牧民可以在畜牧帮查看、预约已经生效的采购计划</div>
                <el-form-item class="mt20" style="margin-bottom: 15px">
                    <el-checkbox v-model="ruleForm.pricePublicFlag" :disabled="ruleForm.planPublicFlag ? false : true" label="同步时隐藏采购价"></el-checkbox>
                </el-form-item>
            </div>
        </div>
        <div class="form-title">
            <span>基本信息</span>
        </div>
        <el-row style="border-bottom: 10px solid #F5F7FA; padding-left: 15px; margin-top: 16px;">
            <el-col :span="8">
                <el-form-item label="采购原料" prop="materialsType">
                    <el-select v-model="ruleForm.materialsType" placeholder="请选择" style="width: 100%" @change="getgroupByMaterials">
                        <el-option v-for="(item, index) in materialsTypeList" :key="index" :label="item.text" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="生效日期" prop="effectiveTime">
                    <el-date-picker type="date" placeholder="选择日期" 
                        :picker-options="pickerOptions"
                        @change="changeTime"
                        v-model="ruleForm.effectiveTime" style="width: 100%;" 
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="截止日期" prop="expiredTime">
                    <el-date-picker type="date" placeholder="选择日期" 
                        :picker-options="pickerOptions1"
                        v-model="ruleForm.expiredTime" style="width: 100%;" 
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="负责人" prop="companyContactName">
                    <el-input placeholder="输入内容" v-model="ruleForm.companyContactName" maxlength="20"></el-input>
                
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="联系电话" prop="companyContactPhone">
                    <el-input placeholder="输入内容" v-model="ruleForm.companyContactPhone" maxlength="11"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                    <el-input placeholder="输入内容" type="textarea" :autosize='{ minRows: 4, maxRows: 6 }' v-model="ruleForm.remark" show-word-limit maxlength="300"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <div class="form-title">
            <span>采购价格</span>
        </div>
        <div style="padding-left: 30px; padding-right: 20px;" v-for="(item,index) in ruleForm.tableData" :key="index">
            <div class="table-title mt20">
                <div class="drop"></div>
                <label class="plan-title">{{item.materialsName}}</label>
                <div class="plan-num">
                    <label>
                        <i style="color: #F56C6C">*</i>
                        每日计划采购数量
                    </label>
                    <el-form-item
                        :prop="'tableData.' + index + '.purchaseNum'"
                        :rules="rules.purchaseNum">
                        <el-input placeholder="输入内容" type="number" v-model="item.purchaseNum" maxlength="6" oninput="value=value.replace(/[^\d]/g,'') "></el-input>
                    </el-form-item>
                </div>
            </div>
            <el-table :data="item.planDetailList" border class="mt20" stripe>
            <el-table-column prop="materialsLevel" align="center" label="胴体等级"></el-table-column>
            <el-table-column prop="weightSection" align="center" label="单只重量区间(kg)"></el-table-column>
            <el-table-column prop="weightingStandard"  align="center" label="过秤标准"></el-table-column>
            <el-table-column prop="checkInNum" label="价格(元/kg)"  align="center">
                <template slot-scope="scope">
                    <el-form-item
                        :prop="'tableData.' + index + '.planDetailList.' + scope.$index + '.unitPrice'"
                        :rules="rules.unitPrice">
                        <el-input type="number" v-model="scope.row.unitPrice" maxlength="8" style="width: 60%" placeholder="必填，请输入价格" ></el-input>
                    </el-form-item>
                    <!-- <el-input-number step-strictly :controls="false" style="width: 60%; text-align: left" v-model="scope.row.unitPrice" type="number" :min="1" maxlength="8" placeholder="必填，请输入价格"></el-input-number> -->

                </template>
            </el-table-column>
        </el-table>
        </div>
        <el-form-item style="padding-left: 30px;">
            <el-checkbox v-model="ruleForm.standardFlag" label="注：标准外拒收"></el-checkbox>
        </el-form-item>
        <div class="fcc footer_btn" style="width: 80%">
            <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
            <el-button type="primary"  size="mini"  @click="submitForm('ruleForm')">提 交</el-button>
        </div>
    </el-form>
  </div>
</template>

<script>
import { purchasePlanadd, checkInInfo, groupByMaterials, purchasePlanCheck } from '@/api/acquisition/index.js'
import uploadCard from '@/components/FileUpload/uploadCard.vue'
import { areaData } from "@/utils/mixin/area.js";
export default {
    mixins:[areaData],
    data() {
        var validatePass = (rule, value, callback) => {
        if (parseFloat(value) < 0) {
            callback(new Error("请输入大于0的数字"));
            } else {
                callback();
            }
        };
        var numVerification=(rule, value, callback)=>{
            if (!( /^\d+(\.\d{1,2})?$/.test(value))) {
                callback(new Error("小数位不能超过两位"));
            } else {
                callback();
            }
        }
        return {
            materialsTypeList:  [
                { text: '羊', value: 1 },
                { text: '牛', value: 2 },
            ],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000));
                },
            },
            ruleForm: {
                planPublicFlag: true, //是否公开采购计划（1是 0否）
                pricePublicFlag: true, //是否公开采购价格（1是 0否）
                standardFlag: false, //是否标准外拒收（1是 0否）
                materialsType: 1, //原料类型（1羊 2牛）
                companyContactName: '', //采购计划负责人名称
                companyContactPhone: '', //采购计划负责人联系电话
                effectiveTime: '', //采购计划生效时间
                expiredTime: '', //采购计划失效时间
                remark: '', //采购计划备注
                tableData: [],
            },
            rules: {
                materialsType:[
                    { required: true, message: '请选择采购原料', trigger: 'change' },
                ], 
                effectiveTime: [
                    { required: true, message: '请选择生效日期', trigger: 'change' },
                ],
                expiredTime: [
                    { required: true, message: '请选择截止日期', trigger: 'change' },
                ],
                companyContactName: [
                    { required: true, message: '请选择负责人', trigger: 'blur' },
                ],
                companyContactPhone:[
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                ], 
                purchaseNum: [
                    { required: true, message: "请输入每日计划采购数", trigger: 'blur', },
                    { validator: validatePass, trigger: ["blur", "change"], },
                ],
                unitPrice: [
                    {
                        required: true,
                        message: "请输入价格",
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: validatePass,
                        trigger: ["blur", "change"],
                    },
                    {
                        validator: numVerification,
                        trigger: ["blur", "change"],
                    },
                ],
                continueFlag: 0
            },
            tableData: [],
        }
    },
    components: {
        uploadCard
    },
    props: {
        checkInId: String
    },
    watch: {
    },
    computed: {
        pickerOptions1() {
            const that = this
            return { 
                disabledDate(time) {
                    return time.getTime() < (Date.parse(that.ruleForm.effectiveTime) - (24 * 60 * 60 * 1000));
                }
            }
        },
    },
    created() {
        this.getgroupByMaterials()
    },
    methods: {
        setInfoValue() {
            checkInInfo({
                checkInId: this.checkInId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                }
            })
        },
        getgroupByMaterials() {
            groupByMaterials({
                materialsType: this.ruleForm.materialsType,
            }).then(res => {
                this.ruleForm.tableData = res.result
            })
        },
        changeSwitch() {
            if (!this.ruleForm.planPublicFlag) {
                this.ruleForm.pricePublicFlag = false
            }
        },
        changeTime() {
            if (this.ruleForm.expiredTime) {
                this.ruleForm.expiredTime = ''
                this.$nextTick(() => {
                    this.$refs.ruleForm.clearValidate(['expiredTime']);
                })
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // this.$confirm(
                    //     `
                    //         <div style='font-size:18px'>
                    //             <i class='el-icon-warning' style='color:#FF9900'></i>
                    //             
                    //         </div>
                    //         <div style='padding-left:22px'>
                    //             ${this.ruleForm.planPublicFlag ? '您已勾选同步畜牧帮，平台审核通过后牧民可直接浏览采购计划' : ''}
                    //         </div>
                    //     `,
                    //     "提示",
                    //     {
                    //         confirmButtonText: "确定",
                    //         cancelButtonText: "取消",
                    //         dangerouslyUseHTMLString: true,
                    //     }
                    // ).then(() => {
                    //     this.addData()
                    // })
                    
                    const details = [];
                    this.ruleForm.tableData.forEach(item => {
                        const planDetailList = []
                        item.planDetailList.forEach(i => {
                            planDetailList.push({
                                purchaseStandardId: i.purchaseStandardId,
                                unitPrice: i.unitPrice
                            })
                        })
                        details.push({
                            materialsId: item.materialsId,
                            purchaseNum: item.purchaseNum,
                            planDetailList: planDetailList
                        })
                    })
                    const params = {
                        ...this.ruleForm,
                        planPublicFlag: this.ruleForm.planPublicFlag ? 1 : 0,
                        pricePublicFlag: this.ruleForm.pricePublicFlag ? 1 : 0, 
                        standardFlag: this.ruleForm.standardFlag ? 1 : 0,
                        planDetailGroupModels: details,
                        continueFlag: this.continueFlag
                    }
                    purchasePlanCheck(params).then((res) => {
                        // if (res.code == 200) {
                        //     console.log(res)
                            this.$confirm(
                                `
                                    <div style='font-size:18px'>
                                        <i class='el-icon-warning' style='color:#FF9900'></i>
                                        ${res.code == 92000? '与已有计划日期冲突，新增计划生效时，原有计划将变更为失效状态，是否确认提交？' : '提交后将不能修改，确认提交采购计划？'}
                                    </div>
                                    <div style='padding-left:22px'>
                                        ${this.ruleForm.planPublicFlag ? '您已勾选同步畜牧帮，平台审核通过后牧民可直接浏览采购计划' : ''}
                                    </div>
                                `,
                                "提示",
                                {
                                    confirmButtonText: "确定",
                                    cancelButtonText: "取消",
                                    dangerouslyUseHTMLString: true,
                                }
                            ).then(() => {
                                if (res.code == 92000) {
                                    this.continueFlag = 1
                                }
                                this.addData(details)
                            })
                        // }
                    });
                }
            });
        },
        addData(details) {
            purchasePlanadd({
                ...this.ruleForm,
                planPublicFlag: this.ruleForm.planPublicFlag ? 1 : 0,
                pricePublicFlag: this.ruleForm.pricePublicFlag ? 1 : 0, 
                standardFlag: this.ruleForm.standardFlag ? 1 : 0,
                planDetailGroupModels: details,
                continueFlag: this.continueFlag
            }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('新建成功')
                    this.$emit('getList')
                    this.resetForm('ruleForm')
                }
            })
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss" scoped>
.form-title{

    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    margin-top: 20px;
    padding-left: 30px;
}
.el-col-8,.el-col-24{
    padding: 0 15px;
    padding-right: 5%;
} 
.sync{
    padding-left: 30px;
    padding-right: 20px;
    margin-top: 10px;
    .sync-box{
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        padding-left: 10px;
        .swich{
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #333333;
            line-height: 22px;
            label{
                margin-right: 10px;
            }
            .el-form-item{
                margin: 0 !important;
            }
        }
        .tips{
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #999999;
            line-height: 22px;      
        }
    }
}
.table-title{
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #333333;
    line-height: 22px;
    .drop{
        width: 6px;
        height: 6px;
        background: #5672FA;
        border-radius: 100px 100px 100px 100px;
        margin-right: 8px;
    }
    .plan-title{
        min-width: 160px;
    }
    .plan-num{
        display: flex;
        align-items: center;
        label{
            margin-right: 10px;
        }
        .el-form-item{
            margin: 0 !important;
            width: 300px;
        }
    }
}
.el-alert--warning.is-light{
    background-color: #FCEFE7;
    color: #F85300;
    height: 50px;
    font-size: 12px;
}
</style>

<style lang="scss">
.sync-box{
    .el-checkbox__label,.el-checkbox__input.is-checked + .el-checkbox__label{
        color: #333333 !important;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background: #5672FA;
        border-color: #5672FA;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner{
        border-color: #5672FA;
    }
}
.add-plan-main{

    .el-alert__closebtn{
        top: 18px !important;
    }
    .el-alert.is-light .el-alert__closebtn{
        color: #F85300 !important;
    }
}
.add-plan-main{
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
</style>