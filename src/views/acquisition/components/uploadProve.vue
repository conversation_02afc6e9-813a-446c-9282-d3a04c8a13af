<template>
    <div>
        <el-dialog
            title=""
            :visible.sync="dialogVisible"
            width="30%"
            :modal="false"
            :show-close="false">
            <div>
                <span class="title1">{{title}}</span>
                <FileUpload
                    :fileType="['png', 'jpg', 'jpeg','rar', 'zip', 'doc','docx','pdf']"
                    :fileSize="10"
                    :isShowTip="true"
                    v-model="quarantineCert"
                    :isBackList="true"
                    class="mt20"
                    :limit="3">
                </FileUpload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button type="primary" size="mini" class="add_fill_btn" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            quarantineCert: []
        }
    },
    props: {
        title: String
    },
    methods: {
        handleClose() {
            this.dialogVisible = false
            this.quarantineCert = []
        },
        handleOpen() {
            this.dialogVisible = true
            this.quarantineCert = []
        },
        submit() {
            if (this.quarantineCert.length <= 0) {
                this.$message.info('请上传文件')
                return
            }
            this.$emit('submit', this.quarantineCert)
            this.handleClose()
        }
    }
}
</script>

<style lang="scss" scoped>
.title1{

font-size: 20px;
font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
font-weight: 500;
color: #1D2129;
}
</style>