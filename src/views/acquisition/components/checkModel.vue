<template>
    <div style="padding-bottom: 60px">
        <el-dialog
            :visible.sync="dialogVisible"
            width="30%"
            :modal='false'
            class="init_dialog"
            :before-close="handleClose">
            <p class="mes-title">
                <i class="el-icon-warning"></i>
                <span>{{options.title}}</span>
            </p>
            <el-form ref="form" :model="ruleForm" :rules="rules" label-width="30px"> 
                <el-form-item prop="reason">
                    <el-input
                        type="textarea"
                        maxlength="100"
                        rows="4"
                        show-word-limit
                        @blur="inputBlur"
                        :placeholder="options.subTitle"
                        v-model="ruleForm.reason"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button size="mini" type="primary" class="add_fill_btn" @click="handleSubmit">确 定</el-button>
            </span>
            </el-dialog>
    </div>
</template>

<script>
import {
  purchasePlanAudit,
} from "@/api/acquisition/index.js";
export default {
    data() {
        return {
            ruleForm: {
                reason: '',
            },
            dialogVisible: false,
            rules: {
                reason: [
                    { required: true, message: '请填写审核原因', trigger: 'blur' }
                ]
            }
        }
    },
    props: {
        inventoryCheckId: String,
        options: Object
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true,
            this.ruleForm.reason = ''
        },
        handleClose() {
            this.dialogVisible = false
            this.$refs.form.resetFields();
        },
        inputBlur() {
            if (this.options.auditStatus == 1) {
                this.$nextTick(() => {
                    this.$refs.form.clearValidate('reason')
                })
            }
        },
        handleSubmit(){
            if (this.options.auditStatus == 3) {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.checkData()
                    }
                });
            } else {
                this.checkData()
            }
        },
        checkData() {
            purchasePlanAudit({
                purchasePlanId: this.options.purchasePlanId, 
                auditStatus: this.options.auditStatus, // 1审核通过 5审核驳回
                reason: this.ruleForm.reason
            }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('审核成功')
                    this.dialogVisible = false
                    this.$emit('submit')
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.mes-title{
    font-size: 20px;
    font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
    font-weight: 500;
    color: #1D2129;
    span{
        margin-left: 10px
    }
    i {
        color: #F85300;
    }
}
</style>