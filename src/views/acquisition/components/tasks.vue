<template>
    <div>
        <el-dialog
            title=""
            :visible.sync="dialogVisible"
            width="30%"
            :modal="false"
            :show-close="false">
            <div>
                <span class="title1">分配任务</span>
            </div>

            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="mt20">
                <el-form-item label="屠宰时间" prop="butcherTime">
                    <el-date-picker type="date" placeholder="选择日期" 
                        :picker-options="pickerOptions"
                        v-model="ruleForm.butcherTime" style="width: 100%;" 
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="生产线" prop="productLineId">
                    <el-select v-model="ruleForm.productLineId" placeholder="请选择" style="width: 100%">
                        <el-option v-for="(item, index) in productLineData" :key="index" :label="item.productLineName" :value="item.productLineId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="屠宰数量" prop="butcherNum">
                    <el-input placeholder="输入内容" v-model="ruleForm.butcherNum" disabled :maxlength="6"></el-input>
                </el-form-item>
                <el-form-item label="屠宰工序" prop="butcherFlow">
                    <el-select v-model="ruleForm.butcherFlow"  style="width: 100%">
                        <el-option
                        v-for="dict in dict.type.mes_bt_flag"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button type="primary" size="mini" class="add_fill_btn" @click="submit('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { productLineList } from "@/api/basics/index.js";
import {
    butcherUpdate
} from "@/api/acquisition/index.js";
export default {
    dicts: ["mes_bt_flag"],
    data() {
        return {
            dialogVisible: false,
            productLineData: [],
            ruleForm: {
                productLineId: '', 
                butcherTime: '', //屠宰时间
                butcherNum: '', //屠宰数量
                butcherFlow: '' //屠宰工序
            },
            rules: {
                butcherTime: [
                    { required: true, message: '请选择屠宰时间', trigger: 'change' },
                ],
                productLineId: [
                    { required: true, message: '请选择生产线', trigger: 'change' },
                ],
                butcherNum:[
                    { required: true, message: '请输入本次屠宰数量', trigger: 'blur' },
                    {
                        pattern: /^\+?[1-9][0-9]*$/,
                        message: "请输入大于0的纯数字",
                        trigger: 'blur',
                    },
                ], 
                butcherFlow: [
                    { required: true, message: '请选择屠宰工序', trigger: 'change' },
                ],
            }
        }
    },
    props: {
        butcherId: String,
        materialsType: Number,
        checkInTime: String,
        checkInNum: Number
    },
    watch: {
        checkInNum() {
            if(this.checkInNum) {
                this.ruleForm.butcherNum = this.checkInNum
            }
        }
    },
    computed: {
        pickerOptions() {
            const that = this
            return { 
                disabledDate(time) {
                    return time.getTime() < (Date.parse(that.checkInTime) - (24 * 60 * 60 * 1000));
                }
            }
        },
    },
    mounted() {
        if(this.checkInNum) {
            this.ruleForm.butcherNum = this.checkInNum
        }
    },
    methods: {
        getproductLineList() {
            productLineList({
                pageNum: 1,
                pageSize: 1000,
                materialsType: this.materialsType
            }).then(res => {
                this.productLineData = res.result.list
            })
        },
        handleClose() {
            this.$refs.ruleForm.resetFields();
            this.dialogVisible = false
        },
        handleOpen() {
            this.getproductLineList()
            this.dialogVisible = true
        },
        submit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    butcherUpdate({
                        ...this.ruleForm,
                        butcherId: this.butcherId,
                        materialsType: this.materialsType
                    }).then(() => {
                        this.$message.success('分配成功')
                        this.$emit('getInfo')
                        this.handleClose()
                    })
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.title1{

font-size: 20px;
font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
font-weight: 500;
color: #1D2129;
}
</style>