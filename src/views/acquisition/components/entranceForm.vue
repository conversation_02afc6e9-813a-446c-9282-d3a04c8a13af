<template>
  <div class="entrance-form-main" style="padding-bottom: 80px">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <div class="form-title">
            <span>登记信息</span>
        </div>
        <el-row style="border-bottom: 10px solid #F5F7FA; padding-left: 15px;" class="mt20">
            <el-col :span="8">
                <el-form-item label="入厂方式" prop="checkInType">
                    <el-select v-model="ruleForm.checkInType" placeholder="请选择" style="width: 100%">
                        <el-option v-for="(item, index) in checkInTypeList" :key="index" :label="item.text" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="预约单" prop="checkInAppointmentId" v-if="ruleForm.checkInType == 2">
                    <el-select v-model="ruleForm.checkInAppointmentId"
                        remote
                        reserve-keyword
                        filterable
                        :remote-method="remoteMethod"
                        @change="changeCode"
                        placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="dict in appointLists"
                            :key="dict.checkInAppointmentId"
                            :label="dict.text"
                            :value="dict.checkInAppointmentId"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="分配圈舍" prop="livestockHouseId">
                    <el-select v-model="ruleForm.livestockHouseId"
                        placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="dict in livestockHouseLists"
                            :key="dict.livestockHouseId"
                            :label="dict.houseName"
                            :value="dict.livestockHouseId"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <div class="form-title">
            <span>入厂信息</span>
        </div>
        <el-row style="padding-left: 15px;" class="mt20">
            <el-col :span="8">
                <el-form-item label="联系电话" prop="supplierContactPhone">
                    <el-input placeholder="输入内容" v-model="ruleForm.supplierContactPhone" @input="changePhone"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="类别" prop="supplierType">
                    <el-select v-model="ruleForm.supplierType" placeholder="请选择" style="width: 100%" disabled @change="changePhone">
                        <el-option
                            v-for="dict in types"
                            :key="dict.value"
                            :label="dict.text"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="供应商名称" prop="supplierName">
                    <el-input placeholder="输入内容" v-model="ruleForm.supplierName"></el-input>
                </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
                <el-form-item label="联系人" prop="supplierContactName">
                    <el-input placeholder="输入内容" v-model="ruleForm.supplierContactName"></el-input>
                </el-form-item>
            </el-col> -->
            <el-col :span="8">
                <el-form-item prop="supplierCertNo">
                    <span slot="label">
                        {{ ruleForm.supplierType == 1 ? '营业执照号':'身份证号码'}}
                    </span>
                    <el-input placeholder="输入内容" v-model="ruleForm.supplierCertNo"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="所属地区" prop="supplierAddressIds">
                    <el-cascader :options="areaProps" :clearable="true" style="width:100%" v-model="ruleForm.supplierAddressIds" @change="handleAreaChange" ref="myCascader" > </el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="入厂车牌" prop="licensePlateNumber">
                    <el-input placeholder="输入内容" v-model="ruleForm.licensePlateNumber"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row style="padding-left: 30px;" class="mt20">
            <el-col :span="24">
                <el-form-item label="检疫证明" prop="quarantineCert" style="margin: 0">
                </el-form-item>
                <FileUpload
                    :fileType="['png', 'jpg', 'jpeg','rar', 'zip', 'doc','docx','pdf']"
                    :fileSize="10"
                    :isShowTip="true"
                    v-model="ruleForm.quarantineCert"
                    :isBackList="true"
                    style="margin-top: 14px"
                    :limit="3">
                </FileUpload>
            </el-col>
        </el-row>
        <el-row style="border-bottom: 10px solid #F5F7FA; padding-left: 30px; padding-bottom: 20px;">
            <el-col :span="24">
                <el-form-item label="车辆备案" prop="carRecords" style="margin-bottom: 0;margin-top: 22px">
                </el-form-item>
                <uploadCard
                    :fileType="['png', 'jpg', 'jpeg']"
                    :fileSize="10"
                    :limit="3"
                    v-model="ruleForm.carRecords"
                    :isShowTip="true"
                    style="margin-top: 14px"
                >
                </uploadCard>
            </el-col>
        </el-row>

        <div class="form-title">
            <span> 到厂明细</span>
        </div>
        <el-row style="padding-left: 15px;" class="mt20">
            <el-col :span="8">
                <el-form-item label="采购原料" prop="materialsType">
                    <el-select v-model="ruleForm.materialsType" placeholder="请选择" style="width: 100%" @change="getpurchasePlanList" :disabled="infoId && ruleForm.checkInType == 2 ? true : false">
                        <el-option v-for="(item, index) in materialsTypeList" :key="index" :label="item.text" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-table :data="tableData" stripe border style="padding: 0 30px;"
                    max-height="300"
                    show-summary
                    :summary-method="getSummaries"
                    @select-all="handleSelectionChange">
            <el-table-column type="selection" width="80" align="center">
                <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.check"></el-checkbox>
                </template>
            </el-table-column>
          <el-table-column label="序号" align="center"  width="300">
            <template slot-scope="scope">
                {{ scope.$index + 1 }}
            </template>
          </el-table-column>
            <el-table-column prop="materialsName" align="center" width="300" label="原料名称"></el-table-column>
            <el-table-column prop="checkInNum" align="center" label="入厂数量" :sum-text="'合计'">
                <template slot-scope="scope">
                    <el-input-number  step-strictly :controls="false" style="width: 50%; text-align: left" v-model="scope.row.checkInNum" @change="(value) => changeNum(value, scope.row)" type="number" :min="1" :max="999999999" placeholder="请输入"></el-input-number>
                </template>
            </el-table-column>
        </el-table>
        <div class="fcc footer_btn" style="width: 80%">
            <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
            <el-button type="primary"  size="mini"  @click="submitForm('ruleForm')">确认入厂</el-button>
        </div>
    </el-form>
  </div>
</template>

<script>
import { livestockHouseList } from "@/api/basics/index.js";
import { appointList, purchasePlanList, checkInUpdate, checkInInfo, checkInAdd } from '@/api/acquisition/index.js'
import { selectSysUser } from "@/api/supplier/index.js"
import { bankCardList } from "@/api/settlementManage/index.js";
import uploadCard from '@/components/FileUpload/uploadCard.vue'
import { areaData } from "@/utils/mixin/area.js";
export default {
    mixins:[areaData],
    data() {
        return {
            checkInTypeList: [
                { text: '直接入厂', value: 1 },
                { text: '预约入厂', value: 2 },
            ],
            types: [
                { text: '企业', value: 1 },
                { text: '中间商', value: 2 },
                { text: '养殖户', value: 3 },
            ],
            materialsTypeList:  [
                { text: '羊', value: 1 },
                { text: '牛', value: 2 },
            ],
            appointLists: [],
            livestockHouseLists: [],
            userInfo: {},
            supplierAddressIds:[],
            address: [],
            ruleForm: {
                checkInType: '',
                checkInAppointmentId: '',
                livestockHouseId: '',
                carRecords: '',
                supplierName: "", //供应商名称
                supplierContactName: "", //联系人姓名
                supplierContactPhone: "", //联系人电话
                supplierCertNo: "", //供应商证件号码
                supplierType: 3, //供应商类型（1企业 2中间商 3养殖户）
                supplierAddressIds: "", //供应商所属地区ID
                supplierAddress: "", //供应商所属地区
                licensePlateNumber: "", //入厂车牌号
                quarantineCert: [],
                materialsType: 1
            },
            rules: {
                checkInType: [
                    { required: true, message: '请选择入厂方式', trigger: 'change' },
                ],
                checkInAppointmentId: [
                    { required: true, message: '请选择预约单', trigger: 'change' },
                ],
                // livestockHouseId: [
                //     { required: true, message: '请选择圈舍', trigger: 'change' },
                // ],
                supplierContactPhone:[
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                ], 
                supplierType: [
                    { required: true, message: '请选择类别', trigger: 'change' },
                ],
                supplierName:[
                    { required: true, message: '请输入供应商名称', trigger: 'blur' },
                ], 
                supplierContactName:[
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                ], 
                supplierCertNo:[
                    { required: true, message: '请输入证件号码', trigger: 'blur' },
                ], 
                supplierAddressIds: [
                    { required: true, message: '请选择所属地区', trigger: 'change' },
                ],
                // licensePlateNumber:[
                //     { required: true, message: '请输入入厂车牌', trigger: 'blur' },
                // ], 
                // quarantineCert:[
                //     { required: true, message: '请上传检疫证明', trigger: 'change' },
                // ], 
                // carRecords:[
                //     { required: true, message: '请上传车辆备案', trigger: 'change' },
                // ], 
                materialsType:[
                    { required: true, message: '请选择采购原料类型', trigger: 'change' },
                ], 
            },
            tableData: [],
            purchasePlanId: '',
            dataInfo: {},
            infoId: '',
            totalNum: ''
        }
    },
    components: {
        uploadCard
    },
    props: {
        checkInId: String
    },
    watch: {
        checkInId() {
            if (this.checkInId) {
                this.infoId = this.checkInId
                this.setInfoValue() 
            }
        }
    },
    created() {
        if (this.checkInId) {
            this.infoId = this.checkInId
            this.setInfoValue()   
        } else {
            this.getpurchasePlanList(1)
        }
        this.userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
        if (!this.userInfo.tenantId) {
            this.$message.error('该账号有误，请联系管理员')
        }
        this.getappointList()
        this.getlivestockHouseList()
    },
    methods: {
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                if (index === 3) {
                    sums[index] = this.totalNum;
                }
            })
            return sums;
        },
        setInfoValue() {
            checkInInfo({
                checkInId: this.infoId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                    // this.carRecords = this.dataInfo.carRecords.split(',');
                    // this.quarantineCert = JSON.parse(this.dataInfo.quarantineCert)
                    this.ruleForm = {
                        checkInType: this.dataInfo.checkInType,
                        checkInAppointmentId: this.dataInfo.checkInAppointmentId,
                        livestockHouseId: this.dataInfo.livestockHouseId,
                        carRecords: this.dataInfo.carRecords,
                        supplierName: this.dataInfo.supplierName, //供应商名称
                        supplierContactName: this.dataInfo.supplierContactName, //联系人姓名
                        supplierContactPhone: this.dataInfo.supplierContactPhone, //联系人电话
                        supplierCertNo: this.dataInfo.supplierCertNo, //供应商证件号码
                        supplierType: this.dataInfo.supplierType, //供应商类型（1企业 2中间商 3养殖户）
                        supplierAddressIds: this.dataInfo.supplierAddressIds && this.dataInfo.supplierAddressIds.split(','), //供应商所属地区ID
                        supplierAddress: this.dataInfo.supplierAddress, //供应商所属地区
                        licensePlateNumber: this.dataInfo.licensePlateNumber, //入厂车牌号
                        quarantineCert: this.dataInfo.quarantineCert && JSON.parse(this.dataInfo.quarantineCert) ,
                        materialsType: this.dataInfo.materialsType
                    }
                    this.getpurchasePlanList()
                    if (this.dataInfo.supplierContactPhone) {
                        this.getSysUser()
                    }
                }
            })
        },
        getlivestockHouseList(query) {
            livestockHouseList({
                pageNum: 1, //页数
                pageSize: 1000, //行数
                tenantId: this.userInfo.tenantId
            }).then(res => {
                this.livestockHouseLists = res.result.list
            })
        },
        changeNum(val, item) {
            if (val) {
                item.check = true
            } else {
                item.check = false
            }
            this.getTotal()
        },
        getTotal() {
            this.totalNum  = 0
            this.tableData.forEach(item => {
                if (+item.checkInNum > 0) {
                    this.totalNum += +item.checkInNum
                }
            })
        },
        getappointList(query) {
            appointList({
                pageNum: 1, //页数
                pageSize: 1000, //行数
                appointmentStatus: 2, //预约状态（0未入厂 1已入厂）
                requestRole: 2,
                supplierContactName: query
            }).then(res => {
                this.appointLists = res.result.list.map(item => {
                    item.text = item.supplierContactName + item.appointmentCode
                    return item
                })
            })
        },
        remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                this.getappointList(query)
            } else {
                this.options = [];
            }
        },
        getpurchasePlanList(count) {
            purchasePlanList({
                pageNum: 1,
                pageSize: 1000,
                planStatus: 1, // 生效中
                requestRole: 2,
                tenantId: this.userInfo.tenantId,
                materialsType: this.ruleForm.materialsType,
                groupByNameFlag: 1
            }).then(res => {
                this.tableData = []
                if (count && res.result.list && res.result.list.length <= 0) {
                    this.ruleForm.materialsType = 2;
                    this.getpurchasePlanList()
                }

                if (!count && res.result.list && res.result.list.length <= 0) {
                    this.$message.info(`当前企业未发布${this.ruleForm.materialsType == 2 ? '牛' : '羊'}采购计划`)
                }
                if (this.dataInfo.details && this.dataInfo.details.length > 0) {
                    res.result.list.forEach(item => {
                        this.purchasePlanId = item.purchasePlanId
                        this.tableData = item.detailGroupList.map(i => {
                            i.check = false;
                            return i
                        })
                    })
                    this.dataInfo.details.forEach(x => {
                        res.result.list.forEach(item => {
                            item.detailGroupList.forEach(i => {
                                if (x.materialsId == i.materialsId) {
                                    i.check = true;
                                    i.checkInNum = x.checkInNum * 1
                                }
                                return i
                            })
                        })
                    })
                    this.getTotal()
                } else {
                    res.result.list.forEach(item => {
                        this.purchasePlanId = item.purchasePlanId
                        this.tableData = item.detailGroupList.map(i => {
                            i.check = false;
                            return i
                        })
                    })
                }
            })
        },
        changePhone() {
            if (this.ruleForm.supplierContactPhone.length === 11) {
                this.getSysUser()
            }
        },
        changeCode() {
            this.appointLists.forEach(item => {
                if (item.checkInAppointmentId == this.ruleForm.checkInAppointmentId) {
                    this.infoId = item.checkInId
                    this.setInfoValue()
                }
            })
            
        },
        getSysUser(params) {
            // this.$refs.ruleForm.clearValidate(['supplierName', 'contactName' ,'certNo' ,'businessAddressIds'])
            bankCardList({
                contactPhone: this.ruleForm.supplierContactPhone,
                // supplierType: this.ruleForm.supplierType,
                // ...params
            }).then(res=>{
                if(res.code==200){
                    const data = res.result
                    if(data.supplierName) {
                        this.ruleForm.supplierName = data.supplierName
                    }
                    // if(data.supplierType) {
                    //     this.ruleForm.supplierType = data.supplierType
                    // }
                    if(data.contactPhone) {
                        this.ruleForm.supplierContactPhone = data.contactPhone
                    }
                    if(data.certNo) {
                        this.ruleForm.supplierCertNo = data.certNo
                    }
                    // if(data.contactName) {
                    //     this.ruleForm.supplierContactName = data.contactName
                    // }
                    if (data.businessAddressIds) {
                        this.ruleForm.supplierAddressIds =  data.businessAddressIds.split(',')//id
                        this.address=data.supplierAddressIds && data.businessAddressIds.split(',')
                        this.ruleForm.supplierAddress=data.businessAddress
                    }
                    this.ruleForm.userId = data.userId
                    this.selectUserInfo = data
                }
            })
        },
        handleAreaChange(){
            let areaCodeData  = this.$refs['myCascader'].getCheckedNodes()[0];
            this.ruleForm.supplierAddressIds=(areaCodeData.path)
            this.ruleForm.supplierAddress=(areaCodeData.pathLabels).join(' ')
        },
        submitForm(formName) {
            const details = [];
            this.tableData.forEach(item => {
                if (item.check) {
                    details.push({
                        materialsId: item.materialsId,
                        checkInNum: item.checkInNum
                    })
                }
            })
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$confirm(
                        "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定入厂？</div>",
                        "提示",
                        {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            dangerouslyUseHTMLString: true,
                        }
                    ).then(() => {
                        if (this.checkInId) {
                            checkInUpdate({
                                ...this.ruleForm,
                                details,
                                requestRole: 2,
                                checkInId: this.checkInId,
                                purchasePlanId: this.purchasePlanId,
                                tenantId: this.userInfo.tenantId,
                                supplierAddressIds: this.ruleForm.supplierAddressIds.toString(),
                                quarantineCert: typeof this.ruleForm.quarantineCert == 'string' ? this.ruleForm.quarantineCert : JSON.stringify(this.ruleForm.quarantineCert)
                            }).then(() => {
                                this.$message.success('入厂成功')
                                this.$emit('getList')
                                this.resetForm('ruleForm')
                            })
                        } else {
                            checkInAdd({
                                ...this.ruleForm,
                                details,
                                requestRole: 2,
                                purchasePlanId: this.purchasePlanId,
                                tenantId: this.userInfo.tenantId,
                                supplierAddressIds: this.ruleForm.supplierAddressIds.toString(),
                                quarantineCert: JSON.stringify(this.ruleForm.quarantineCert)
                            }).then(() => {
                                this.$message.success('入厂成功')
                                this.$emit('getList')
                                this.resetForm('ruleForm')
                            })
                        }
                    });
                }
            });
        },
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss" scoped>
.form-title{

    font-size: 16px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #1F2026;
    margin-top: 20px;
    padding-left: 30px;
}
.el-col-8{
    padding: 0 15px;
    padding-right: 5%;
} 
</style>
<style lang="scss">
.entrance-form-main{
    .el-form-item__label{
        line-height: 22px;
    }
}
</style>