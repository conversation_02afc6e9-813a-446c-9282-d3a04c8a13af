<template>
  <div class="modelPricePlan mt20">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
      <el-form-item label="原料类型" prop="materialsType">
        <el-radio-group v-model="ruleForm.materialsType" size="mini" @change="getType">
          <el-radio label="1">羊</el-radio>
          <el-radio label="2">牛</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="生效日期" prop="effectiveTime">
        <el-date-picker
          v-model="ruleForm.effectiveTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="date"
          placeholder="选择日期"
        ></el-date-picker>
      </el-form-item>
      <el-table :data="ruleForm.tableData" stripe style="width: 100%">
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="materialsType" label="原料类型" :formatter="formatStatus" />
        <el-table-column prop="materialsName" label="原料名称 "></el-table-column>
          <el-table-column prop="materialsLevel" label="等级"></el-table-column>
        <el-table-column label="单只重量区间(kg)">
          <template slot-scope="scope">{{ scope.row.weightStart+'-'+scope.row.weightEnd }}</template>
        </el-table-column>
        <el-table-column prop="weightingStandard" label="过秤标准"></el-table-column>
        <el-table-column label="每日计划采购数量">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.purchaseNum'"
              :rules="rules.purchaseNum"
            >
              <el-input v-model.number="scope.row.purchaseNum" maxlength="6" placeholder="请输入采购数量"  oninput="value=value.replace(/[^\d]/g,'') "></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="unitPrice">
          <template slot="header">
            <span>
              价格(kg)
              <span style="color:red">*</span>
            </span>
          </template>
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.unitPrice'"
              :rules="rules.unitPrice"
            >
              <el-input type="number" v-model="scope.row.unitPrice" maxlength="8"  placeholder="请输入价格" ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
      <span v-show="standardFlag" style="color:rgb(245, 154, 35)">注：标准外拒收</span>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="ruleForm.remark"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>

    <div class="submit fcc">
      <el-button type="primary" size="mini" @click="submitForm">提交</el-button>
      <el-button @click="close" size="mini">取消</el-button>
    </div>
  </div>
</template>

<script>
import {
  purchaseStandardList,
  purchasePlanadd,
} from "@/api/acquisition/index.js";
export default {
  name: "EastMindMesappUiModelPurchasePlan",

  data() {
    var validatePass = (rule, value, callback) => {
      if (parseFloat(value) < 0) {
        callback(new Error("请输入大于0的数字"));
      } else {
        callback();
      }
    };
    var numVerification=(rule, value, callback)=>{
        if (!( /^\d+(\.\d{1,2})?$/.test(value))) {
        callback(new Error("小数位不能超过两位"));
      } else {
        callback();
      }
    }
    return {
      standardFlag: false,
      ruleForm: {
        materialsType:'1',//原料类型
        effectiveTime: "",
        tableData: [],
        remark: "",
      },
      rules: {
        effectiveTime: [
          {
            required: true,
            message: "请选择生效时间",
            trigger: ["blur", "change"],
          },
        ],
        unitPrice: [
          {
            required: true,
            message: "请输入价格",
            trigger: ["blur", "change"],
          },
          {
            validator: validatePass,
            trigger: ["blur", "change"],
          },
          {
            validator: numVerification,
            trigger: ["blur", "change"],
          },
        ],
        purchaseNum: [
          {
            validator: validatePass,
            trigger: ["blur", "change"],
          },
          
        ],
      },
    };
  },

  created() {
    this.$nextTick(() => {
      this.getList(this.ruleForm.materialsType);
    });
  },
  computed: {
    formatStatus() {
      return (row, com, val) => {
        return val == 1 ? "羊" : "牛";
      };
    },
  },
  methods: {
    getType(){
        this.getList(this.ruleForm.materialsType)
    },
    getList(type) {
      purchaseStandardList({materialsType:type}).then((res) => {
   
        if (res.result?.list.length <= 0) {
          this.$message({
            message: "请先维护规格",
            type: "warning",
          });
          return;
        }
        this.ruleForm.tableData = res.result?.list;
        res.result?.list[0].standardFlag == 1
          ? (this.standardFlag = true)
          : (this.standardFlag = false);
        this.ruleForm.tableData.forEach((item) => {
          item?.weightStart && item?.weightEnd
            ? (item.weightSection = item.weightStart + "-" + item.weightEnd)
            : "";
          this.$set(item, "purchaseNum", "");
          this.$set(item, "unitPrice", "");
        });
      });
    },
    close() {
      this.$emit("close");
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let queryObj = {
            effectiveTime: this.ruleForm.effectiveTime,
            remark: this.ruleForm.remark,
            materialsType:this.ruleForm.materialsType,
            standardFlag:this.standardFlag == true?'1':'',
            details: [],
          };
          this.ruleForm.tableData.forEach((item) => {
            let obj = {
              purchaseStandardId: item.purchaseStandardId,
              purchaseNum: item.purchaseNum,
              unitPrice: item.unitPrice,
              weightStart: item.weightStart,
              weightEnd: item.weightEnd,
            };
            queryObj.details.push(obj);
          });
            const h = this.$createElement;
            this.$confirm('操作确认', {
                title: '操作确认',
                message: h('div',null, [h('p', { style: 'font-size:18px; margin-bottom: 10px' },'提交后将不能修改，确认提交采购价?'),
                h('p',null, '审核通过后牧民可直接浏览采购价')]),
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
              purchasePlanadd(queryObj).then((res) => {
                if (res.code == 200) {
                  this.$message({
                    message: "添加成功",
                    type: "success",
                  });
                  this.$emit("refreshList");
                  this.close();
                }
              });
            })
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped >
.modelPricePlan {
  min-height: 100vh;
  margin: 0 30px;
}
.submit {
  margin-bottom: 0;
}
</style>