<template>
<el-drawer title="预约单详情" 
      :visible.sync="dialogVisible" 
      :show-close="true" 
      :append-to-body="true" 
      size="70%"
      :wrapperClosable="false"
      :modal="true"
  >
    <span class="status">{{ handelStatus(checkInStatusList,info.checkInStatus) }}</span>
    <div class="box" >
      <div class="el-row" v-show='info.checkInStatus==2||info.checkInStatus==0||info.checkInStatus==4||info.checkInStatus==1||info.checkInStatus==7'>
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>预约信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>预约编号：</span>
            <span>{{ info.appointmentCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>预约人：</span>
            <span>{{ info.appointmentContactName }}</span>
          </el-col>
          <el-col :span="8">
            <span>联系电话：</span>
            <span>{{ info.appointmentContactPhone }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>入厂时间：</span>
            <span>{{ info.checkInTime }}</span>
          </el-col>
          <el-col :span="8">
            <span>原料类型：</span>
            <span>{{ info.materialsName }}</span>
          </el-col>
          <el-col :span="8">
            <span>预约时间：</span>
            <span>{{ info.appointmentTime }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="info.checkInStatus==0">
          <el-col :span="8">
            <span>取消时间：</span>
            <span>{{ info.closeTime }}</span>
          </el-col>
        </el-row>
      </div>

      <div class="el-row" v-show='info.checkInStatus==2||info.checkInStatus==4||info.checkInStatus==1||info.checkInStatus==7'>
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>企业信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>企业名称：</span>
            <span>{{ info.companyName }}</span>
          </el-col>
          <el-col :span="8">
            <span>企业地址：</span>
            <span>{{ info.businessAddress }}</span>
          </el-col>
          <el-col :span="8">
            <span>联系人：</span>
            <span>{{ info.companyContactName }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>联系电话：</span>
            <span>{{ info.companyPhone }}</span>
          </el-col>
        </el-row>
      </div>

      <div class="el-row" v-show="info.checkInStatus==3||info.checkInStatus==4||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>入厂信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>入厂编号：</span>
            <span>{{ info.checkInCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>供应商名称：</span>
            <span>{{ info.supplierName }}</span>
          </el-col>
          <el-col :span="8">
            <span>类别：</span>
            <span>{{ handelStatus(supplierTypeList,info.supplierType) }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>联系人：</span>
            <span>{{info.supplierContactName}}</span>
          </el-col>
          <el-col :span="8">
            <span>联系电话：</span>
            <span>{{ info.supplierContactPhone }}</span>
          </el-col>
          <el-col :span="8">
            <span>身份证号码：</span>
            <span>{{ info.supplierCertNo }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>所属地区：</span>
            <span>{{ info.supplierAddress }}</span>
          </el-col>
          <el-col :span="8">
            <span>入厂车牌：</span>
            <span>{{ info.licensePlateNumber }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" class="fc">
            <span style="margin-right: 10px;">检疫证明:</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="info.quarantineCert"
              :preview-src-list="srcList"
            ></el-image>
          </el-col>
          <el-col :span="8" class="fc">
            <span style="margin-right: 10px;">车辆备案:</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="info.carRecords"
              :preview-src-list="srcList"
            ></el-image>
          </el-col>
        </el-row>
      </div>

      <div class="el-row" v-show="info.checkInStatus==3||info.checkInStatus==4||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>到厂明细</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" v-for="item in info.details" :key="item" class="el-row">
            <span>{{ item.materialsName }}：</span>
            <span>{{ item.checkInNum }}</span>
          </el-col>
        </el-row>
      </div>

      <div class="el-row" v-show="info.checkInStatus==4||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>登记信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>入厂方式：</span>
            <span>{{ info.checkInType==1?'直接入厂':'预约入厂' }}</span>
          </el-col>
          <el-col :span="8">
            <span>预约单：</span>
            <span>{{ info.appointmentCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>分配圈舍：</span>
            <span>{{ info.houseName }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>入厂时间：</span>
            <span>{{ info.confirmCheckInTime }}</span>
          </el-col>
          <el-col :span="8">
            <span>登记人：</span>
            <span>{{ info.updateUserName }}</span>
          </el-col>
        </el-row>
      </div>

      <div class="el-row" v-show="info.checkInStatus==5||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>屠宰任务信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>屠宰任务编号：</span>
            <span>{{ info.settlementCode}}</span>
          </el-col>
          <el-col :span="8">
            <span>屠宰时间：</span>
            <span>{{ info.butcherTime }}</span>
          </el-col>
          <el-col :span="8">
            <span>生产线：</span>
            <span></span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>本次屠宰数量：</span>
            <span></span>
          </el-col>
          <el-col :span="8">
            <span>屠宰工序：</span>
            <span></span>
          </el-col>
          <el-col :span="8">
            <span>分配时间：</span>
            <span></span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>操作人：</span>
            <span></span>
          </el-col>
        </el-row>
      </div>
      <div class="el-row" v-show="info.checkInStatus==1||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>结算信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" >
            <span>结算单号：</span>
            <span>{{ info.settlementCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>结算金额：</span>
            <span>{{ info.finalAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>结算时间：</span>
            <span>{{ info.settlementTime }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>收款账户：</span>
            <span></span>
          </el-col>
          <el-col :span="8">
            <span>检斤明细：</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="info.weightDetailUrl"
              :preview-src-list="srcList"
            ></el-image>
          </el-col>
        </el-row>
      </div>
      <div class="el-row" v-show="info.checkInStatus==6||info.checkInStatus==1">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>结算信息</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>结算金额：</span>
            <span>{{ info.finalAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>检斤明细：</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="info.weightDetailUrl"
              :preview-src-list="srcList"
            ></el-image>
          </el-col>
        </el-row>
      </div>
      <div class="el-row" v-show="info.checkInStatus==0">
        <el-row :gutter="20" class="fc">
          <div class="fast"></div>
          <span>取消说明</span>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>取消原因：</span>
            <span>{{ info.remark }}</span>
          </el-col>
          <el-col :span="8">
            <span>取消时间：</span>
            <span>{{ info.closeTime }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { appointInfo } from "@/api/acquisition/index";
export default {
  data() {
    return {
      dialogVisible: false,
      srcList: [],
      checkInStatusList:[
        {label:'已取消',value:0},
        {label:'已结算',value:1},
        {label:'待入厂',value:2},
        {label:'待确认',value:3},
        {label:'待分配',value:4},
        {label:'待屠宰',value:5},
        {label:'待结算',value:6},
        {label:'已爽约',value:7},
      ],
      supplierTypeList:[
      {label:'企业',value:1},
      {label:'中间商',value:2},
      {label:'养殖户',value:3},
      ],
      info:{}
    };
  },
  computed:{
    handelStatus(){
        return (list,value)=>{
            let name=''
            list.forEach(item=>{
                if(item.value==value){
                    name=item.label
                }
            })
            return name
        }
    }
  },
  methods: {
    getInfo(id){
        appointInfo({checkInId:id}).then(res=>{
            if(res.code==200){
                this.info=res.result
            }
        })
    }
  },
};
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.status {
  position: absolute;
  top: 50px;
  right: 80px;
  font-size: 20px;
  color: #409eff;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.box{
    padding: 0 20px;
}
</style>