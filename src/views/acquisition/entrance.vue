<template>
    <div class="app-container">
      <el-card shadow="never" class="box-card form-card mb10">
        <el-row :gutter="10">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                    <el-form-item prop="butcherCode">
                    <el-input v-model="queryParams.butcherCode" placeholder="屠宰编码" clearable />
                    </el-form-item>
                    <el-form-item prop="supplierName">
                    <el-input v-model="queryParams.supplierName" placeholder="供应商" clearable />
                    </el-form-item>
                    <el-form-item prop="supplierCode">
                    <el-input v-model="queryParams.supplierCode" placeholder="供应商编码" clearable />
                    </el-form-item>
                    <el-form-item prop="materialsType">
                        <el-select v-model="queryParams.materialsType" placeholder="原料类型">
                            <el-option
                                v-for="(item, index) in materialsTypeList"
                                :key="index"
                                :label="item.text"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="checkInStatus">
                        <el-select v-model="queryParams.checkInStatus" placeholder="状态">
                            <el-option
                                v-for="(item, index) in statusList"
                                :key="index"
                                :label="item.text"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="checkInType">
                        <el-select v-model="queryParams.checkInType" placeholder="入厂方式">
                            <el-option
                                v-for="(item, index) in checkInTypeList"
                                :key="index"
                                :label="item.text"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker
                            v-model="dateRange"
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="入厂开始日期"
                            end-placeholder="入厂结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-row>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    <template v-if="toggleSearchDom">
                        <el-button type="text" @click="packUp">
                            {{ toggleSearchStatus ? '收起' : '展开' }}
                            <i
                            :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                            ></i>
                        </el-button>
                    </template>
                </el-form-item>
            </el-row>
          </el-form>
        </el-row>
      </el-card>
      <el-card shadow="never" class="table_box">
        <el-row :gutter="10" class="fend mb8 form_btn">
            <el-button icon="el-icon-plus" size="mini" class="default_btn" @click="goPlan()">新 建</el-button>
            <el-button plain icon="el-icon-download" class="default_btn" size="mini" @click="exportData" >导出数据</el-button>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
            <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading" :max-height="tableHeight">
            <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
            <el-table-column
                v-for="(item,index) in tableColumn"
                :key="index"
                :prop="item.prop"
                :label="item.label"
                show-overflow-tooltip
                :min-width="item.width"
                :align="item.align"
                :sortable="item.sortable"
            >

            <template slot-scope="scope">
                <span v-if="scope.row.checkInStatus == 2 && item.prop == 'updateUserName' || scope.row.checkInStatus == 3 && item.prop == 'updateUserName'">-</span>
                <span v-else>{{ scope.row[item.prop] || '-'}}</span>
            </template>
            </el-table-column>
            <el-table-column align="center" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button
                        icon="el-icon-warning-outline"
                        size="mini"
                        @click="getInfo(scope.row)"
                        type="text"
                        class="text_btn"
                    >查看</el-button>
                </template>
            </el-table-column>
            </el-table>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
      <!-- 新建 -->
      <el-drawer 
        class="drawer_box drawer_box1"
        title="入厂登记" 
        :visible.sync="showInfo" 
        :show-close="true" 
        :append-to-body="true" 
        :modal="true"
        :destroy-on-close="true"
        size="80%"
        :wrapperClosable="false">
            <EntranceForm ref="entranceForm" @close="refreshList" @getList="refreshList"></EntranceForm>
      </el-drawer>
      <el-drawer 
        class="drawer_box"
        title="入厂信息详情" 
        :visible.sync="detailStatus" 
        :show-close="true" 
        :append-to-body="true" 
        :modal="true"
        :destroy-on-close="true"
        size="80%"
        :wrapperClosable="false">
            <EntranceDetail ref="entranceDetail" :checkInId="checkInId" @refreshList="getList" @getList="refreshList"></EntranceDetail>
      </el-drawer>
    </div>
    
</template>
        
<script>
import { checkInList, checkInExport } from "@/api/acquisition/index";
import EntranceForm from './components/entranceForm.vue';
import EntranceDetail from './components/entranceDetail';
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
    components:{
        EntranceForm,
        EntranceDetail
    },
    data() {
      return {
        tableColumn: [
          { check: true, prop: "butcherCode", label: "屠宰编码", width: 120, align:'center' },
          { check: true, prop: "materialsTypeName", label: "原料类型", width: 80, align:'center' },
          { check: true, prop: "supplierCode", label: "供应商编码", width: 120, align:'center' },
          { check: true, prop: "supplierName", label: "供应商名称", width: 120, align:'center' },
          { check: true, prop: "supplierContactPhone", label: "联系电话", align:'center' },
          { check: true, prop: "supplierAddress", label: "所属地区", width: 120, align:'left' },
          { check: true, prop: "checkInStatusName", label: "状态", width: 80, align:'center' },
          { check: true, prop: "checkInTypeName", label: "入厂方式", width: 80, align:'center' },
          { check: true, prop: "checkInTime", label: "入厂时间", width: 120, align:'center', sortable: true },
          { check: true, prop: "updateUserName", label: "登记人", align:'center' },
        ],
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          requestRole: 2,
          supplierContactPhone: '',
          materialsType: '',
          butcherCode: '',
          supplierName: '',
          supplierCode: '',
          checkInType: ''
        },
        appointmentTimeHash: {
          1: '上午',
          2: '下午',
        },
        materialsTypeList:  [
            { text: '羊', value: 1 },
            { text: '牛', value: 2 },
        ],
        statusList: [
            { text: '待入厂', value: 2 },
            { text: '待确认', value: 3 },
            { text: '待分配', value: 4 },
            { text: '待屠宰', value: 5 },
            { text: '已屠宰', value: 6 },
            { text: '已取消', value: 0 },
        ],
        statusList1: [
            { text: '待入厂', value: 2 },
            { text: '待确认', value: 3 },
            { text: '待分配', value: 4 },
            { text: '待屠宰', value: 5 },
            { text: '已屠宰', value: 6 },
            { text: '已屠宰', value: 1 },
            { text: '已取消', value: 0 },
        ],
        checkInTypeList: [
            { text: '直接入厂', value: '1' },
            { text: '预约入厂', value: '2' },
        ],
        tableData: [],
        loading: true,
        total: 0,
        showInfo: false,
        detailStatus:false,
        dateRange: [],
        checkInId: '',
      };
    },
  
    created() {
      this.getList();
    },
    methods: {
        refresh() {
            this.getList();
        },
        getList() {
            checkInList(this.queryParams).then((res) => {
            if (res.code == 200) {
                this.tableData = res.result.list.map(item => {
                    item.materialsTypeName = this.formatText(item.materialsType, this.materialsTypeList)
                    item.checkInStatusName = this.formatText(item.checkInStatus, this.statusList1)
                    item.checkInTypeName = this.formatText(item.checkInType, this.checkInTypeList)
                    return item
                });
                this.total = Number(res.result.total);
            }
            this.loading = false;
            });
        },
        formatText(val,list) {
            let name = "";
            list.forEach((item) => {
            if (item.value == val) {
                name = item.text;
            }
            });
            return name;
        },
        //重置
        resetQuery() {
            this.dateRange = [];
            this.reset();
            this.handleQuery();
        },
        reset() {
            this.resetForm("queryForm");
        },
        //刷新页面
        refreshList() {
            this.getList();
            this.showInfo = false
            this.detailStatus = false
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.dateRange);
            this.getList();
        },
        //采购计划详情
        goPlan(){
            this.showInfo = true
        },
        getInfo(row) {
            this.detailStatus = true;
            this.checkInId = row.checkInId
        },
        //导出数据
        exportData(){
            exportExcel(checkInExport,this.queryParams,'入厂管理')
        },
    },
};
</script>
<style lang="scss">
.drawer_box1{
    .el-drawer__body{
        padding-left: 0 !important;
    }
}
</style>
        