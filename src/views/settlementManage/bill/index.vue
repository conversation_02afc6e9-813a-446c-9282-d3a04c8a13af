<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
      <el-row :gutter="10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
          <el-row class=" form_row">
            <el-row class="form_col">
              <el-form-item prop="searchValue">
                <el-input v-model="queryParams.searchValue" placeholder="入厂编号/供应商名称" />
              </el-form-item>
              <el-form-item >
                <el-select v-model="finalButcherNum" placeholder="屠宰数量">
                  <el-option label="0-100" value="0-100" />
                  <el-option label="101-200" value="101-200" />
                  <el-option label="201-500" value="201-500" />
                  <el-option label="501-1000" value="501-1000" />
                  <el-option label="1000以上" value="1000" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="dateEnter"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="入厂开始日期"
                  end-placeholder="入厂结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="dateRange"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="结算开始日期"
                  end-placeholder="结算结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-row>
          </el-row>
          <el-row>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <template v-if="toggleSearchDom">
                    <el-button type="text" @click="packUp">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                </template>
            </el-form-item>
          </el-row>
        </el-form>
      </el-row>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row class="mb8 form_btn">
        <el-col :span="12" class="tabs-box">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="全部" :name="''"></el-tab-pane>
            <el-tab-pane
              v-for="(item,index) in statusList"
              :key="index"
              :label="item.label"
              :name="item.value"
            ></el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="12" class="fend">
          <el-button  icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
        </el-col>
      </el-row>

      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading"
                :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号"></el-table-column>
          <el-table-column
            v-for="(item,index) in tableColumn"
            :key="index" show-overflow-tooltip
            :prop="item.prop"
            :label="item.label"
            :align="item.align"
            :min-width="item.width"
            :sortable="item.sortable" :sort-method="(a, b) => { return a[item.prop] - b[item.prop]}"
            :formatter="item.prop=='settlementStatus'?handelStatus: handelLabel"
          ></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" prop="settlementTime" min-width="140" label="负责人">
              <template slot-scope="scope">
                <span>{{scope.row.settlementStatus == 1 ? scope.row.updateUserName : ''}}</span>
              </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="140" sortable prop="settlementTime" label="结算时间"></el-table-column>
          <el-table-column label="操作" min-width="130" align="left" fixed="right">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-warning-outline"
                @click="handleDetails(scope.row.settlementId)"
                type="text"
                size="mini"
                class="text_btn"
              >查看</el-button>
              <el-button
                icon="el-icon-delete"
                @click="cancelSettlementFn(scope.row.butcherId)"
                v-if="scope.row.settlementStatus == 2"
                type="text"
                size="mini"
                class="delete_text_btn"
              >撤销</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <el-drawer
      class="drawer_box"
      title="结算单详情"
      :visible.sync="drawer.open"
      :show-close="true"
      :append-to-body="true"
      :modal="true"
      :destroy-on-close="true"
      size="95%"
      :wrapperClosable="false"
    >
      <modelInfo :drawer="drawer" @close='close'></modelInfo>
    </el-drawer>
  </div>
</template>
  
  <script>
import modelInfo from "./components/modelInfo.vue";
import { settlementList, cancelSettlement, settlementExport} from "@/api/settlementManage/index.js";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  components: {
    modelInfo,
  },
  data() {
    return {
      tableColumn: [
        { check: true, prop: "settlementCode", label: "结算单号", width: 140, align: 'center' },
        { check: true, prop: "checkInCode", label: "入厂编号", width: 140, align: 'center' },
        { check: true, prop: "supplierName", label: "供应商", width: 140, align: 'center' },
        { check: true, prop: "receiverPhone", label: "联系电话", width: 140, align: 'center' },
        { check: true, prop: "checkInTime", label: "入厂时间", align: 'center', width: 140, sortable: true },
        { check: true, prop: "finalButcherNum", label: "屠宰数量", align: 'right', width: 140, sortable: true },
        { check: true, prop: "finalNetWeight", label: "重量(kg)", align: 'right', width: 140, sortable: true },
        { check: true, prop: "finalButcherNum", label: "结算数量", align: 'right', width: 140, sortable: true },
        { check: true, prop: "finalAmount", label: "结算金额", align: 'right', width: 140, sortable: true },
        { check: true, prop: "settlementStatus", label: "状态", width: 140, align: 'center' },
      ],
      statusList: [
        { label: "已结算", value: "1" },
        { label: "待结算", value: "2" },
      ],
      drawer: {
        open: false,
        id:''
      },
      activeName: "",
      queryParams: {
        searchValue:'',
        settlementStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      finalButcherNum:'',//屠宰数量
      tableData: [],
      dateRange: [],
      dateEnter: [],
      loading: true,
      total: 0,
      windowHeight:0,
    };
  },
  computed: {
    handelLabel() {
      return (row, com, val) => {
        return val;
      };
    },
    handelStatus() {
      return (row, com, val) => {
        let name = "";
        this.statusList.forEach((item) => {
          if (item.value == val) {
            name = item.label;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    //导出数据
    exportList(){exportExcel(settlementExport,this.queryParams,'结算单')},
    //列表查询
    getList() {
      settlementList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result?.list||[];
          this.total = Number(res.result?.total||0);
          this.loading = false;
        }
      });
    },
    //tab切换
    handleClick({name}) {
        this.queryParams.settlementStatus=name
        this.getList()
    },
    reset() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.dateEnter = [];
    },

    //重置
    resetQuery() {
      this.finalButcherNum=''
      this.dateRange = [];
      this.reset();
      this.handleQuery();
      
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.handelData("startTime2", "endTime2", this.dateRange);
      if(this.finalButcherNum){
        let num=this.finalButcherNum.split('-')
        this.queryParams.startValue=num[0]
        num.length>1?this.queryParams.endValue=num[1]:  this.queryParams.endValue=''
      }else{
        delete this.queryParams.startValue;
        delete this.queryParams.endValue;
      }
      console.log( this.queryParams);
      this.getList();
    },
    /** 查看详情按钮操作 */
    handleDetails(id) {
      this.drawer.open = true;
      this.drawer.id=id
    },
    close() {
      this.drawer.open = false;
      this.getList();
    },
    cancelSettlementFn(butcherId) {
      this.$confirm('撤销后结算单将回退至未提交状态，列表中将不再展示结算单信息', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(() => {
          cancelSettlement({
            butcherId
          }).then(() => {
              this.$message.success('已撤销')
              this.getList()
          })
      })
    }
  },
};
</script>
  
  <style lang="scss" scoped>
</style>
  