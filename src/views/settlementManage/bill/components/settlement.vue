<template>
    <div class="dialog_box dialog_box1">
        <el-dialog
            title=""
            :visible.sync="dialogVisible"
            width="30%"
            :modal='false'
            :show-close="false"
            :before-close="handleClose">
            <div>
                <div class="title1">
                    <img src="../../../../assets/images/sett.png" alt="">
                    去结算
                </div>
                <div class="radio" @click="changeRadio(1)">
                    <img v-if="radio == 1" src="../../../../assets/images/radio_selected.png" alt="" srcset="">
                    <img v-else src="../../../../assets/images/radio_unselect.png" alt="" srcset="">
                    线上支付
                    <div class="tips" v-if="isTip && !bankAccountNo && !receiverUserId" >该供应商未开通收款通道，暂不支持农赢保支付</div>
                </div>
                <div class="radio" @click="changeRadio(2)">
                    <img v-if="radio == 2" src="../../../../assets/images/radio_selected.png" alt="" srcset="">
                    <img v-else src="../../../../assets/images/radio_unselect.png" alt="" srcset="">
                    线下支付(非系统支付)
                </div>
                <!-- <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <el-form-item label="支付方式：" class="mt20" prop="radio">
                        <el-radio-group v-model="form.radio">
                            <el-radio :label="1">农赢保支付</el-radio>
                            <el-radio :label="2">线下支付（非系统支付）</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="" v-if="isTip" class="mt20">
                        <span style="color: #F85300;">该供应商未开通收款通道，暂不支持农赢保支付</span>
                    </el-form-item>
                </el-form> -->
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" class="grey_fill_btn" @click="handleClose">取 消</el-button>
                <el-button type="primary" class="add_fill_btn" size="mini" @click="submmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            radio: 1,
            rules: {
                radio: [
                    { required: true, message: '请选择', trigger: 'change' },
                ]
            }
        }
    },
    props: {
        isTip: Boolean,
        bankAccountNo: String,
        receiverUserId: String
    },
    methods: {
        handleOpen() {
            this.dialogVisible = true
        },
        handleClose() {
            this.dialogVisible = false
            this.$emit('handleClose')
        },
        changeRadio(value) {
            this.radio = value
        },
        submmit() {
            this.$emit('submmit', this.radio)
        }
    }
}
</script>

<style lang="scss" scoped>
.title1{
    display: flex;
    align-items: center;
    font-size: 18px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #1D2129;
    margin-top: 12px;
    padding-left: 27px;
    img{
        width:18px;
        height: 18px;
        margin-right: 10px;
    }
}
.radio{
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #1D2129;
    margin-top: 30px;
    padding-left: 54px;
    position: relative;
    cursor: pointer;
    img{
        width: 16px;
        height:  16px;
        margin-right: 10px;
    }
    .tips{
        position: absolute;
        bottom: -20px;
        left: 78px;
        font-size: 10px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #F85300;
    }
}
:deep(.el-dialog__header) {
    padding: 0;
}
:deep(.el-dialog__body) {
    padding: 0;
}
</style>
<style>
.dialog_box1 .el-dialog__header{
    background: transparent !important;
}
</style>