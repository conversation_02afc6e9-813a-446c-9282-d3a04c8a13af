<template>
  <div>
    <!--  结算单-->
    <div class="box-card" v-show="activeName==1">
      <div id="print">
        <div class="point_icon">
          <span>结算信息</span>
        </div>
        <el-table
          :data="settleTableData"
          stripe border
          style="width: 100%"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
          <el-table-column prop="materialsName" label="原料名称"></el-table-column>
          <el-table-column prop="levelName" label="等级" align="center"></el-table-column>
          <el-table-column prop="weightSection" label="单只重量区间(kg)" align="center" min-width="110px"></el-table-column>
          <el-table-column prop="weightNum" label="数量(只)" align="right"></el-table-column>
          <el-table-column prop="netWeight" label="胴体重量(kg)" align="right" min-width="100px"></el-table-column>
          <el-table-column prop="averageWeight" label="均重(kg)" align="right"></el-table-column>
          <el-table-column prop="unitPrice" label="价格(元/kg)" align="right"></el-table-column>
          <el-table-column prop="weightAmount" label="金额(元)" align="right"></el-table-column>
        </el-table>

        <el-row :gutter="20" class="mt20">
          <el-col :span="8">
            <span>补贴金额：</span>
            <span class="model-text">{{ settleInfo.subsidyAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>扣款金额：</span>
            <span class="model-text">{{ settleInfo.deductAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>总计金额：</span>
            <span class="model-text">{{ settleInfo.finalAmount }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20"></el-row>
        <div class="fc">
          <span class="point_icon">账户信息</span>
        </div>
        <div>
          <el-form :model="form" :rules="rules" ref="form" label-width="110px">
            <el-form-item label="开户行" required>
              <el-row style="margin: 0; width:400px">
                <el-col :span="10">
                    <el-select
                      v-model="openingBankType"
                      @change="changeBankType"
                    >
                      <el-option label="其他账户" value="1"></el-option>
                      <el-option label="默认账户" v-if="backList.length > 0" value="2"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="14">
                  <el-form-item prop="openingBank">
                    <el-select
                      v-model="form.openingBank"
                      filterable
                      allow-create
                      style="width:100%"
                      @change="selectCarNo"
                      v-if='backList.length > 0 && openingBankType == 2'
                    >
                      <el-option
                        v-for="(item,index) in backList"
                        :key="index"
                        :label="item.openingBank"
                        :value="item.openingBank"
                      ></el-option>
                    </el-select>
                    <el-input v-model="form.openingBank"  v-else></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="收款账户" prop="bankAccountName">
              <el-input v-model="form.bankAccountName" style="width:400px" :disabled="openingBankType == 2"></el-input>
            </el-form-item>
            <el-form-item label="银行卡号" prop="bankAccountNo">
              <el-input
                :disabled="openingBankType == 2"
                v-model="form.bankAccountNo"
                style="width:400px"
                oninput="value=value.replace(/[^\d]/g,'') "
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="fcc mb20" style="width:450px">
        <el-button type="primary" @click="settleSumint" size="small">保存</el-button>
      </div>
    </div>
    <!-- 检斤单 -->
    <div class="box-card" v-show="activeName==2">
      <div id="printWeigth">
        <div class="point_icon">
          <span>检斤单</span>
          <el-button plain class="export_btn" size="small" @click="exportList">导出数据</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>屠宰任务编号：</span>
            <span class="model-text">{{ weightInfo.butcherCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>日期：</span>
            <span class="model-text">{{ weightInfo.butcherTime }}</span>
          </el-col>
          <el-col :span="8">
            <span>采购计划编号：</span>
            <span class="model-text">{{ weightInfo.purchasePlanCode }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20"></el-row>
        <el-table :data="weightTableData" stripe style="width: 100%" border>
          <el-table-column label="序号" width="55" align="center" >
            <template slot-scope="{row,$index}">{{ row.index=='index'?'合计':$index+1}}</template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="materialsName" label="商品名称"></el-table-column>
          <el-table-column show-overflow-tooltip prop="levelName" label="等级" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="weightNum" label="数量" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="grossWeight" label="毛重(kg)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tareWeight" label="皮重(kg)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="netWeight" label="净重(kg)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="averageWeight" label="均重(kg)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="unitPrice" label="单价(元)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="weightAmount" label="金额(元)" align="right"></el-table-column>
          <el-table-column show-overflow-tooltip prop="createTime" label="检斤时间" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="name" label="检斤图片" align="center">
            <template slot-scope="scope">
              <img v-if="scope.row.weightDetailUrl" style="width: 60px; height: 40px" @click="showImg(scope.row)" :src="scope.row.weightDetailUrl" alt="">
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 补扣明细 -->
    <div class="box-card" v-show="activeName==3">
      <div id="printButcherFee">
        <div class="point_icon">
          <span>补扣明细</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>补扣总额：</span>
            <span class="model-text">{{ butcherFeeInfo.subsidyDeductAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>补贴金额：</span>
            <span class="model-text">{{ butcherFeeInfo.subsidyAmount }}</span>
          </el-col>
          <el-col :span="8">
            <span>扣款金额：</span>
            <span class="model-text">{{ butcherFeeInfo.deductAmount }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20"></el-row>
        <el-form :model="ruleForm" :rules="rules" class="form_input" ref="ruleForm">
          <el-table :data="ruleForm.tableData" style="width: 100%" border>
            <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>
                  补扣类型
                  <span style="color:red">*</span>
                </span>
              </template>

              <template slot-scope="scope">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.feeType'"
                  :rules="rules.feeType"
                >
                  <el-select v-model="scope.row.feeType" clearable>
                    <el-option label="补款" :value="1" />
                    <el-option label="扣款" :value="2" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>
                  项目
                  <span style="color:red">*</span>
                </span>
              </template>
              <template slot-scope="scope">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.feeOptions'"
                  :rules="rules.feeOptions"
                >
                  <el-select v-model="scope.row.feeOptions" clearable>
                    <el-option
                      v-for="dict in scope.row.feeType==1?(dict.type.supplementary_payment):dict.type.deduction_items"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value*1"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>
                  单位
                  <span style="color:red">*</span>
                </span>
              </template>
              <template slot-scope="scope">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.mathType'"
                  :rules="rules.mathType"
                >
                  <el-select v-model="scope.row.mathType" clearable>
                    <el-option label="kg" :value="1" />
                    <el-option label="只" :value="2" />
                    <el-option label="元" :value="3" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  min-width="280px">
              <template slot="header">
                <span>
                  计算说明
                  <span style="color:red">*</span>
                </span>
              </template>
              <template slot-scope="scope">
                <div class="fcc" v-if="scope.row.mathType!=3">
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.mathAmount'"
                    :rules="rules.mathAmount"
                  >
                    <el-input
                      type="number"
                      v-model.number="scope.row.mathAmount"
                      placeholder="请输入"
                      @change="countNum(scope.$index)"
                    >
                      <template slot="prepend">每{{scope.row.mathType==1?'kg':'只'}}</template>
                      <template slot="append">元</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.mathNum'"
                    :rules="rules.mathNum"
                  >
                    <el-input
                      type="number"
                      v-model.number="scope.row.mathNum"
                      placeholder="请输入"
                      @change="countNum(scope.$index)"
                    >
                      <template slot="prepend">共有</template>
                      <template slot="append">{{scope.row.mathType==1?'kg':'只'}}</template>
                    </el-input>
                  </el-form-item>
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot="header">
                <span>
                  金额
                  <span style="color:red">*</span>
                </span>
              </template>
              <template slot-scope="scope">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.totalAmount'"
                  :rules="rules.totalAmount"
                >
                  <el-input
                    type="number"
                    v-model="scope.row.totalAmount"
                    placeholder="请输入金额"
                    :disabled="scope.row.mathType==3?false:true"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column label="备注说明">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input v-model="scope.row.remark" placeholder="请输入内容"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-delete"
                  @click="delFrom(scope.$index)"
                  type="text"
                  size="mini"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button
            icon="el-icon-plus"
            @click="addFrom"
            style="width:100% ;color:rgb(64, 158, 255);"
          >新增</el-button>
        </el-form>
      </div>
      <el-row class="fcc">
        <el-button type="primary" @click="repairSumint" size="small">保存</el-button>
      </el-row>
    </div>
    <el-dialog  width="40%" :modal="false" :visible.sync="dialogVisible1">
      <img width="100%"  style="height: 500px;" :src="currentImgUrl" alt="">
    </el-dialog>
    <Confirm ref="confirm" :registerType="backInfo.registerFlag"  @submit="submit"></Confirm>
  </div>
</template>
  
  <script>
import {
  settlementDetail,
  weightDetail,
  butcherFeeDetail,
  bankCardList,
  saveBankAccount,
  butcherFeeAdd,
  settlementWeightDetailExport
} from "@/api/settlementManage/index.js";
import { exportExcel } from "@/utils/east";
import Confirm from './confirm.vue'
export default {
  dicts: ["supplementary_payment", "deduction_items"],
  props: {
    activeName: {
      type: String,
      default: "1",
    },
  },
  components: {
    Confirm
  },
  data() {
    return {
      dialogVisible: false,
      dialogVisible1: false,
      currentImgUrl: '',
      ruleForm: {
        tableData: [],
      },
      backInfo: {},
      disabled: false,
      tableData: [],
      settlementId: "",
      settleTableData: [],
      backList: [], //银行卡列表
      weightInfo: {}, //检斤单
      butcherFeeInfo: {}, //补扣明细数据
      weightTableData: [],
      settleInfo: {}, //结算单数据
      form: {
        openingBank: "",
        bankAccountNo: "",
        bankAccountName: "",
        settlementId: "",
      },
      openingBankType: '1',
      rules: {
        feeType: [
          { required: true, message: "请选择补扣类型", trigger: "blur" },
        ],
        feeOptions: [
          { required: true, message: "请选择补扣项目", trigger: "blur" },
        ],
        mathType: [{ required: true, message: "请选择单位", trigger: "blur" }],
        mathAmount: [{ required: true, message: "请输入", trigger: "blur" }],
        mathNum: [{ required: true, message: "请输入", trigger: "blur" }],
        totalAmount: [
          { required: true, message: "请输入金额", trigger: "blur" },
        ],
        bankAccountName: [
          {
            required: true,
            message: "请选择收款账户",
            trigger: ["blur", "change"],
          },
        ],
        openingBank: [
          {
            required: true,
            message: "请输入开户行",
            trigger: ["blur", "change"],
          },
        ],
        bankAccountNo: [
          {
            required: true,
            message: "请填写银行卡号",
            trigger: ["blur", "change"],
          }
        ],
      },
    };
  },
  mounted() {},

  methods: {
    changeBankType() {
      if (this.openingBankType == 1) {
          this.form.bankAccountNo = this.settleInfo.bankAccountNo;
          this.form.bankAccountName = this.settleInfo.bankAccountName;
          this.form.openingBank = this.settleInfo.openingBank;
      } else {
        this.form.bankAccountNo = '';
        this.form.bankAccountName = '';
        this.form.openingBank = '';
      }
    },
    //计算金额
    countNum(index) {
      if (
        this.ruleForm.tableData[index].mathNum &&
        this.ruleForm.tableData[index].mathAmount
      ) {
        this.ruleForm.tableData[index].totalAmount =
          this.ruleForm.tableData[index].mathNum *
          this.ruleForm.tableData[index].mathAmount;
      }
    },
    selectCarNo() {
      this.form.bankAccountNo = "";
      this.form.bankAccountName = "";
      this.disabled = false;
      this.backList.forEach((item) => {
        if (this.form.openingBank == item.openingBank) {
          // if (item.bankAccountNo && item.openingBank) {
          //   this.disabled = true;
          // }
          this.form.bankAccountNo = item.bankCardNo;
          this.form.bankAccountName = item.accountName;
        }
      });
    },
    //银行卡列表
    getCardList(userId, contactPhone, isEdit) {
      bankCardList({
        // supplierType: "3",
        // userId: userId,
        contactPhone: contactPhone,
      }).then((res) => {
        if (res.code == 200) {
          this.backInfo = res.result
          this.backList = res.result.bankCardList || [];
          this.$emit('backList',this.backList)
          if(this.backList.length==1){
            this.openingBank=this.backList[0].openingBank
            if (this.openingBankType == 2 && !isEdit) {
              this.selectCarNo()
          }
          }
        }
      });
    },
    //结算单
    getInfo(isEdit) {
      settlementDetail({ settlementId: this.settlementId }).then((res) => {
        if (res.code == 200) {
          this.settleTableData = res.result.butcherWeightList || [];
          this.settleInfo = res.result || {};
          this.form.bankAccountNo = this.settleInfo.bankAccountNo;
          this.form.bankAccountName = this.settleInfo.bankAccountName;
          this.form.openingBank = this.settleInfo.openingBank;
          this.getCardList(this.settleInfo.receiverUserId, this.settleInfo.receiverPhone, isEdit)
          // if( this.form.openingBank&&this.form.openingBank&&this.form.bankAccountNo && !isEdit){
          //   this.disabled=true
          // }
        }
      });
    },

    // 结算单
    settleSumint() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // this.form.settlementId = this.settlementId;
          // saveBankAccount(this.form).then((res) => {
          //   if (res.code == 200) {
          //     this.$message({
          //       message: "保存成功",
          //       type: "success",
          //     });
          //   }
          // });
          this.$refs.confirm.showModel()
        }
      });
    },

    showImg(row) {
      this.currentImgUrl = row.weightDetailUrl
      this.dialogVisible1 = true
    },
    // 检斤单
    getWeigth() {
      weightDetail({ settlementId: this.settlementId }).then((res) => {
        if (res.code == 200) {
          this.weightInfo = res.result || {};
          this.weightTableData = res.result.list || [];
          this.weightTableData.push({
            index: "index",
            weightNum: this.weightInfo.finalButcherNum,
            grossWeight: this.weightInfo.finalGrossWeight,
            tareWeight: this.weightInfo.finalTareWeight,
            netWeight: this.weightInfo.finalNetWeight,
            weightAmount: this.weightInfo.payableAmount,
          });
        }
      });
    },
    //补扣明细
    getButcherFee() {
      butcherFeeDetail({ settlementId: this.settlementId }).then((res) => {
        if (res.code == 200) {
          this.butcherFeeInfo = res.result || {};
          let butcherFeeData = res.result.list || [];
          this.ruleForm.tableData = []
          butcherFeeData.forEach((item) => {
            let obj = {
              feeType: item.feeType, //补扣类型
              feeOptions: item.feeOptions, //补扣项
              mathType: item.mathType, //单位
              mathAmount: item.mathAmount, //单位金额
              mathNum: item.mathNum, //数量
              remark: item.remark, //备注
              totalAmount: item.totalAmount, //金额
            };
            this.ruleForm.tableData.push(obj);
          });
        }
      });
    },
    //提交补扣明细
    repairSumint() {
      this.$refs["ruleForm"].validate((valid) => {
        let obj = {
          butcherId: this.settleInfo.butcherId,
          list: this.ruleForm.tableData,
        };
        if (valid) {
          butcherFeeAdd(obj).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: "保存成功",
                type: "success",
              });
              this.$emit('getInfo')
            }
          });
        }
      });
    },
    submit(registerFlag) {
      this.form.settlementId = this.settlementId;
      saveBankAccount({
        ...this.form,
        ...registerFlag,
        receiverUserId: this.backInfo.userId
      }).then((res) => {
        if (res.code == 200) {
          this.getInfo(1)
          this.$message({
            message: "保存成功",
            type: "success",
          });
        }
      });
    },
    //delFrom
    delFrom(index) {
      this.ruleForm.tableData.splice(index, 1);
    },
    //添加表单
    addFrom() {
      let obj = {
        feeType: "", //补扣类型
        feeOptions: "", //补扣项
        mathType: "", //单位
        mathAmount: "", //单位金额
        mathNum: "", //数量
        remark: "", //备注
        totalAmount: "", //金额
      };
      this.ruleForm.tableData.push(obj);
    },
    exportList(){exportExcel(settlementWeightDetailExport,{
      settlementId: this.settlementId
    },'结算检斤明细')},
    //统计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 5) {
          sums[index] = this.settleInfo.finalNetWeight;
        }
        if (index === 8) {
          sums[index] = this.settleInfo.payableAmount;
        }
        if (index === 4) {
          sums[index] = this.settleInfo.finalButcherNum;
        }
      });
      return sums;
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.el-row {
  font-size: 14px !important;
  margin-top: 20px;
  &:last-child {
    margin-bottom: 20px;
  }
}
.card-title {
  margin-bottom: 15px;
  margin-top: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}

.header {
  &-title {
    font-size: 20px;
  }
}
:deep(.el-form-item__content) {
  line-height: 30px;
}
.point_icon{
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="scss">
  .form_input{
    .el-form-item__content {
      line-height: 30px;
    }
  }
</style>