<template>
  <div class="">
      <el-descriptions class="mt20" :title="info.supplierName" :column="3" size="medium" border>
          <template slot="extra">
            <el-button
              plain
              size="small"
              @click="goSettle"
              class="add_btn"
              v-show="info.settlementStatus==2"
            >去结算</el-button>
            <el-button plain size="small" v-if="info.settlementStatus == 2" class="add_btn" @click="goSign">{{ info.contractUrl ? '编辑采购合同' : '签署采购合同'}}</el-button>
            <el-button plain size="small" v-if="info.contractUrl" class="add_btn" @click="handleCommand('contractUrl')">查看采购合同</el-button>
            <!-- <el-button plain size="small" class="add_btn" v-print="printOption">打印结算单</el-button> -->
            <!-- <el-dropdown style="margin-left: 10px" @command='handleCommand'>
              <el-button plain icon="el-icon-arrow-down" size="small">更多操作</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-print="printWeigth">打印检斤单</el-dropdown-item>
                <el-dropdown-item command='contractUrl'>查看采购合同</el-dropdown-item>
                <el-dropdown-item v-print="printButcherFee">打印补扣明细</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
          </template>
      </el-descriptions>
    <!-- <div class="header fb">
      <span class="header-title">{{info.companyName}}</span>
      <div>
      </div>
    </div> -->
    <!-- <el-row :gutter="20" class="fcc">
      <el-col :span="22">
        <el-row :gutter="20">
          <el-col :span="7">
            <span>入厂编号：</span>
            <span class="model-text">{{ info.checkInCode }}</span>
          </el-col>
          <el-col :span="7">
            <span>入厂时间：</span>
            <span class="model-text">{{ info.checkInTime }}</span>
          </el-col>
          <el-col :span="10">
            <span>供应商类型：</span>
            <span class="model-text">{{ handelType(info.supplierType,supplierTypeList) }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="7">
            <span>联系人：</span>
            <span class="model-text">{{ info.supplierContactName }}</span>
          </el-col>
          <el-col :span="7">
            <span>联系电话：</span>
            <span class="model-text">{{ info.supplierContactPhone }}</span>
          </el-col>
          <el-col :span="10">
            <span v-show="info.supplierType==1">企业营业执照号：</span>
            <span v-show="info.supplierType!=1">身份证号码：</span>
            <span class="model-text">{{ info.supplierCertNo }}</span>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="2" class="fcc">
        <div style="width: 16px; height: 16px; border-radius: 100%;background: #409EFF; margin-right: 10px"></div>
        <span>{{ handelType(info.settlementStatus,statusList) }}</span>
      </el-col>
    </el-row> -->


    <el-row :gutter="20" class="fcc">
        <el-col :span="20">
            <el-descriptions class="mt20" :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                <el-descriptions-item label='入厂编号：'>{{ info.checkInCode }}</el-descriptions-item>
                <el-descriptions-item label='入厂时间：'>{{ info.checkInTime }}</el-descriptions-item>
                <el-descriptions-item label='供应商类型：'>{{ handelType(info.supplierType,supplierTypeList) }}</el-descriptions-item>
                <el-descriptions-item label='联系人：'>{{ info.supplierContactName }}</el-descriptions-item>
                <el-descriptions-item label='联系电话：'>{{ info.supplierContactPhone }}</el-descriptions-item>
                <el-descriptions-item :label='info.supplierType==1 ? "企业营业执照号：" : "身份证号码："'>
                  {{ info.supplierCertNo }}</el-descriptions-item>
            </el-descriptions>
        </el-col>
        <el-col :span="4" class="fcc">
            <div class="status"></div>
            <span>{{ handelType(info.settlementStatus,statusList) }}</span>
        </el-col>
    </el-row>
    <el-row>
      <el-tabs v-model="activeName">
        <el-tab-pane label="结算单" name="1" />
        <el-tab-pane label="检斤单" name="2" />
        <el-tab-pane label="补扣明细" name="3" />
        <el-tab-pane label="结算记录" name="4" v-if="info.settlementStatus!=2" />
      </el-tabs>
    </el-row>
    <!-- 待结算 -->
    <modelWait @backList='getBackList' @getInfo='getInfo' :activeName="activeName" v-if="info.settlementStatus==2" ref="modelEnd"></modelWait>
    <!-- 已结算 -->
    <modelEnd :activeName="activeName" v-if="info.settlementStatus!=2" ref="modelEnd"></modelEnd>
      <!-- 结算 -->
    <Settlement ref="settlement" :isTip="isTip" :bankAccountNo="info.bankAccountNo" :receiverUserId="info.c" @submmit="submmit" @handleClose="handleClose"></Settlement>
    
    <el-drawer
        class="drawer_box"
        :visible.sync="visibleStatus" 
        :show-close="true" 
        :append-to-body="true" 
        :destroy-on-close="true"
        size="80%"
        title="签署合同"
        :wrapperClosable="false">
        <Sign @close='getInfo' :dataInfo="info"></Sign>
    </el-drawer>
    <el-dialog  width="30%" :modal="false" :visible.sync="dialogVisible">
        <img width="100%" style="height: 500px;" :src="dialogImageUrl" alt="">
      </el-dialog>
  </div>
</template>

<script>
import modelWait from "./modelWait.vue";
import modelEnd from "./modelEnd.vue";
import Settlement from "./settlement.vue";
import Sign from '../../agreement/sign.vue'
import { settlementInfo, getPayUrl, paySuccess } from "@/api/settlementManage/index.js";
import { suffix } from '@/utils/validate'
export default {
  dicts: ["supplementary_payment", "deduction_items"],
  components: {
    modelWait,
    modelEnd,
    Settlement,
    Sign
  },
  props: {
    drawer: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      printOption: {
        id: "print",
        popTitle: "&nbsp;", //页眉标题 默认浏览器标题 空字符串时显示undefined
      },
      printWeigth: {
        id: "printWeigth",
        popTitle: "&nbsp;",
      },
      printButcherFee: {
        id: "printButcherFee",
        popTitle: "&nbsp;",
      },
      statusList: [
        { label: "取消", value: "0" },
        { label: "已结算", value: "1" },
        { label: "待结算", value: "2" },
      ],
      supplierTypeList: [
        { label: "企业", value: 1 },
        { label: "中间商", value: 2 },
        { label: "养殖户", value: 3 },
      ],
      visibleStatus: false,
      info: {},
      activeName: "1",
      backList: [],
      isTip: false,
      dialogVisible: false,
      dialogImageUrl: ''
    };
  },
  computed: {
    handelType() {
      return (value, list) => {
        let name = "";
        list.forEach((item) => {
          if (item.value == value) {
            name = item.label;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getInfo();
  },
  mounted() {},
  methods: {
    getInfo() {
      this.visibleStatus = false
      settlementInfo({ settlementId: this.drawer.id }).then((res) => {
        {
          if (res.code == 200) {
            this.info = res.result;
            this.$nextTick(() => {
              this.$refs.modelEnd.settlementId = this.drawer.id;
              this.$refs.modelEnd.getInfo();
              this.$refs.modelEnd.getWeigth();
              this.$refs.modelEnd.getButcherFee();

              if (this.info.settlementStatus == 2) {
                this.$refs.modelEnd.getCardList(
                  this.info.receiverUserId,
                  this.info.receiverPhone
                );
              }
            });
          }
        }
      });
    },
    //收银台接口
    settleSumint() {
      getPayUrl({
        settlementId: this.info.settlementId,
      }).then((res) => {
        if (res.code == 200) {
          this.$emit('close')
          this.$refs.settlement.handleClose()
          window.open(res.result);
        }
      });
    },
    //去结算
    goSettle() {
      this.$refs.settlement.handleOpen()
      
    },
    submmit(data) {
      if (data == 1) {
        if (this.backList.length > 0 || this.info.bankAccountNo || this.info.receiverUserId) {
          this.settleSumint();
        } else {
          this.isTip = true;
        }
      } else {
        this.open();
      }
    },
    handleClose() {
      this.isTip = false;
    },
    open() {
      this.$confirm(
        "<div style='font-size:18px'><i class='el-icon-warning' style='color:#FF9900'></i> 是否确定已向该供应商支付货款？</div><div style='padding-left:22px'>确定后将变更状态为已支付，不可恢复</div>",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
        }
      ).then(() => {
        paySuccess({
          settlementId: this.info.settlementId,
          paymentType: 0
        }).then((res) => {
          if (res.code == 200) {
            this.$refs.settlement.handleClose()
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.$emit('close')
          }
        });
      });
    },
    //签署合同
    goSign() {
      this.visibleStatus = true
    },
    showImg(url) {
        this.dialogVisible = true
        this.dialogImageUrl = url
    },
    handleCommand(command) {
      if (command === 'contractUrl') {
        if (!this.info.contractUrl) {
          this.$message({
            message: "您还未签署采购合同",
          });
        } else {
            const fileSuffix = suffix(this.info.contractUrl)
            if (fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'jpeg') {
                this.showImg(this.info.contractUrl)
            } else {
                window.open(this.info.contractUrl, '_blank')
            }
        }
      }
    },
    getBackList(dataList) {
      this.backList = dataList
    }
  },
};
</script>

<style lang="scss" scoped>
.el-row {
  font-size: 14px !important;
  margin-top: 20px;
  &:last-child {
    margin-bottom: 20px;
  }
}
.card-title {
  margin-bottom: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.model {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  &-text {
    color: rgb(102, 102, 102);
  }
  .header {
    &-title {
      font-size: 18px;
    }
  }
}
</style>