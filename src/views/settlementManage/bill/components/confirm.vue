<template>
    <div class="dialog_box">
        <el-dialog
            title="操作确认"
            :visible.sync="dialogVisible"
            :modal='false'
            width="30%">
            <el-row class="fc mt20" style="padding-left: 12px">
                <i class="el-icon-warning" style="font-size: 21px; margin-right: 10px; color:#FF4302"></i>
                <div style="color: #1D2129;font-size: 18px">
                    是否保存供应商信息?
                </div>
            </el-row>
            <el-row style="padding: 0 45px" v-if="registerType == 0">
                <div class="mt20">
                    <p>该供应商尚未注册畜牧帮平台，不可使用线上支付，企业如想使用线上付款，保存供应商时可勾选同步注册按钮，勾选时请提前沟通供应商悉知</p>
                </div>
                <div class="mt20">
                    <el-checkbox v-model="registerFlag">
                        保存后同步注册该用户至畜牧帮平台
                    </el-checkbox>
                </div>
            </el-row>
            <div class="mt20" style="padding: 0 45px" v-if="registerType == 1">
                <p>结算时以最新保存信息为准</p>
            </div>
            <span slot="footer" class="dialog-footer fend">
                <el-button type="text" style="color: #1D2129" size="mini" @click="dialogVisible = false">取 消</el-button>
                <el-button type="text" style="color: #FF4302" size="mini" @click="addSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            registerFlag: true,
        }
    },
    props: {
        registerType: String
    },
    methods: {
        addSubmit() {
            this.dialogVisible = false
            if (this.registerType == 0) {
                this.$emit('submit', {
                    registerFlag: this.registerFlag ? 1 : 0,
                })
            } else {
                this.$emit('submit', {})
            }
        },
        showModel() {
            this.dialogVisible = true
        }
    }
}
</script>

<style>

</style>