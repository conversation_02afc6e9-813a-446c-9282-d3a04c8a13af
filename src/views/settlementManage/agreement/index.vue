<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
          <el-row class=" form_row">
            <el-row class="form_col">
              <el-form-item prop="settlementCode">
                <el-input v-model="queryParams.settlementCode" placeholder="结算单号"  />
              </el-form-item>
              <el-form-item prop="contractCode">
                <el-input v-model="queryParams.contractCode" placeholder="合同编号"  />
              </el-form-item>
              <el-form-item prop="supplierName">
                <el-input v-model="queryParams.supplierName" placeholder="供应商"  />
              </el-form-item>
              <el-form-item prop="contractType">
                <el-select v-model="queryParams.contractType" placeholder="合同类型">
                  <el-option label="电子合同" value="1" />
                  <el-option label="纸质合同" value="2" />
                  <el-option label="不限" value />
                </el-select>
              </el-form-item>
              <el-form-item prop="settlementStatus">
                <el-select v-model="queryParams.settlementStatus" placeholder="结算状态">
                  <el-option label="待结算" value="2" />
                  <el-option label="已结算" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item prop="supplierType">
                <el-select v-model="queryParams.supplierType" placeholder="支付方式">
                  <el-option label="额度支付" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item >
                <el-date-picker
                  v-model="dateRange"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="签署开始日期"
                  end-placeholder="签署结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-row>
          </el-row>
          <el-row>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <template v-if="toggleSearchDom">
                    <el-button type="text" @click="packUp">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                </template>
            </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="mb8 form_btn">
        <el-button type="primary" class="default_btn" icon="el-icon-plus" size="mini" @click="agreementAdd('1')">新建</el-button>
      </el-row>
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" stripe style="width: 100%" border v-loading="loading"
                :max-height="tableHeight">
          <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
          <el-table-column
            v-for="(item,index) in tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :align="item.align"
            :min-width="item.width"
            :sortable="item.sortable"
            show-overflow-tooltip 
          ></el-table-column>
          <el-table-column label="操作" min-width="180" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-warning-outline"
                @click="handleDetails(scope.row.settlementContractId)"
                type="text"
                size="mini"
                class="text_btn"
              >查看</el-button>
              <el-button
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
                type="text"
                size="mini"
                class="edit_text_btn"
              >编辑</el-button>
              <el-button
                icon="el-icon-download"
                class="text_btn"
                @click="downloadFile(scope.row)"
                type="text"
                size="mini"
              >下载合同</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 详情 -->
    <modelInfo ref="modelInfo"></modelInfo>
    <!-- 新建 -->
    <el-drawer
        class="drawer_box"
        :visible.sync="visibleStatus" 
        :show-close="true" 
        :append-to-body="true" 
        :destroy-on-close="true"
        size="80%"
        title="新建合同"
        :wrapperClosable="false">
        <Sign @close='close' :dataInfo="dataInfo"></Sign>
    </el-drawer>
  </div>
</template>
    
    <script>
import { contractPage, getContractPdfUrl} from "@/api/settlementManage/index.js";
import modelInfo from "./components/modelInfo.vue";
import { exportExcel } from "@/utils/east";
import Sign from './sign.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  components: {
    modelInfo,
    Sign
  },
  data() {
    return {
      tableColumn: [
        { check: true, prop: "settlementCode", label: "结算单号", align: 'center', width: '180' },
        { check: true, prop: "contractCode", label: "合同编号", align: 'center', width: '180' },
        { check: true, prop: "contractTypeText", label: "合同类型", align: 'center', width: '120' },
        { check: true, prop: "supplierName", label: "供应商", align: 'center', width: '120'},
        { check: true, prop: "supplierContactPhone", label: "联系电话", align: 'center', width: '120' },
        { check: true, prop: "contractStatusText", label: "签署状态", align: 'center', width: '120' },
        { check: true, prop: "settlementStatusText", label: "结算状态", align: 'center', width: '120' },
        // { check: true, prop: "contractPurposeText", label: "支付方式", align: 'center' },
        { check: true, prop: "updateUserName", label: "负责人", align: 'center', width: '120' },
        { check: true, prop: "signTime", label: "签署日期", align: 'center', width: '180', sortable: true },
      ],
      contractTypeList: [
        { label: "电子合同", value: 1 },
        { label: "纸质合同", value: 2 },
      ],
      statusList: [
        { label: "已结算", value: 1 },
        { label: "待结算", value: 2 },
      ],
      contractPurposeList: [{ label: "额度支付", value: 1 }],
      queryParams: {
        settlementCode: "",
        contractCode: "",
        supplierName: "",
        contractType: "",
        settlementStatus: "",
        contractPurpose: "",
        contractStatus:1,
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      dateRange: [],
      loading: true,
      total: 0,
      visibleStatus: false,
      sreachShow: false,
      windowHeight: '',
      dataInfo: {}
    };
  },
  created() {
    this.getList();
    },
    methods: {
    refresh() {
      this.getList();
    },
    //列表查询
    getList() {
      contractPage(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result?.list || [];
          this.loading = false;
          this.total = Number(res.result?.total || 0);
          this.tableData.forEach((item) => {
            item.contractStatusText = item.contractStatus == 1 ? '已签署' : '未签署'
            this.handeltext(
              this.contractTypeList,
              item.contractType,
              item,
              "contractTypeText"
            );
            this.handeltext(
              this.contractPurposeList,
              item.contractPurpose,
              item,
              "contractPurposeText"
            );
            this.handeltext(
              this.statusList,
              item.settlementStatus,
              item,
              "settlementStatusText"
            );
          });
          console.log(this.tableData);
        }
      });
    },
    handeltext(list, value, item, key) {
      list.forEach((items) => {
        if (items.value == value) {
            this.$set(item,key,items.label)
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateRange = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange?.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        delete this.queryParams.startTime;
        delete this.queryParams.endTime;
      }
      this.getList();
    },
    //下载合同
    downloadFile(row) {
      // getContractPdfUrl({settlementCode: id}).then((res) => {
      //   if (res.code == 200) {
      //     window.open(res.result, '_blank')
      //   }
      // });
      if (row.contractUrl) {
        window.open(row.contractUrl, '_blank')
      } else {
        this.$message.warning("您暂未签署合同")
      }
    },
    //   新增
    agreementAdd() {
      // this.$router.push({
      //   path: "/settleAccounts/sign",
      // });
      this.visibleStatus = true
    },
    handleEdit(row) {
      this.dataInfo = row
      this.visibleStatus = true
    },
    close() {
      this.visibleStatus = false
      this.getList();
    },
    /** 查看详情按钮操作 */
    handleDetails(id) {
      this.$refs.modelInfo.getInfo(id);
    },
  },
};
</script>
    
    <style lang="scss" scoped>
</style>
    