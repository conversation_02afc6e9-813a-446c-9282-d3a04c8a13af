<template>
    <div class="dialog_box">
      <el-dialog title="合同详情" :visible.sync="dialogVisible" width="70%" @close="dialogVisible=false">
        <div style="padding: 30px 30px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="title1">合同类型：</span>
            <span>{{ info.contractType==1?'电子合同':'纸质合同' }}</span>
          </el-col>
          <el-col :span="12">
            <span class="title1">结算单号：</span>
            <span>{{ info.settlementCode }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
            <span class="title1">收款账户：</span>
            <span>{{ info.bankAccountName }}</span>
          </el-col>
          <el-col :span="12">
            <span class="title1">开户行：</span>
            <span>{{ info.openingBank }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
      
          <el-col :span="12">
            <span class="title1">银行卡号：</span>
            <span>{{ info.bankAccountNo }}</span>
          </el-col>
          <el-col :span="12">
            <span class="title1">内部合同编号：</span>
            <span>{{info.contractCode}}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <span class="title1">签署时间：</span>
            <span>{{ info.signTime }}</span>
          </el-col>
            <el-col :span="12">
            <span class="title1">签署合同：</span>
            <el-button
              class="text_btn"
              @click="openAgreement(info.contractUrl)"
              type="text"
              size="mini"
            >查看合同</el-button>
          </el-col>
          </el-row>
        </div>
      </el-dialog>

      <el-dialog  width="30%" :modal="false" :visible.sync="dialogVisible1">
        <img width="100%" style="height: 500px;" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
</template>
  
<script>
  import { contractInfo, selectBySettlementCode } from "@/api/settlementManage/index.js";
import { suffix } from '@/utils/validate'
  export default {
    data() {
      return {
        info: {},
        dialogVisible: false,
        dialogVisible1: false,
        dialogImageUrl: ''
      };
    },
    methods: {
        showImg(url) {
            this.dialogVisible1 = true
            this.dialogImageUrl = url
        },
        //查看合同
        openAgreement(url){
            const fileSuffix = suffix(url)
            if (fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'jpeg') {
                this.showImg(url)
            } else {
                window.open(url, '_blank')
            }
        },
        getInfo(id) {
          contractInfo({settlementContractId:id}).then(res=>{
              if(res.code==200){
                  this.info=res.result||{}
              }
          })
          this.dialogVisible = true;
      },
    },
  };
</script>
  
<style lang="scss" scoped>
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .rectangle {
    width: 8px;
    height: 16px;
    background: #1890ff;
    margin-right: 5px;
  }
  .title1 {
    font-size: 14px;
    font-weight: 700;
  }
</style>