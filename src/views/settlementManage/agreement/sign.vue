<template>
  <div class="modelAdd">
    <el-card class="box-card">
      <!-- <el-button type="success" v-if="$route.query.type" icon="el-icon-arrow-left" size="mini" @click="backPage">返回</el-button> -->
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contractType" :rules="rules.contractType">
              <el-radio-group size="mini" v-model="ruleForm.contractType">
                <el-radio-button label="1">电子合同</el-radio-button>
                <el-radio-button label="2">纸质合同</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算单" prop="settlementCode">
              <el-select
                v-model="ruleForm.settlementCode"
                clearable
                style="width:100%"
                filterable
                remote
                reserve-keyword
                placeholder="请输入供应商名称、入厂编号、结算单号"
                :remote-method="remoteMethod"
                @change="selectCode"
                :disabled="isDisabled"
              >
                <el-option
                  v-for=" (item ,index) in settlementList"
                  :key="index"
                  :label="item.settlementCode1"
                  :value="item.settlementCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="收款账户" prop="bankAccountName">
              <el-select
                v-model="ruleForm.bankAccountName"
                clearable
                style="width:100%"
                filterable
                allow-create
                @change="selectBankNo"
              >
                <el-option
                  v-for="(item,index) in bankList"
                  :key="index"
                  :label="item.bankAccountName"
                  :value="item.bankAccountName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行" prop="openingBank">
              <el-input v-model="ruleForm.openingBank" placeholder="请输入开户行" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行卡号" prop="bankAccountNo">
              <el-input
                v-model="ruleForm.bankAccountNo"
                placeholder="请输入银行卡号"
                clearable
                maxlength="30"
                oninput="value=value.replace(/[^\d]/g,'') "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内部合同编号" prop="contractCode">
              <el-input v-model="ruleForm.contractCode" placeholder="请输入合同编号" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 电子合同 -->
        <el-form-item label="上传合同扫描件" prop="supplierName" v-if="ruleForm.contractType==2">
          <file-upload
            :fileType="['rar', 'zip','doc', 'docx', 'pdf']"
            :fileSize="100"
            :limit="1"
            v-model="ruleForm.contractUrl"
            :isShowTip="false"
          >
            <el-button type="primary" size="mini" class="lobtb">
              <i class="el-icon-upload el-icon--right"></i>
              上传合同扫描件
            </el-button>
          </file-upload>
        </el-form-item>
      </el-form>
      <div class="submit">
        <el-button
          type="primary"
          @click="submitForm"
          size="mini"
          :loading="buttonLoading"
          v-show="(contractStatus==2||contractStatus=='')&&signBtn"
        >{{ruleForm.contractType == 1 ? '发起合同签署' : '保存'}}</el-button>
        <el-button size="mini" @click="close">取消</el-button>
      </div>
    </el-card>
  </div>
</template>
  
  <script>
import {
  settlementList,
  bankCardList,
  selectBySettlementCode,
  getContractUrl,
} from "@/api/settlementManage/index.js";
export default {
  name: 'sign',
  data() {
    return {
      dialogVisible: false,
      codeForm: {}, //验证码
      settlementList: [],
      bankList: [],
      contractStatus: "", //签署状态
      signBtn: true,
      ruleForm: {
        contractType: "",
        settlementCode: "",
        bankAccountName: "",
        bankAccountNo: "",
        openingBank: "",
        contractUrl: "",
      },
      isDisabled: false,
      buttonLoading: false,
      rules: {
        settlementCode: [
          { required: true, message: "请选择结算单", trigger: "blur" },
        ],
        bankAccountName: [
          { required: true, message: "请选择收款账户", trigger: "blur" },
        ],
        openingBank: [
          { required: true, message: "请填写开户行", trigger: "blur" },
        ],
        bankAccountNo: [
          { required: true, message: "请填写银行卡号", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    dataInfo: Object
  },
  watch:{
    dataInfo() {
      if (this.dataInfo && this.dataInfo.settlementCode) {
        this.ruleForm = {
          contractType: this.dataInfo.contractType || '1',
          settlementCode: this.dataInfo.settlementCode,
          bankAccountName: this.dataInfo.bankAccountName,
          bankAccountNo: this.dataInfo.bankAccountNo,
          openingBank: this.dataInfo.openingBank,
          contractCode: this.dataInfo.contractCode,
          contractUrl: this.dataInfo.contractUrl,
        }
        this.getCardList(this.dataInfo.receiverUserId, this.dataInfo.receiverPhone);
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    }
  },
  created() {
    this.getSettlement("");
    if (this.dataInfo && this.dataInfo.settlementCode) {
      this.ruleForm = {
        contractType: this.dataInfo.contractType || '1',
        settlementCode: this.dataInfo.settlementCode,
        bankAccountName: this.dataInfo.bankAccountName,
        bankAccountNo: this.dataInfo.bankAccountNo,
        openingBank: this.dataInfo.openingBank,
        contractCode: this.dataInfo.contractCode,
        contractUrl: this.dataInfo.contractUrl,
      }
      this.getCardList(this.dataInfo.receiverUserId, this.dataInfo.receiverPhone);
      this.isDisabled = true
    } else {
      this.isDisabled = false
    }
  },

  methods: {
    remoteMethod(value) {
        this.signBtn=true
        this.getSettlement(value);
    },

    selectCode() {
      if (this.ruleForm.settlementCode) {
        this.getSettleCode();
        this.settlementList.forEach((item) => {
          if (item.settlementCode == this.ruleForm.settlementCode) {
            this.getCardList(item.receiverUserId, item.receiverPhone);
          }
        });
      }
    },
    selectBankNo() {
      this.bankList.forEach((item) => {
        if (this.ruleForm.bankAccountName == item.bankAccountName) {
          this.ruleForm.bankAccountNo = item?.bankAccountNo;
          this.ruleForm.openingBank = item?.openingBank;
        }
      });
    },
    //获取合同
    getSettleCode() {
      selectBySettlementCode({
        settlementCode: this.ruleForm.settlementCode,
      }).then((res) => {
        if (res.code == 200) {
          this.ruleForm.bankAccountName = res.result?.bankAccountName;
          this.ruleForm.bankAccountNo = res.result?.bankAccountNo;
          this.ruleForm.openingBank = res.result?.openingBank;
          this.contractStatus = res.result?.contractStatus || "";
        }
      });
    },
    //获取结算单列表
    getSettlement(settlementCode) {
      settlementList({
        pageNum: 1,
        pageSize: 100000,
        searchValue: settlementCode,
        settlementStatus: 2
      }).then((res) => {
        if (res.code == 200) {
          this.settlementList = res.result.list.map((item) => {
            item.settlementCode1 = item.supplierName + item.settlementCode
            return item
          });
        }
      });
    },
    //银行列表
    getCardList(userId, phone) {
      bankCardList({
        // supplierType: 3,
        // userId: userId,
        contactPhone: phone,
      }).then((res) => {
        if (res.code == 200) {
          this.bankList = res.result.bankCardList || [];
        }
      });
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          if (this.ruleForm.contractType == 1) {
            this.buttonLoading = true
          }
          getContractUrl(this.ruleForm).then((res) => {
            if (res.code == 200) {
              this.signBtn = false;
              this.buttonLoading = false
              this.close()
              console.log(this.ruleForm.contractType)
              if (this.ruleForm.contractType == 1) {
                setTimeout(() => {
                  this.buttonLoading = false
                  const tempwindow = window.open("_blank");
                  tempwindow.location = res.result.contractUrl;
                }, 500);
              } else {
                this.signBtn = false;
                this.close()
                this.$message({
                  message: "签署成功",
                  type: "success",
                });
              }
            }
          });
        }
      });
    },
    close() {
      if (this.$route.query.type) {
        this.backPage()
      } else {
        this.$emit("close");
      }
      this.ruleForm = {
        contractType: "",
        settlementCode: "",
        bankAccountName: "",
        bankAccountNo: "",
        openingBank: "",
        contractUrl: "",
      }
    },
    backPage() {
      this.$router.go(-1)
    }
  },
};
</script>
  
  <style lang="scss" scoped>
.modelAdd {
  padding: 20px 20px;
}
:deep(.el-icon-upload) {
  font-size: 12px;
  color: #fff;
}
.el-icon-upload {
  font-size: 12px;
  color: #fff;
}
.submit {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>