<template>
    <div class="mt20">
        <el-card shadow="never" class="mb10">
            <el-row :gutter="10" class="form_box">
                <el-form
                    :model="queryParams"
                    ref="queryForm"
                    size="small"
                    :inline="true"
                >
                <el-form-item label="单位名称" prop="unitName">
                    <el-input
                    v-model="queryParams.unitName"
                    placeholder="请输入单位名称"
                    clearable
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" >搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery" >重置</el-button>
                </el-form-item>
                </el-form>
            </el-row>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb20">
                <el-col class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                </el-col>
                <!-- <right-toolbar
                        :showSearch.sync="showSearch"
                        @queryTable="getList"
                    ></right-toolbar> -->
            </el-row>
            <!-- 表格数据 -->
            <el-table
                :data="tableData"
                stripe
                style="width: 100%"
                v-loading="loading"
            >
                <el-table-column type="index" label="序号" align="center"></el-table-column>
                <el-table-column prop="unitName" label="单位名称"  align="center"/>
                <el-table-column prop="updateUserName" label="创建人" align="center"></el-table-column>
                <el-table-column prop="updateTime" label="更新时间" align="center" sortable></el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-edit" size="mini" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                        <el-button icon="el-icon-delete" size="mini" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <unitModel ref="modelRef" :data="currentData" @getList='getList'></unitModel>
    </div>
</template>
  
<script>
import unitModel from "./components/unitModel.vue";
import { productUnitList, productUnitDelete } from "@/api/basics/index.js";
export default {
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                unitName: '',
            },
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
        };
    },
    components: {
        unitModel
    },
    created() {
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            productUnitList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
            this.$refs.queryForm.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        deleteEnclosure(row) {
            this.$confirm('是否删除该单位？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                productUnitDelete({
                    productUnitId: row.productUnitId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        }
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  