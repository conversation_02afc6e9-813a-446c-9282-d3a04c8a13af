<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.productTypeId ? '编辑类型' : '新建类型'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        :modal='false'
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="130px"  class="mt20">
            <el-form-item label="产品类型名称" prop="productTypeName">
                <el-input v-model="ruleForm.productTypeName"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="productTypeStatus">
                <el-select v-model="ruleForm.productTypeStatus" style="width: 100%">
                    <el-option label="启用" :value="1" />
                    <el-option label="禁用" :value="0" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary" @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { productTypeAdd, productTypeUpdate } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                productTypeName: '',
                productTypeStatus: 1
            },
            rules: {
                productTypeName: [
                    { required: true, message: '请输入产品类型', trigger: 'blur' },
                ],
                productTypeStatus: [
                    { required: true, message: '请选择状态', trigger: 'change' },
                ]
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
        data() {
            this.ruleForm = {
                productTypeName: this.data.productTypeName,
                productTypeStatus: this.data.productTypeStatus != 0 ? 1 : this.data.productTypeStatus
            }    
        }
    },
    created() {
        console.log(11)
        if (this.data.productTypeId) {
            this.ruleForm = {
                productTypeName: this.data.productTypeName,
                productTypeStatus: this.data.productTypeStatus != 0 ? 1 : this.data.productTypeStatus
            }    
        }
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.productTypeId) {
                        productTypeUpdate({
                            ...this.ruleForm,
                            productTypeId: this.data.productTypeId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        productTypeAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>