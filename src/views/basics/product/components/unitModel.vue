<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.productUnitId ? '编辑单位' : '新建单位'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        :modal='false'
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="ruleForm.unitName"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button size="mini" @click="resetForm('ruleForm')">取 消</el-button>
                <el-button type="primary" size="mini" @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { productUnitAdd, productUnitUpdate } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                unitName: ''
            },
            rules: {
                unitName: [
                    { required: true, message: '请输入单位名称', trigger: 'blur' },
                ]
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
        data() {
            this.ruleForm = {
                unitName: this.data.unitName
            }    
        }
    },
    created() {
        if (this.data.productUnitId) {
            this.ruleForm = {
                unitName: this.data.unitName
            }    
        }
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.productUnitId) {
                        productUnitUpdate({
                            ...this.ruleForm,
                            productUnitId: this.data.productUnitId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        productUnitAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>