<template>
  <div class="dialog_box">
    <!-- <el-dialog
        :title="data.productId ? '编辑产品' : '新建产品'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="75%"> -->
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="190px" style="padding-right: 50px">
            <div class="point_icon">
                <span>基础信息</span>
            </div>
            <el-row>
                  <el-col :span="12">
                    <el-form-item label="产品类型" prop="productTypeId">
                        <el-select v-model="ruleForm.productTypeId" placeholder="请选择" style="width: 100%" @change="productTypeChange">
                            <el-option v-for="(item, index) in prodTypeList" :key="index" :label="item.productTypeName" :value="item.productTypeId" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否为白条" prop="btFlag">
                        <el-select v-model="ruleForm.btFlag" placeholder="请选择" style="width: 100%" @change="btFlagChange">
                            <el-option label="是" :value="1" />
                            <el-option label="否" :value="0" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="ruleForm.btFlag == 1">
                    <el-form-item label="白条类型" prop="btType">
                        <el-select v-model="ruleForm.btType" placeholder="请选择" style="width: 100%" @change="btTypeChange">
                            <el-option
                                v-for="dict in dict.type.mes_bt_flag"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.label"
                            />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="ruleForm.btFlag == 1">
                    <el-form-item label="是否排酸" prop="acidFlag">
                        <el-select v-model="ruleForm.acidFlag" style="width: 100%" @change="acidFlagChange" placeholder="请选择">
                            <el-option label="是" :value="1" />
                            <el-option label="否" :value="0" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品名称" prop="productName">
                        <el-input v-model="ruleForm.productName" maxlength="25" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="内部产品编码" prop="productCode">
                        <el-input v-model="ruleForm.productCode" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="冷热产品" prop="hotOrCold" style="width: 100%">
                        <el-select v-model="ruleForm.hotOrCold" style="width: 100%" >
                            <el-option label="冷线" :value="2" />
                            <el-option label="热线" :value="1" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产许可证" prop="productionLicence">
                        <el-input v-model="ruleForm.productionLicence" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="执行标准" prop="productStandard">
                        <el-input v-model="ruleForm.productStandard" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="状态" prop="productStatus">
                        <el-select v-model="ruleForm.productStatus"  placeholder="请选择" style="width: 100%">
                            <el-option label="启用" :value="1" />
                            <el-option label="禁用" :value="0" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item required label="企业内部产品编码">
                        <el-row>
                            <el-col :span="10">
                                <el-form-item prop="productSourceCodeType">
                                    <el-select v-model="ruleForm.productSourceCodeType"  placeholder="请选择" style="width: 100%">
                                        <el-option
                                            v-for="dict in dict.type.mes_product_source_code_type_flag"
                                            :key="dict.value"
                                            :label="dict.label"
                                            :value="dict.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="14">
                                <el-form-item prop="productSourceCode">
                                    <el-input v-model="ruleForm.productSourceCode" placeholder="输入内容"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品图片" prop="productImage">
                        <uploadCard
                            :fileType="['png', 'jpg', 'jpeg']"
                            :fileSize="10"
                            :limit="1"
                            v-model="ruleForm.productImage"
                            :isShowTip="false"
                            style="margin-top: 14px"
                        >
                        </uploadCard>
                    </el-form-item>
                  </el-col>
            </el-row>
            <div class="point_icon">
                <span>参数信息</span>
            </div>
            <el-row>
                  <el-col :span="12">
                    <el-form-item label="称重类型" prop="weightingType">
                        <el-select v-model="ruleForm.weightingType" placeholder="请选择" style="width: 100%">
                            <el-option label="定重" :value="1" />
                            <el-option label="抄码" :value="2" />
                        </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="ruleForm.weightingType == 1 ? '基本单位重量（kg）' : '基本单位重量区间（kg）'" required>
                        <el-row>
                            <el-col :span="16" v-if="ruleForm.weightingType == 1">
                                <el-form-item prop="unitWeight">
                                    <el-input placeholder="输入内容" v-model="ruleForm.unitWeight" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16" v-else>
                                <el-row>
                                    <el-col :span="11">
                                        <el-form-item prop="unitWeight">
                                           <el-input placeholder="最小重量" v-model="ruleForm.unitWeight" type="number"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col class="fcc" :span="2">-</el-col>
                                        <el-col :span="11">
                                        <el-form-item prop="unitWeightEnd">
                                            <el-input placeholder="最大重量" v-model="ruleForm.unitWeightEnd" type="number"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="productUnitId">
                                    <el-select v-model="ruleForm.productUnitId" placeholder="请选择" @change="productUnitChange">
                                        <el-option v-for="(item, index) in prodUnitList" :key="index" :label="item.unitName" :value="item.productUnitId" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="包装比例" required>
                        <el-row>
                            <el-col :span="10">
                                <el-input value="1" disabled></el-input>
                            </el-col>
                            <el-col class="fcc" :span="2">:</el-col>
                            <el-col :span="10">
                                <el-form-item prop="packingRatio">
                                    <el-input v-model="ruleForm.packingRatio" placeholder="输入装箱比例"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col class="fcc" :span="2">
                                <el-tooltip effect="dark" class="fcc" content="包装比例为1箱的产品个数，如一箱中有10个产品，则包装比例为1：10">
                                    <i class="el-icon-question" style="line-height:40px"></i>
                                </el-tooltip>
                            </el-col>
                        </el-row>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="存储条件" prop="storageCondition">
                        <el-input v-model="ruleForm.storageCondition" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品配料" prop="productBurden">
                        <el-input v-model="ruleForm.productBurden" placeholder="输入内容"></el-input>
                    </el-form-item>
                  </el-col>
            </el-row>
            <div class="point_icon">
                <span>产品详情</span>
            </div>
            <el-row style="padding-left: 100px">
                <editor v-model="ruleForm.remark" :min-height="202"/>
            </el-row>
            <div class="fcc" style="margin: 30px 0">
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button type="primary"  size="mini"  @click="submitForm('ruleForm')">保 存</el-button>
            </div>
        </el-form>
    <!-- </el-dialog> -->
    <Model ref="modelRef" :data="{}" @getList='getNewList'></Model>
    <unitModel ref="unitModel" :data="{}" @getList='getNewList'></unitModel>
  </div>
</template>

<script>
import { productAdd, productUpdate, productTypeList, productUnitList } from "@/api/basics/index.js";
import Model from "./productTypeModel.vue";
import unitModel from "./unitModel.vue";
import uploadCard from '@/components/FileUpload/uploadCard.vue'
import { timingSafeEqual } from "crypto";
export default {
    dicts: ['mes_bt_flag', 'mes_product_source_code_type_flag'],
    data() { 
        var validatePass = (rule, value, callback) => {
            if (value && !Number.isInteger(+value)) {
                callback(new Error('请输入纯数字'));
            }
            callback()
        };
        return {
            dialogVisible: false,
            prodTypeList: [],
            prodUnitList: [],
            ruleForm: {
                productTypeId: '',
                productTypeName: '',
                productName: '',
                btFlag: '',
                btType: '', //白条类型（1胴体，2二分体，3四分体，4八分体，0其他），btFlag=1必传
                acidFlag: '',
                hotOrCold: '',
                productionLicence: '',
                productStandard: '',
                productStatus: 1,
                weightingType: '',
                unitWeight: '',
                unitWeightEnd: '',
                productUnitId: '',
                unitName: '',
                packingRatio: '',
                storageCondition: '',
                productBurden: '',
                remark: '',
                productCode: '',
                productImage: '',
                productSourceCode: '',
                productSourceCodeType: ''
            },
            rules: {
                productTypeId: [
                    { required: true, message: '请输入产品类型', trigger: 'blur' },
                ],
                productName: [
                    { required: true, message: '请输入产品名称', trigger: 'blur' },
                ],
                btFlag: [
                    { required: true, message: '请选择是否为白条', trigger: 'blur' },
                ],
                btType: [
                    { required: true, message: '请选择白条类型', trigger: 'blur' },
                ],
                acidFlag: [
                    { required: true, message: '请选择是否排酸', trigger: 'blur' },
                ],
                hotOrCold: [
                    { required: true, message: '请选择冷热产品', trigger: 'blur' },
                ],
                productStatus: [
                    { required: true, message: '请选择状态', trigger: 'blur' },
                ],
                productSourceCode: [
                    { required: true, message: '请输入识别码', trigger: 'blur' },
                    { validator: validatePass, trigger: 'blur' },
                ],
                weightingType: [
                    { required: true, message: '请选择称重类型', trigger: 'blur' },
                ],
                unitWeight: [
                    { required: true, message: '请输入单位重量', trigger: 'blur' },
                ],
                unitWeightEnd: [
                    { required: true, message: '请输入单位重量', trigger: 'blur' },
                ],
                productUnitId: [
                    { required: true, message: '请选择单位', trigger: 'change' },
                ],
                packingRatio:[
                    { required: true, message: '请输入包装比例', trigger: 'blur' },
                ], 
                productSourceCodeType:  [
                    { required: true, message: '请选择识别码类型', trigger: 'change' },
                ],
            }
        }
    },
    components: {
        Model,
        unitModel,
        uploadCard
    },
    props: {
        data: Object
    },
    watch: {
        data() {
            this.setInfoValue() 
        }
    },
    created() {
        if (this.data.productId) {
            this.setInfoValue()   
        }
        this.getSelectType()
    },
    methods: {
        setInfoValue() {
            this.ruleForm = {
                productTypeId: this.data.productTypeId,
                productTypeName: this.data.productTypeName,
                productName: this.data.productName,
                btFlag: this.data.btFlag,
                btType: this.data.btType,
                acidFlag: this.data.acidFlag,
                hotOrCold: this.data.hotOrCold,
                productCode:this.data.productCode,
                productionLicence: this.data.productionLicence,
                productStandard: this.data.productStandard,
                productStatus: this.data.productStatus,
                weightingType: this.data.weightingType,
                unitWeight: this.data.unitWeight,
                unitWeightEnd: this.data.unitWeightEnd,
                productUnitId: this.data.productUnitId,
                packingRatio: this.data.packingRatio,
                storageCondition: this.data.storageCondition,
                productBurden: this.data.productBurden,
                productImage: this.data.productImage,
                remark: this.data.remark,
                productSourceCode: this.data.productSourceCode,
                productSourceCodeType: this.data.productSourceCodeType + ''
            }
        },
        btFlagChange() {
            this.ruleForm.productName = ''
            this.ruleForm.btType = ''
            this.ruleForm.acidFlag = ''
            this.$refs.ruleForm.clearValidate(['productName', 'btType', 'acidFlag'])
        },
        btTypeChange() {
            if (this.ruleForm.btFlag == 1) {
                if (this.ruleForm.acidFlag == 1) {
                    this.ruleForm.productName = '排酸' +  this.ruleForm.btType 
                } else {
                    this.ruleForm.productName = this.ruleForm.btType 
                }
                
            }
        },
        acidFlagChange() {
            if (this.ruleForm.btFlag == 1) {
                if (this.ruleForm.acidFlag == 1) {
                    this.ruleForm.productName = '排酸' +  this.ruleForm.btType 
                } else {
                    this.ruleForm.productName = this.ruleForm.btType 
                }
            }
        },
        productTypeChange(value) {
            if (value == 'addProductType') {
                this.$refs.modelRef.showModel()
                this.ruleForm.productTypeId = ''
            }
        },
        productUnitChange(value) {
            if (value == 'addProductUnit') {
                this.$refs.unitModel.showModel()
                this.ruleForm.productUnitId = ''
            }
        },
        getNewList() {
            this.getSelectType()
            this.$emit('getNewList')
        },
        getSelectType() {
            productTypeList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                if (res.code == 200) {
                    this.prodTypeList = res.result.list.filter(item => {
                        return item.productTypeStatus == 1
                    });
                    this.prodTypeList.push({
                        productTypeId: 'addProductType',
                        productTypeName: '+ 新增产品类型'
                    })
                }
            });
            productUnitList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                if (res.code == 200) {
                    this.prodUnitList = res.result.list;
                    this.prodUnitList.push({
                        productUnitId: 'addProductUnit',
                        unitName: '+ 新增单位'
                    })
                }
            });
        },
        showModel() {
            this.getSelectType()
            this.dialogVisible = true;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.productId) {
                        productUpdate({
                            ...this.ruleForm,
                            productId: this.data.productId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        productAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
            this.$emit('close')
        }
    }
}
</script>

<style scoped>

.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding-left: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}
</style>