<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
                <el-form
                    :model="queryParams"
                    size="small"
                    :inline="true"
                    ref="formBox" class="form_box"
                >
                    <el-row class=" form_row">
                        <el-row class="form_col">
                            <el-form-item  prop="productCode">
                                <el-input
                                v-model="queryParams.productCode"
                                placeholder="产品编码"
                                />
                            </el-form-item>
                            <el-form-item  prop="productName">
                                <el-input
                                v-model="queryParams.productName"
                                placeholder="产品名称"
                                />
                            </el-form-item>
                            <el-form-item  prop="productTypeId">
                                <el-select v-model="queryParams.productTypeId" placeholder="产品类型">
                                    <el-option v-for="(item, index) in prodTypeList" :key="index" :label="item.productTypeName" :value="item.productTypeId" />
                                </el-select>
                            </el-form-item> 
                            <!-- <el-form-item label="冷热产品" prop="hotOrCold">
                                <el-select v-model="queryParams.hotOrCold" clearable>
                                    <el-option v-for="(item, index) in hotOrColdList" :key="index" :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item>  -->
                            <el-form-item  prop="weightingType">
                                <el-select v-model="queryParams.weightingType" placeholder="称重类型">
                                    <el-option v-for="(item, index) in weightingTypeList" :key="index" :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item> 
                            <el-form-item  prop="productStatus">
                                <el-select v-model="queryParams.productStatus" placeholder="状态">
                                    <el-option v-for="(item, index) in productStatusList" :key="index" :label="item.text" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-row>
                    </el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新 建</el-button>
                    <el-button class="default_btn" icon="el-icon-discount" size="mini" @click="productUnitModelShow = true">单位管理</el-button>
                    <el-button class="default_btn" icon="el-icon-news" size="mini" @click="productTypeModelShow = true">产品类型</el-button>
                    <el-button class="default_btn" icon="el-icon-video-play" size="mini" @click="setStatus(1)">启用</el-button>
                    <el-button class="default_btn" icon="el-icon-video-pause" size="mini" @click="setStatus(0)">禁用</el-button>
                    <el-button class="default_btn" size="mini" icon="el-icon-upload2" @click="handleExport">导出数据</el-button>
                    <el-button class="default_btn" size="mini" icon="el-icon-upload2" @click="handleTemplate">导入模版下载</el-button>
                    <el-button class="default_btn" size="mini" icon="el-icon-upload2" @click="handleImport">导入数据</el-button>
                    <el-button  class="default_device_btn" circle icon="el-icon-setting" size="mini" @click="setUp"></el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    border
                    v-loading="loading"
                    @select-all="handleSelectionChange"
                    :max-height="tableHeight"
                >
                    <el-table-column align="center" type="selection" width="50">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.check"></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column type="index" width="55" label="序号" align="center"></el-table-column>
                    <!-- <el-table-column prop="productCode" label="内部产品编码" />
                    <el-table-column prop="productName" label="产品名称"></el-table-column>
                    <el-table-column prop="productTypeName" label="产品类型"></el-table-column>
                    <el-table-column prop="hotOrCold" label="冷热产品" :formatter="formatHotOrColdList"></el-table-column>
                    <el-table-column prop="weightingType" label="称重类型" :formatter="formatWeightingType"></el-table-column>
                    <el-table-column prop="unitWeightName" label="规格单位"></el-table-column>
                    <el-table-column prop="packingRatio" label="包装比例"></el-table-column>
                    <el-table-column prop="productStatus" label="状态" :formatter="formatType"></el-table-column>
                    <el-table-column prop="updateUserName" label="创建人"></el-table-column>
                    <el-table-column prop="updateTime" label="更新时间"></el-table-column> -->
                    
                    <el-table-column v-for="(item, index) in tableColumn" :sortable="item.sortable" :key="index" :prop="item.prop" :align="item.align" :label="item.label" :min-width="item.width" show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" align="center" width='180' fixed="right">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-edit" size="mini" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button icon="el-icon-delete" size="mini" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-drawer
            class="drawer_box"
            :visible.sync="productUnitModelShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="1200px"
            title='单位管理'
            :wrapperClosable="false">
            <productUnitModel></productUnitModel>
        </el-drawer>
        <el-drawer
            class="drawer_box"
            :visible.sync="productTypeModelShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="1200px"
            title='产品类型管理'
            :wrapperClosable="false">
            <productTypeModel @getNewList="getSelectType"></productTypeModel>
        </el-drawer>

        <el-drawer
            class="drawer_box"
            :visible.sync="productModelShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            :title="currentData.productId ? '编辑产品' : '新建产品'"
            :wrapperClosable="false">
            <Model ref="modelRef" :data="currentData" @getList='getList' @getNewList="getSelectType" @close='productModelShow = false'></Model>
        </el-drawer>
        <draggableModel ref="draggableModel" @changeTable='changeTable' :defaultList='defaultList' :list='canChangeList' id='10006'></draggableModel>
    </div>
</template>
  
<script>
import Model from "./components/productModel.vue";
import productUnitModel from './unit.vue'
import productTypeModel from './productType.vue'
import draggableModel from '../../../components/draggableTable.vue'
import { productList, productDelete, productTypeList, exportData, batchUpdate, importData } from "@/api/basics/index.js";
import {exportExcel} from '@/utils/east-mind.js'
import { getMenuList } from "@/api/system/customMenu/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productCode: '', //产品编码
                productName: '', //产品名称
                productTypeId: '', //产品类型主键ID
                hotOrCold: '', //冷线还是热线
                weightingType: '', //称重类型（1定重，2抄码）
                productStatus: '' //状态（1启用，0禁用）
            },
            productModelShow: false,
            productUnitModelShow: false,
            productTypeModelShow: false,
            defaultList: [],
            canChangeList: [],
            tableColumn: [
                { check: true, prop: "productCode",label: "内部产品编码", width: 200, align: 'center'  },
                { check: true, prop: "productName", label: "产品名称", align: 'center', width: 160},
                { check: true, prop: "productTypeName", label: "产品类型", align: 'center', width: 140 },
                // { check: true, prop: "hotOrColdName", label: "冷热产品" },
                { check: true, prop: "weightingTypeName", label: "称重类型", align: 'center', width: 140 },
                { check: true, prop: "specification", label: "规格单位", align: 'center', width: 140 },
                { check: true, prop: "packingRatioName", label: "包装比例", align: 'center', width: 140 },
                { check: true, prop: "productStatusName", label: "状态", align: 'center', width: 140 },
                { check: true, prop: "updateUserName", label: "创建人", align: 'center', width: 140 },
                { check: true, prop: "updateTime", label: "更新时间", align: 'center', sortable: true , width: 160},
            ],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            prodTypeList: [],
            hotOrColdList: [
                { text: "冷线产品", value: 1 },
                { text: "热线产品", value: 2 },
            ],
            weightingTypeList: [
                { text: "定重", value: 1 },
                { text: "抄码", value: 2 },
            ],
            productStatusList: [
                { text: "启用", value: 1 },
                { text: "禁用", value: 0 },
            ],
            tableHeight: 0,
            tableBoxHeight: 0,
            sreachShow: false
        };
    },
    components: {
        Model,
        productUnitModel,
        productTypeModel,
        draggableModel
    },
    created() {
        this.defaultList = JSON.parse(JSON.stringify(this.tableColumn));
        this.canChangeList = JSON.parse(JSON.stringify(this.tableColumn));
        this.getMenu()
        this.getList();
        this.getSelectType()
    },
    methods: {
        //列表查询
        getList() {
            productList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list.map((item) => {
                        // if (item.weightingType == 2) {
                        //     item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + 'kg/' + item.unitName;
                        // } else {
                        //     item.unitWeightName = item.unitWeight + 'kg/' + item.unitName;
                        // }
                        item.check = false
                        item.packingRatioName = '1:' + item.packingRatio
                        this.productStatusList.forEach((i) => {
                            if (i.value === item.productStatus) {
                                item.productStatusName = i.text;
                            }
                        });
                        this.weightingTypeList.forEach((i) => {
                            if (i.value == item.weightingType) {
                                item.weightingTypeName = i.text;
                            }
                        });
                        this.hotOrColdList.forEach((i) => {
                            if (i.value == item.hotOrCold) {
                                item.hotOrColdName = i.text;
                            }
                        });
                        return item
                    });
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        getSelectType() {
            productTypeList({
                pageNum: 1,
                pageSize: 999,
            }).then((res) => {
                if (res.code == 200) {
                    this.prodTypeList = res.result.list;
                }
            });
        },
        handleSelectionChange(data) {
            this.tableData.map((item) => {
                item.check = data.length > 0 ? true : false;
                return item
            })
        },
        reset() {
            this.$refs.formBox.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        getMenu() {
            getMenuList({
                businessType: 10006
            }).then((res) => {
                const list = JSON.parse(res.result);
                this.changeTable(list)
            })
        },
        setUp() {
            this.$refs.draggableModel.showModel()
        },
        changeTable(dataList) {
            if (!dataList || dataList.length <= 0) { return }
            this.canChangeList = dataList
            this.tableColumn = []
            dataList.forEach(i => {
                if (i.check) {
                    this.tableColumn.push(i)
                }
            })
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 导出按钮操作 */
        handleExport() {
            console.log('导出')
            exportExcel(exportData,this.queryParams,'产品列表')
        },
        handleTemplate(){
              window.open('https://nmb-new.obs.cn-north-4.myhuaweicloud.com/template/mes-import-product.xlsx')
        },
        // 导入数据
        handleImport(){
            importData()
        },
        // 删除
        deleteEnclosure(row) {
            this.$confirm('是否删除该产品？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                productDelete({
                    productId: row.productId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
        setStatus(status) {
            const productIds = []
            this.tableData.forEach((item) => {
                if(item.check) {
                    productIds.push(item.productId)
                }
            })
            if(productIds.length <= 0) {
                this.$message.info('请先选择产品')
                return
            }
            this.$confirm(
                `
                    <div style='font-size:18px'>
                        <i class='el-icon-warning' style='color:#FF9900'></i>
                        变更产品为${status == 1 ? '启用' : '禁用'}状态
                    </div>
                    <div style='padding-left:22px'>
                        选中产品将同步变更
                    </div>
                `,
                "提示",
                {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                    batchUpdate({
                        productStatus: status,
                        productIds
                    }).then(res => {
                        this.$message.success(status == 1 ? '已启用' : '已禁用')
                        this.getList()
                    })
            });
    
        }
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  