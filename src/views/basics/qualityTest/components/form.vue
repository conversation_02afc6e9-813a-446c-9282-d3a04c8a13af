<template>
  <div class="dialog_box" style="padding-bottom: 80px;">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" style="padding-right: 50px">
            <el-row class="mt20">
                  <el-col :span="12">
                    <el-form-item label="质检报告类型" prop="materialsType">
                        <el-select v-model="ruleForm.materialsType" placeholder="请选择" style="width: 100%;">
                            <el-option
                            v-for="item in materialsTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                  </el-col>
            </el-row>
            <el-row class="mt20">
                  <el-col :span="12">
                    <el-form-item label="质检报告有效期" prop="effectiveStartDate">

                        <el-date-picker
                            v-model="ruleForm.effectiveDate"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            style="width: 100%;">
                        </el-date-picker>
                        <!-- <el-col :span="11">
                            <el-form-item prop="effectiveStartDate">
                                <el-date-picker
                                type="date"
                                placeholder="选择日期"
                                value-format="yyyy-MM-dd"
                                v-model="ruleForm.effectiveStartDate"
                                style="width: 100%;"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col class="fcc" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-form-item prop="effectiveEndDate">
                                <el-date-picker
                                placeholder="选择时间"
                                value-format="yyyy-MM-dd"
                                v-model="ruleForm.effectiveEndDate"
                                style="width: 100%;"></el-date-picker>
                            </el-form-item>
                        </el-col> -->
                    </el-form-item>
                  </el-col>
            </el-row>
            <el-row class="mt20">
                  <el-col :span="24">
                    <el-form-item label="质检报告" prop="qaReportPic">
                        <uploadCard
                            :fileType="['png', 'jpg', 'jpeg']"
                            :fileSize="10"
                            :limit="10"
                            v-model="ruleForm.qaReportPic"
                            :isShowTip="true"
                        >
                            <el-button type="primary" plain size="mini" class="lobtb">
                            <i class="el-icon-upload el-icon--right"></i>
                            上传图片
                            </el-button>
                        </uploadCard>
                    </el-form-item>
                  </el-col>
            </el-row>
            <el-row class="mt20">
                  <el-col :span="24">
                    <el-form-item label="质检过程图片" prop="qaReportFlowPic">
                        <uploadCard
                            :fileType="['png', 'jpg', 'jpeg']"
                            :fileSize="10"
                            :limit="10"
                            v-model="ruleForm.qaReportFlowPic"
                            :isShowTip="true"
                        >
                            <el-button type="primary" plain size="mini" class="lobtb">
                            <i class="el-icon-upload el-icon--right"></i>
                            上传图片
                            </el-button>
                        </uploadCard>
                    </el-form-item>
                  </el-col>
            </el-row>
        </el-form>
        <div class="fcc footer_btn">
            <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
            <el-button type="primary"  size="mini"  @click="submitForm('ruleForm')">提 交</el-button>
        </div>
  </div>
</template>

<script>
import { qaReportAdd, qaReportEdit, qaReportInfo } from "@/api/basics/index.js";
import uploadCard from '../../../../components/FileUpload/uploadCard.vue'
export default {
    data() {
        return {
            prodTypeList: [],
            prodUnitList: [],
            appDoe2: [],
            ruleForm: {
                materialsType: '', //质检报告类型（1羊 2牛）
                effectiveStartDate: '', //有效期开始时间
                effectiveEndDate: '', //有效期结束时间
                qaReportPic: '', //质检报告图url,逗号分割
                qaReportFlowPic: '', //质检流程图,逗号分割
                effectiveDate: []
            },
            rules: {
                materialsType: [
                    { required: true, message: '请选择质检报告类型', trigger: 'change' },
                ],
                // effectiveStartDate: [
                //     { required: true, message: '请选择有效期开始时间', trigger: 'change' },
                // ],
                effectiveDate: [
                    { required: true, message: '请选择有效期', trigger: 'change' },
                ],
                qaReportPic: [
                    { required: true, message: '请上传质检报告', trigger: 'change' },
                ],
            },
            materialsTypeList: [
                { label: '牛', value: 2 },
                { label: '羊', value: 1 },
            ]
        }
    },
    components: {
        uploadCard
    },
    props: {
        data: Object
    },
    watch: {
        data() {
            this.getInfo() 
        }
    },
    created() {
        if (this.data.qaReportId) {
            this.getInfo()   
        }
    },
    methods: {
        getInfo() {
            qaReportInfo({
                qaReportId: this.data.qaReportId
            }).then((res) => {
                const data = res.result
                this.ruleForm = {
                    materialsType: data.materialsType, //质检报告类型（1羊 2牛）
                    effectiveStartDate: data.effectiveStartDate, //有效期开始时间
                    effectiveEndDate: data.effectiveEndDate, //有效期结束时间
                    qaReportPic: data.qaReportPic, //质检报告图url,逗号分割
                    qaReportFlowPic: data.qaReportFlowPic, //质检流程图,逗号分割
                    effectiveDate: [data.effectiveStartDate, data.effectiveEndDate]
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const ruleForm = {
                        materialsType: this.ruleForm.materialsType, //质检报告类型（1羊 2牛）
                        effectiveStartDate: this.ruleForm.effectiveDate[0], //有效期开始时间
                        effectiveEndDate: this.ruleForm.effectiveDate[1], //有效期结束时间
                        qaReportPic: this.ruleForm.qaReportPic, //质检报告图url,逗号分割
                        qaReportFlowPic: this.ruleForm.qaReportFlowPic, //质检流程图,逗号分割
                    }
                    if (this.data.qaReportId) {
                        qaReportEdit({
                            ...ruleForm,
                            qaReportId: this.data.qaReportId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        qaReportAdd(ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.$emit('close')
        }
    }
}
</script>

<style scoped>

.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding-left: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}
</style>