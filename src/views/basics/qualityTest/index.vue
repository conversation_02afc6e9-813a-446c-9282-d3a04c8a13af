<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box"
                >
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item prop="startTime">
                            <el-date-picker
                                v-model="queryParams.startTime"
                                type="date"
                                placeholder="有效期"
                                value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe 
                    border
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                >
                    <el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号"></el-table-column>
                    <el-table-column  show-overflow-tooltip  align="center" label="质检报告类型">
                        <template slot-scope="scope">
                            {{ scope.row.materialsType == 1 ? '羊' : '牛' }}
                        </template>
                    </el-table-column>
                    <el-table-column  show-overflow-tooltip  label="质检报告有效期" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.effectiveStartDate }} - {{ scope.row.effectiveEndDate }}
                        </template>
                    </el-table-column>
                    <el-table-column  show-overflow-tooltip prop="updateUserName" align="center" label="维护人"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="updateTime" align="center" label="维护时间" sortable></el-table-column>
                    <el-table-column  show-overflow-tooltip label="操作" width="150" align="center">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-edit" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button icon="el-icon-delete" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-drawer
            class="drawer_box"
            :visible.sync="productModelShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="60%"
            :title="currentData.qaReportId ? '编辑质检报告' : '新建质检报告'"
            :wrapperClosable="false">
            <Model ref="modelRef" :data="currentData" @getList='getList' @close='productModelShow = false'></Model>
        </el-drawer>
    </div>
</template>
  
<script>
import Model from "./components/form.vue";
import { qaReportList,qaReportDelete } from "@/api/basics/index.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                startTime: ''
            },
            productModelShow: false,
            defaultList: [],
            canChangeList: [],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            prodTypeList: [],
        };
    },
    components: {
        Model,
    },
    created() {
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            qaReportList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list.map((item) => {
                        return item
                    });
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
            this.$refs.queryForm.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 删除
        deleteEnclosure(row) {
            this.$confirm('删除质检报告有可能会导致部分溯源码显示质检报告为空，确认删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                qaReportDelete({
                    qaReportId: row.qaReportId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  