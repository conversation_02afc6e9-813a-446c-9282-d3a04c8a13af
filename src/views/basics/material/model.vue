<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.materialsId ? '编辑原料' : '新建原料'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="原料名称" prop="materialsName">
                <el-input v-model="ruleForm.materialsName"></el-input>
            </el-form-item>
            <el-form-item label="原料编码" prop="materialsCode">
                <el-input v-model="ruleForm.materialsCode"></el-input>
            </el-form-item>
            <el-form-item label="原料类型" prop="materialsType">
                <el-select v-model="ruleForm.materialsType" style="width: 100%">
                    <el-option label="牛" :value="2" />
                    <el-option label="羊" :value="1" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary" @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { materialsAdd, materialsUpdate } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                materialsName: '',
                materialsType: '',
                materialsCode: ''
            },
            rules: {
                materialsName: [
                    { required: true, message: '请输入原料名称', trigger: 'blur' },
                ],
                materialsType: [
                    { required: true, message: '请输入原料类型', trigger: 'blur' },
                ]
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
    },
    created() {
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.data.materialsId) {
                    this.ruleForm = {
                        materialsName: this.data.materialsName,
                        materialsCode: this.data.materialsCode,
                        materialsType: this.data.materialsType
                    }
                } else {
                    this.ruleForm = {
                        materialsName: '',
                        materialsCode: '',
                        materialsType: ''
                    }
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.materialsId) {
                        materialsUpdate({
                            ...this.ruleForm,
                            materialsId: this.data.materialsId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        materialsAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>