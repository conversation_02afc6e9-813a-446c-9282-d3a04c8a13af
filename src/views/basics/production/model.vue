<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.productLineId ? '编辑生产线' : '新建生产线'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="生产线名称" prop="productLineName">
                <el-input v-model="ruleForm.productLineName"></el-input>
            </el-form-item>
            <el-form-item label="屠宰类型" prop="materialsType">
                <el-select v-model="ruleForm.materialsType" style="width: 100%">
                    <el-option label="牛" :value="2" />
                    <el-option label="羊" :value="1" />
                </el-select>
            </el-form-item> 
            <el-form-item label="负责人" prop="managerName">
                <el-input v-model="ruleForm.managerName"></el-input>
            </el-form-item>
            <el-form-item label="负责人电话" prop="managerPhone">
                <el-input v-model="ruleForm.managerPhone"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary"  @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { productLineAdd, productLineUpdate } from "@/api/basics/index.js";
export default {
    data() {
        const isMobile = (rule, value, callback) => {
            if (!/^1[3456789]\d{9}$/.test(value)) {
                callback(new Error("请输入正确有效的手机号"));
            } else {
                callback();
            }
        };
        return {
            dialogVisible: false,
            ruleForm: {
                productLineName: '',
                materialsType: '',
                managerName: '',
                managerPhone: '',
            },
            rules: {
                productLineName: [
                    { required: true, message: '请输入生产线名称', trigger: 'blur' },
                ],
                materialsType: [
                    { required: true, message: '请输入屠宰类型', trigger: 'blur' },
                ],
                managerName: [
                    { required: true, message: '请输入负责人', trigger: 'blur' },
                ],
                managerPhone: [
                    { required: true, message: '请输入负责人手机号', trigger: 'blur' },
                    { validator: isMobile, message: "请输入正确有效的手机号", trigger: "blur" },
                ],
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
    },
    created() {
        
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.data.productLineId) {
                    this.ruleForm = {
                        productLineName: this.data.productLineName,
                        materialsType: this.data.materialsType,
                        managerName: this.data.managerName,
                        managerPhone: this.data.managerPhone,
                    }    
                } else {
                    this.ruleForm = {
                        productLineName: '',
                        materialsType: '',
                        managerName: '',
                        managerPhone: '',
                    }   
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.productLineId) {
                        productLineUpdate({
                            ...this.ruleForm,
                            productLineId: this.data.productLineId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        productLineAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>