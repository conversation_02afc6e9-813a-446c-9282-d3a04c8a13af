<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form
                :model="queryParams"
                size="small"
                :inline="true"
                ref="formBox" class="form_box"
            >
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item label="" prop="levelName">
                            <el-input
                            v-model="queryParams.levelName"
                            placeholder="等级名称"
                            />
                        </el-form-item>
                        <el-form-item label="" prop="materialsType">
                            <el-select v-model="queryParams.materialsType" placeholder="原料类型">
                                <el-option v-for="(item, index) in materialsTypeList" :key="index" :label="item.text" :value="item.value" />
                            </el-select>
                        </el-form-item> 
                    </el-row>
                </el-row>
                <el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box" >
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col :span="24" class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                    <el-button  class="default_device_btn" circle icon="el-icon-setting" size="mini" @click="setUp"></el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe
                    border
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                >
                    <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                    <!-- <el-table-column prop="levelName" label="等级名称" />
                    <el-table-column prop="materialsType" label="原料类型" :formatter="formatType"></el-table-column>
                    <el-table-column prop="updateUserName" label="创建人"></el-table-column>
                    <el-table-column prop="updateTime" label="更新时间"></el-table-column> -->
                    <el-table-column v-for="(item, index) in tableColumn" :key="index" :sortable="item.sortable" :prop="item.prop" :align="item.align" :label="item.label"  show-overflow-tooltip/>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-edit" size="mini" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button icon="el-icon-delete" size="mini" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <Model ref="modelRef" :data="currentData" @getList='getList'></Model>
        <draggableModel ref="draggableModel" @changeTable='changeTable' :defaultList='defaultList' :list='canChangeList' id='10005'></draggableModel>
    </div>
</template>
  
<script>
import Model from "./model.vue";
import { materialsLevelList, materialsLevelDelete } from "@/api/basics/index.js";
import draggableModel from '../../../components/draggableTable.vue'
import { getMenuList } from "@/api/system/customMenu/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                levelName: '',
                materialsType: '',
            },
            defaultList: [],
            canChangeList: [],
            tableColumn: [
                { check: true, prop: "levelName", label: "等级名称",align: 'center' },
                { check: true, prop: "materialsTypeName", label: "原料类型", align: 'center' },
                { check: true, prop: "updateUserName", label: "创建人", align: 'center' },
                { check: true, prop: "updateTime", label: "更新时间", align: 'center', sortable: true },
            ],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            materialsTypeList: [
                { text: "牛", value: 2 },
                { text: "羊", value: 1 },
            ],
        };
    },
    components: {
        Model,
        draggableModel
    },
    created() {
        this.defaultList = JSON.parse(JSON.stringify(this.tableColumn));
        this.canChangeList = JSON.parse(JSON.stringify(this.tableColumn));
        this.getMenu()
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            materialsLevelList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list.map((i) => {
                        this.materialsTypeList.forEach((item) => {
                            if (item.value == i.materialsType) {
                                i.materialsTypeName = item.text;
                            }
                        });
                        return i
                    });
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
            this.$refs.queryForm.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        deleteEnclosure(row) {
            this.$confirm('是否删除该原料等级？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                materialsLevelDelete({
                    materialsLevelId: row.materialsLevelId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
        getMenu() {
            getMenuList({
                businessType: 10005
            }).then((res) => {
                this.changeTable(JSON.parse(res.result))
            })
        },
        setUp() {
            this.$refs.draggableModel.showModel()
        },
        changeTable(dataList) {
            if (!dataList || dataList.length <= 0) { return }
            this.canChangeList = dataList
            this.tableColumn = []
            dataList.forEach(i => {
                if (i.check) {
                    this.tableColumn.push(i)
                }
            })
        },
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  