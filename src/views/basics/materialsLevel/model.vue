<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.materialsId ? '编辑等级' : '新建等级'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="等级名称" prop="levelName">
                <el-input v-model="ruleForm.levelName"></el-input>
            </el-form-item>
            <el-form-item label="原料类型" prop="materialsType">
                <el-select v-model="ruleForm.materialsType" style="width: 100%">
                    <el-option label="牛" :value="2" />
                    <el-option label="羊" :value="1" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary" @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { materialsLevelAdd, materialsLevelUpdate } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                levelName: '',
                materialsType: ''
            },
            rules: {
                levelName: [
                    { required: true, message: '请输入等级名称', trigger: 'blur' },
                ],
                materialsType: [
                    { required: true, message: '请输入原料类型', trigger: 'blur' },
                ]
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
    },
    created() {
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.data.materialsLevelId) {
                    this.ruleForm = {
                        levelName: this.data.levelName,
                        materialsType: this.data.materialsType
                    }
                } else {
                    this.ruleForm = {
                        levelName: '',
                        materialsType: ''
                    }
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.materialsLevelId) {
                        materialsLevelUpdate({
                            ...this.ruleForm,
                            materialsLevelId: this.data.materialsLevelId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        materialsLevelAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>