<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-row :gutter="10">
                <el-form
                    :model="queryParams"
                    ref="queryForm"
                    size="small"
                    :inline="true"
                    class="form_box"
                >
                    <el-row class=" form_row">
                        <el-row class="form_col">
                            <el-form-item prop="productName">
                                <el-input
                                v-model="queryParams.productName"
                                placeholder="产品名称"
                                clearable
                                />
                            </el-form-item>
                            <el-form-item prop="createTime">
                                <el-date-picker
                                    v-model="createTime"
                                    type="daterange"
                                    value-format="yyyy-MM-dd"
                                    range-separator="至"
                                    start-placeholder="生产开始日期"
                                    end-placeholder="生产结束日期"
                                    style="width: 100%;">
                                </el-date-picker>
                                <!-- <el-date-picker
                                    v-model="queryParams.startTime"
                                    type="date"
                                    placeholder="请选择"
                                    value-format="yyyy-MM-dd">
                                </el-date-picker> -->
                            </el-form-item>
                        </el-row>
                    </el-row>
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
            </el-row>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb20">
                <el-col class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe border
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                >
                    <el-table-column  show-overflow-tooltip align="center" width="55" type="index" label="序号"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="productName" align="center" label="产品名称"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="productDate" align="center" label="生产日期" sortable></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="createUserName" align="center" label="创建人"></el-table-column>
                    <el-table-column  show-overflow-tooltip prop="createTime" align="center" label="创建时间" sortable></el-table-column>
                    <el-table-column  show-overflow-tooltip label="操作" width="150" align="center">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-edit" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button icon="el-icon-delete" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <el-drawer
            class="drawer_box"
            :visible.sync="productModelShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="60%"
            :title="currentData.qaReportId ? '编辑合格证' : '新增合格证'"
            :wrapperClosable="false">
            <Model ref="modelRef" :data="currentData" @getList='getList' @close='productModelShow = false'></Model>
        </el-drawer>
    </div>
</template>
  
<script>
import Model from "./components/form.vue";
import { certificateList,certificateDelete } from "@/api/basics/index.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
				productName:'',
                startTime: null,
				endTime: null
            },
			createTime:[],
			
            productModelShow: false,
            defaultList: [],
            canChangeList: [],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            prodTypeList: [],
        };
    },
    components: {
        Model,
    },
    created() {
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            certificateList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list.map((item) => {
                        return item
                    });
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
			this.createTime=[]
            this.$refs.queryForm.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.productModelShow = true;
            // this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
			this.handelData("startTime", "endTime", this.createTime);
            this.getList();
        },
		handelData(startTime, endTime, list) {
		    if (list?.length > 0) {
		        this.queryParams[startTime] = list[0];
		        this.queryParams[endTime] = list[1];
		    } else {
		        delete this.queryParams[startTime];
		        delete this.queryParams[endTime];
		    }
		},
        // 删除
        deleteEnclosure(row) {
            this.$confirm('确认删除当前合格证吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                certificateDelete({
                    certificateId: row.certificateId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  