<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
                <el-form
                    :model="queryParams"
                    size="small"
                    :inline="true"
                    ref="formBox" class="form_box"
                >
                    <el-row class=" form_row">
                            <el-row class="form_col">
                                <el-form-item prop="supplierName">
                                    <el-input
                                    v-model="queryParams.houseName"
                                    placeholder="圈舍名称"
                                    />
                                </el-form-item>
                            </el-row>
                    </el-row>
            
                <!-- <el-form-item label="供应商类型" prop="supplierType">
                    <el-select v-model="queryParams.supplierType" clearable>
                        <el-option label="企业" value="1" />
                        <el-option label="中间商" value="2" />
                        <el-option label="养殖户" value="3" />
                    </el-select>
                    </el-form-item> -->
                    <el-row>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            <template v-if="toggleSearchDom">
                                <el-button type="text" @click="packUp">
                                    {{ toggleSearchStatus ? '收起' : '展开' }}
                                    <i
                                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                    ></i>
                                </el-button>
                            </template>
                        </el-form-item>
                    </el-row>
                </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row class="mb8 form_btn">
                <el-col :span="24" class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                    <el-button  class="default_device_btn" circle icon="el-icon-setting" size="mini" @click="setUp"></el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe ref="el_table"
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                    border
                >
                    <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
                    <el-table-column v-for="(item, index) in tableColumn" :key="index" :prop="item.prop" :label="item.label" :sortable="item.sortable" :align="item.align" show-overflow-tooltip>
                </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-edit" size="mini" class="edit_text_btn" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button icon="el-icon-delete" size="mini" class="delete_text_btn" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <Model ref="modelRef" :data="currentData" @getList='getList'></Model>
        <draggableModel ref="draggableModel" @changeTable='changeTable' :defaultList='defaultList' :list='canChangeList' id='10002'></draggableModel>
    </div>
</template>
  
<script>
import Model from "./model.vue";
import { livestockHouseList, livestockHouseDelete } from "@/api/basics/index.js";
import draggableModel from '../../../components/draggableTable.vue'
import { getMenuList } from "@/api/system/customMenu/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                houseName: ''
            },
            defaultList: [],
            canChangeList: [],
            tableColumn: [
                { check: true, prop: "houseName", label: "圈舍名称",align: 'center' },
                { check: true, prop: "remark", label: "圈舍描述" },
                { check: true, prop: "updateUserName", label: "创建人",align: 'center' },
                { check: true, prop: "updateTime", label: "更新时间",align: 'center', sortable: true },
            ],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
        };
    },
    components: {
        Model,
        draggableModel
    },
    created() {
        this.defaultList = JSON.parse(JSON.stringify(this.tableColumn));
        this.canChangeList = JSON.parse(JSON.stringify(this.tableColumn));
        this.getMenu()
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            livestockHouseList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
            this.queryParams.houseName = '';
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        deleteEnclosure(row) {
            this.$confirm('是否删除该圈舍？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                livestockHouseDelete({
                    livestockHouseId: row.livestockHouseId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
        getMenu() {
            getMenuList({
                businessType: 10002
            }).then((res) => {
                this.changeTable(JSON.parse(res.result))
            })
        },
        setUp() {
            this.$refs.draggableModel.showModel()
        },
        changeTable(dataList) {
            if (!dataList || dataList.length <= 0) { return }
            this.canChangeList = dataList
            this.tableColumn = []
            dataList.forEach(i => {
                if (i.check) {
                    this.tableColumn.push(i)
                }
            })
        },
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  