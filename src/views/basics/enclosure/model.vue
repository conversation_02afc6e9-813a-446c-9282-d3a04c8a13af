<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.livestockHouseId ? '编辑圈舍' : '新建圈舍'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="圈舍名称" prop="houseName">
                <el-input v-model="ruleForm.houseName" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="圈舍描述" prop="remark">
                <el-input type="textarea" v-model="ruleForm.remark" maxlength="100" show-word-limit rows='4'></el-input>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary"  @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { livestockHouseAdd, livestockHouseUpdate } from "@/api/basics/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            ruleForm: {
                houseName: '',
                remark: ''
            },
            rules: {
                houseName: [
                    { required: true, message: '请输入圈舍名称', trigger: 'blur' },
                ]
            }
        }
    },
    props: {
        data: Object
    },
    watch: {
    },
    created() {
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.data.livestockHouseId) {
                    this.ruleForm = {
                        houseName: this.data.houseName,
                        remark: this.data.remark
                    }    
                } else {
                    this.ruleForm = {
                        houseName: '',
                        remark: ''
                    }    
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.livestockHouseId) {
                        livestockHouseUpdate({
                            ...this.ruleForm,
                            livestockHouseId: this.data.livestockHouseId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        livestockHouseAdd(this.ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.dialogVisible = false
        }
    }
}
</script>

<style>

</style>