<template>
  <div class="dialog_box" style="padding-bottom: 80px;">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" style="padding-right: 50px">
            <el-row class="mt20">
                  <el-col :span="12">
                    <el-form-item label="产品名称" prop="productId">
						
						<el-select
						    v-model.trim="ruleForm.productId"
						    filterable
						    remote
						    reserve-keyword
						    placeholder="请输入产品名称或编码"
						    :remote-method="remoteMethod"
						    :loading="loading">
						    <el-option
						      v-for="item in productList"
						      :key="item.productId"
						      :label="item.productName"
						      :value="item.productId">
						    </el-option>
						</el-select>
						
                        
                    </el-form-item>
                  </el-col>
            </el-row>
            <el-row class="mt20">
                  <el-col :span="12">
                    <el-form-item label="生产日期" prop="productDate">

                        <el-date-picker
                            v-model="ruleForm.productDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            style="width: 100%;">
                        </el-date-picker>
                        <!-- <el-col :span="11">
                            <el-form-item prop="effectiveStartDate">
                                <el-date-picker
                                type="date"
                                placeholder="选择日期"
                                value-format="yyyy-MM-dd"
                                v-model="ruleForm.effectiveStartDate"
                                style="width: 100%;"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col class="fcc" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-form-item prop="effectiveEndDate">
                                <el-date-picker
                                placeholder="选择时间"
                                value-format="yyyy-MM-dd"
                                v-model="ruleForm.effectiveEndDate"
                                style="width: 100%;"></el-date-picker>
                            </el-form-item>
                        </el-col> -->
                    </el-form-item>
                  </el-col>
            </el-row>
            <el-row class="mt20">
                  <el-col :span="24">
                    <el-form-item label="合格证" prop="imgUrl">
                        <uploadCard
                            :fileType="['png', 'jpg', 'jpeg']"
                            :fileSize="10"
                            :limit="10"
                            v-model="ruleForm.imgUrl"
                            :isShowTip="true"
                        >
                            <el-button type="primary" plain size="mini" class="lobtb">
                            <i class="el-icon-upload el-icon--right"></i>
                            上传图片
                            </el-button>
                        </uploadCard>
                    </el-form-item>
                  </el-col>
            </el-row>
            
        </el-form>
        <div class="fcc footer_btn">
            <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
            <el-button type="primary"  size="mini"  @click="submitForm('ruleForm')">提 交</el-button>
        </div>
  </div>
</template>

<script>
import { productList,certificateAdd,certificateInfo,certificateUpdate } from "@/api/basics/index.js";
import uploadCard from '../../../../components/FileUpload/uploadCard.vue'
export default {
    data() {
        return {

            ruleForm: {
				productId:'',//产品id
                productName: '', //产品名称
                productDate: '', //生产日期
               
                imgUrl: '', //合格证图片url,逗号分割
                
            },
			productList:[],

			loading: false,

            rules: {
                productId: [
                    { required: true, message: '请选择产品', trigger: 'change' },
                ],
                // effectiveStartDate: [
                //     { required: true, message: '请选择有效期开始时间', trigger: 'change' },
                // ],
                productDate: [
                    { required: true, message: '请选择生产日期', trigger: 'change' },
                ],
                imgUrl: [
                    { required: true, message: '请上传合格证图片', trigger: 'change' },
                ],
            },
 
        }
    },
    components: {
        uploadCard
    },
    props: {
        data: Object
    },
    watch: {
        data() {
            this.getInfo() 
        }
    },
    created() {
        if (this.data.certificateId) {
            this.getInfo()   
        }
    },
    methods: {
		async remoteMethod(searchValue){
			this.loading=true
			this.productList=[]
			let res=await productList({searchValue})
			this.loading=false
			if(res.code==200){
				this.productList=res.result?.list||[]
			}

		},
        async getInfo() {
			await this.remoteMethod(this.data.productName)
			
            certificateInfo({
                certificateId: this.data.certificateId
            }).then( (res) => {
                const data = res.result
				
                this.ruleForm = {

					productName: data.productName, //产品id
					productId: data.productId, //产品id
					productDate: data.productDate, //生产日期
					imgUrl: data.imgUrl, //合格证图片
                }
            })
        },
        submitForm(formName) {
			console.log(this.searchValue)
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const ruleForm = {
               
                        productId: this.ruleForm.productId, //产品id
                        productDate: this.ruleForm.productDate, //生产日期
                        imgUrl: this.ruleForm.imgUrl, //合格证图片
                        
                    }
                    if (this.data.certificateId) {
                        certificateUpdate({
                            ...ruleForm,
                            certificateId: this.data.certificateId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        certificateAdd(ruleForm).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.$emit('close')
        }
    }
}
</script>

<style scoped>

.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
    padding-left: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}
</style>