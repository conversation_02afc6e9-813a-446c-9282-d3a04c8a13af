<template>
	<div class="app-container">
		<el-card shadow="never" class="box-card form-card mb10">
			<el-row :gutter="10">
				<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
					<el-row class=" form_row">
						<el-row class="form_col">
							<el-form-item prop="equipmentPlaceId">
								<el-select v-model="queryParams.equipmentPlaceId" placeholder="安装地点" clearable>
									<el-option v-for="(item, index) in placeList" :key="index" :label="item.placeName"
										:value="item.equipmentPlaceId" />
								</el-select>
							</el-form-item>
							<el-form-item prop="equipmentPlaceDetail">
								<el-input v-model="queryParams.equipmentPlaceDetail" @keyup.enter.native="handleQuery" suffix-icon="el-icon-search" placeholder="请输入关键词搜索" clearable />
							</el-form-item>

						</el-row>
					</el-row>
					<el-row>
						<el-form-item>
							<el-button type="primary" icon="el-icon-search" size="mini"
								@click="handleQuery">搜索</el-button>
							<el-button  size="mini"  icon="el-icon-refresh" @click="resetQuery">重置</el-button>
							<template v-if="toggleSearchDom">
								<el-button type="text" @click="packUp">
									{{ toggleSearchStatus ? '收起' : '展开' }}
									<i
										:class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
								</el-button>
							</template>
						</el-form-item>
					</el-row>
				</el-form>
			</el-row>
		</el-card>
		<el-card shadow="never" class="table_box">
			<el-row :gutter="10" class="mb20 form_btn">
				<el-col class="fend">
					<el-button class="default_btn" size="mini" icon="el-icon-plus"
						@click="addEquipment">新增视频设备</el-button>
				</el-col>
			</el-row>
			<!-- 表格数据 -->
			<div class="videoList" >
				<el-row :gutter="20">
					<el-col :span="6" v-for='(item,index) in videoList'>
						<el-card shadow="hover">
							<div class="video-card" @mouseover="mouseover" @mouseout="mouseout">
								<div class="video-card-top" @click="playVideo(item)" ref='coverPicture'>
									<img class='coverPicture' :src="getCoverPicture(item.coverPicture)" alt="">
									<img class="playIcon" src="@/assets/images/playIcon.png" alt="">
									<div class="playStatus" :style="{'color':item.equipmentStatus == 0 ? '#04FF00' : '#FF4302'}">
										<i :style="{'background':item.equipmentStatus == 0 ? '#04FF00' : '#FF4302'}"
											class="dot"></i>{{ item.equipmentStatus == 0 ? '在线' : (item.equipmentStatus == 1 ? '离线' : '未知') }}
									</div>
								</div>
				
								<div class="video-card-mid">
									<span style="flex: 1;margin-right: 5px;">{{item.placeName}}</span>
									<div class="video-card-mid-right">
										<el-button class="mini" size="mini" @click="updateEquipment(item)">编辑</el-button>
										<el-button class="mini" size="mini" @click="deleteEquipment(item.equipmentId,index)">删除</el-button>
									</div>
								</div>
								<span style="width: 60%;">{{item.equipmentPlaceDetail}}</span>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</div>
			



			<pagination :pageSizes='[8]' v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize" @pagination="getList" />
		</el-card>
		<Model ref="modelRef" :data="currentData" :placeList='placeList' @getList='getList'></Model>
		<videoModel v-if="videoOpen.open" :videoOpen="videoOpen" @close="videoOpen.open=false"></videoModel>
	</div>
</template>

<script>
	import Model from "./model.vue";
	import videoModel from "./videoModel.vue";
	import {
		equipmentList,
		equipmentPlaceAdd,
		equipmentPlaceList,
		equipmentDelete
	} from "@/api/basics/index.js";
	import {
		tableUi
	} from "@/utils/mixin/tableUi.js";
	export default {
		mixins: [tableUi],
		data() {
			return {
				queryParams: {
					pageNum: 1,
					pageSize: 8,
					equipmentPlaceDetail: '',
					equipmentPlaceId: ''

				},
				currentData: {},

				visible: true,
				placeList: [],
				videoList: [],
				loading: true,
				total: 0,
				videoOpen: {
					open: false,
					url: "",

				}
			};
		},
		components: {
			Model,
			videoModel
		},
		created() {
			this.getList();
			this.getEquipmentPlaceList()
			
		},
		methods: {
			mouseover(e) {
				const playIcon = event.currentTarget.querySelector('.playIcon');
				if (playIcon) {
					playIcon.style.display = 'block';
				}
			},
			mouseout(e) {
				const playIcon = event.currentTarget.querySelector('.playIcon');
				if (playIcon) {
					playIcon.style.display = 'none';
				}
			},
			getCoverPicture(coverPicture) {
				return coverPicture || require('@/assets/images/coverPicture.png');
			},
			//列表查询
			getEquipmentPlaceList() {
				equipmentPlaceList({}).then(res => {
					console.log(res)
					if (res.code == 200) {
						this.placeList = res.result
					}
				})
			},
			getList() {
				equipmentList(this.queryParams).then((res) => {
					if (res.code == 200) {
						this.videoList = res.result.list
						this.total = Number(res.result.total);
						this.loading = false;
						this.$nextTick(()=>{
							console.log(this.$refs.coverPicture)
							let w=this.$refs.coverPicture[0].clientWidth
							this.$refs.coverPicture.forEach(item=>{
								item.style.height =0.65*w+'px'
							})
							console.log(w)
						})
						
					}
				});
			},
			resetQuery() {

				this.$refs.queryForm.resetFields();
				this.getList();
			},
			handleQuery() {
				this.queryParams.pageNum = 1;
				this.getList();
			},
			updateEquipment(row) {
				this.currentData = row
				this.$refs.modelRef.showModel()
			},
			deleteEquipment(equipmentId,index) {
				this.$confirm("此操作将永久删除, 是否继续?", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						distinguishCancelAndClose:true,
						type: "warning",
					})
					.then(() => {
						equipmentDelete({
							equipmentId
						}).then(res => {
							if (res.code == 200) {
								this.$message({
									type: "success",
									message: "删除成功!",
								});
								this.getList();
							}

						})
					}).catch(() => {
						document.querySelectorAll('button').forEach(item=>{
							item.blur()
						})
						// this.$message({
						// 	type: "info",
						// 	message: "已取消删除",
						// });
					});

			},
			//新增
			addEquipment() {
				this.currentData = {}
				this.$refs.modelRef.showModel()

				// this.$refs.modelRef.showModel()
			},
			playVideo(item) {
				this.videoOpen.open = true;
				this.videoOpen.url = item;
				console.log(item);
			},


		},
	};
</script>

<style lang="scss" scoped>
	
	.video-card {

		// height: 344px;
		background: #FFFFFF;
		// border-radius: 10px 10px 10px 10px;
		// opacity: 1;
		// border: 1px solid #EEEEEE;
		// padding: 20px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.video-card-mid {
			padding: 20px 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.el-button {
				background: #F4F4F4;
				border-radius: 2px 2px 2px 2px;
			}
		}

		.video-card-top {
			// height: 234px;
			// height: 200px;
			// flex:1;
			border-radius: 10px 10px 10px 10px;
			position: relative;

			.coverPicture {
				width: 100%;
				height: 100%;
				border-radius: 10px 10px 10px 10px;

			}

			.playIcon {
				width: 30px;
				height: 30px;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			.playStatus {
				position: absolute;
				right: 10px;
				top: 10px;
				color: #13ce66;
				display: flex;
				align-items: center;
				font-size: 14px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;

				.dot {
					margin-right: 5px;
					width: 6px;
					height: 6px;
					// background: #F85300;
					border-radius: 50%;
				}
			}
		}


		span {
			height: 14px;
			font-size: 14px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #333333;
			line-height: 14px;

			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	.el-card {
		border: 1px solid #EEEEEE !important;
		border-radius: 10px 10px 10px 10px !important;
		
	}
	::v-deep .el-card__body {
		
		padding: 20px !important;
		box-sizing: border-box !important;
	}
	.videoList{
		box-sizing: border-box !important;
		.el-card {
			
			margin-bottom: 15px;
		}
		
		
	}
	
	.mini{
		padding: 5px 13px;
		background: #F4F4F4;
		border-radius: 2px 2px 2px 2px;
		border: none;
	}

</style>