<template>
	<div class="dialog_box">
		<el-dialog :title="data.equipmentId ? '编辑视频设备' : '新增视频设备'" :visible.sync="dialogVisible"
			@close="resetForm('ruleForm')" width="30%">
			<el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="mt20" :status-icon='false'>

				<el-form-item label="设备类型" prop="equipmentBrand">
					<el-select v-model="form.equipmentBrand" placeholder="请选择设备类型" :disabled="disabled"
						style="width: 100%">
						<el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="萤石云账号" prop="equipmentAccount" v-if="form.equipmentBrand==11">
					<el-select v-model="form.equipmentAccount" filterable placeholder="请选择萤石云账号" :disabled="disabled"
						remote :remote-method="remoteMethod" style="width: 100%">
						<el-option v-for="item in ezvizList" :key="item.ezvizId"
							:label="(item.userName + '/' + item.userPhone)" :value="item.ezvizId"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="设备编号" prop="serialCode">
					<el-input v-model.trim="form.serialCode" placeholder="请输入设备编号"></el-input>
				</el-form-item>
				<el-form-item label="通道编号" prop="channelCode" v-if="form.equipmentBrand!=13">
					<el-input v-model.trim="form.channelCode" placeholder="请输入通道号"
						oninput="value=value.replace(/[^\d]/g,'')"></el-input>
				</el-form-item>
				<el-form-item label="是否可转动" prop="rotateFlag">
					<el-radio-group v-model="form.rotateFlag">
						<el-radio :label="1">是</el-radio>
						<el-radio :label="0">否</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-row>
					<el-col :span="12">
						<el-form-item label="安装地址" prop="equipmentPlaceId">

							<el-select v-model="form.equipmentPlaceId" placeholder="请选择" class='mySelect'
								:inline-message='true'>
								<el-option v-for="item in placeList" :key="item.equipmentPlaceId"
									:label="item.placeName" :value="item.equipmentPlaceId"></el-option>

								<el-option :value="null">
									<el-button size="mini" @click="addData" icon="el-icon-plus">新增</el-button>
								</el-option>
							</el-select>

						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item prop="equipmentPlaceDetail" label-width="0">
							<el-input v-model.trim="form.equipmentPlaceDetail" placeholder="请输入挂载地址">

							</el-input>
						</el-form-item>

					</el-col>
				</el-row>

				<el-form-item>
					<el-button size="mini" @click="resetForm('ruleForm')">取 消</el-button>
					<el-button size="mini" type="primary" @click="submitForm('ruleForm')">保 存</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>


		<el-dialog title='新增安装地点' :visible.sync="showPlaceDialog" @close="resetPlaceForm('placeForm')" width="30%">
			<el-form :model="placeForm" ref="placeForm" label-width="100px" class="mt20">
				<el-form-item label="地点名称" prop="placeName" :rules="[
					{ required: true, message: '请输入地点名称'},
				]">
					<el-input v-model.trim="placeForm.placeName" placeholder="请输入地点名称,如仓库"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button size="mini" @click="resetPlaceForm('placeForm')">取 消</el-button>
					<el-button size="mini" type="primary" @click="addEquipmentPlace('placeForm')">保 存</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>

<script>
	import {
		equipmentAdd,
		equipmentUpdate,
		equipmentInfo,
		equipmentPlaceAdd,
		getEzvizList
	} from "@/api/basics/index.js";
	export default {
		data() {
			const isMobile = (rule, value, callback) => {
				if (!/^1[3456789]\d{9}$/.test(value)) {
					callback(new Error("请输入正确有效的手机号"));
				} else {
					callback();
				}
			};
			return {
				dialogVisible: false,
				showPlaceDialog: false,
				typeList: [{
					label: '萤石云',
					value: '11'
				}, {
					label: '国标',
					value: '12'
				}, {
					label: 'AI',
					value: '13'
				}],
				disabled: false,
				form: {
					equipmentBrand: '11', //设备品牌 11萤石云 12国标 13AI
					serialCode: '',
					channelCode: '',
					equipmentPlaceId: "",
					equipmentPlaceDetail: '',
					equipmentAccount: '',
					rotateFlag: '0'
				},
				placeForm: {
					placeName: ''
				},
				ezvizList: [],
				rules: {
					serialCode: [{
						required: true,
						message: "请填写摄像头的编号",
						trigger: "blur",
					}, ],
					equipmentAccount: [{
						required: true,
						message: "请选择萤石云账号",
						trigger: "blur",
					}, ],
					channelCode: [{
						required: true,
						message: "请填写摄像头对应的通道编号",
						trigger: "blur",
					}, ],
					equipmentPlaceId: [{
						required: true,
						message: "请选择安装地点",
						trigger: "change",
					}, ],
					equipmentPlaceDetail: [{
						required: true,
						message: "请填写摄像头挂载地址",
						trigger: "blur",
					}, ],
					rotateFlag:[{
						required: true,
						message: "请选择是否可转动",
						trigger: "blur",
					}, ],
				},

			}
		},
		props: {
			data: Object,
			placeList: Array
		},
		watch: {},
		mounted() {
			console.log('新增设备')
			console.log(this.placeList)
			this.remoteMethod()

		},
		methods: {
			remoteMethod(value) {
				getEzvizList({
					userName: value,
					pageNum: 1,
					pageSize: 100
				}).then(res => {
					this.ezvizList = res.result.list;
				})
			},


			addData() {
				this.showPlaceDialog = true
			},
			showModel() {
				this.dialogVisible = true;
				this.$nextTick(() => {
					if (this.data.equipmentId) {
						this.form = {
							equipmentBrand: this.data.equipmentBrand,
							serialCode: this.data.serialCode,
							channelCode: this.data.channelCode,
							equipmentPlaceId: this.data.equipmentPlaceId,
							equipmentPlaceDetail: this.data.equipmentPlaceDetail,
							equipmentAccount: this.data.equipmentAccount,
							rotateFlag: this.data.rotateFlag
						}
						console.log(this.form)
					} else {
						this.form = {
							equipmentBrand: '11', //设备品牌 11萤石云 12国标 13AI
							serialCode: '',
							channelCode: '',
							equipmentPlaceId: "",
							equipmentPlaceDetail: '',
							equipmentAccount: '',
							rotateFlag: 0
						}
					}
				})
			},
			addEquipmentPlace(formName) {
				this.$refs[formName].validate((valid) => {
					if (valid) {
						equipmentPlaceAdd(this.placeForm).then(res => {
							this.$message.success('添加成功')

							this.resetPlaceForm('placeForm')
							this.$parent.getEquipmentPlaceList()
						})
					}
				})
			},
			submitForm(formName) {
				this.$refs[formName].validate((valid) => {
					if (valid) {
						if (this.data.equipmentId) {
							equipmentUpdate({
								...this.form,
								equipmentId: this.data.equipmentId
							}).then(() => {
								this.$message.success('编辑成功')
								this.$emit('getList')
								this.resetForm('ruleForm')
							})
						} else {
							equipmentAdd(this.form).then(() => {
								this.$message.success('添加成功')
								this.$emit('getList')
								this.resetForm('ruleForm')
							})
						}
					}
				});
			},


			resetForm(formName) {
				this.$refs[formName].resetFields();
				this.dialogVisible = false
			},
			resetPlaceForm(formName) {
				this.$refs[formName].resetFields();
				this.showPlaceDialog = false
			}
		}
	}
</script>
<style lang="scss" scoped>
	::v-deep .mySelect {

		.el-input__suffix {}

	}
</style>