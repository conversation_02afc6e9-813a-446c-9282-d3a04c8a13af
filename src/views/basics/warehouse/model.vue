<template>
  <div class="dialog_box">
    <el-dialog
        :title="data.materialsId ? '编辑仓库' : '新建仓库'"
        :visible.sync="dialogVisible"
        @close="resetForm('ruleForm')"
        width="30%">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
            <el-form-item label="仓库名称" prop="warehouseName">
                <el-input v-model="ruleForm.warehouseName" placeholder="输入内容"></el-input>
            </el-form-item>
            <el-form-item label="仓库类型" prop="warehouseType">
                <el-select v-model="ruleForm.warehouseType" style="width: 100%">
                    <el-option v-for="(item, index) in warehouseTypeList" :key="index" :label="item.text" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="库管员" prop="managerUserIds">
                <el-select v-model="ruleForm.managerUserIds" multiple style="width: 100%">
                    <el-option v-for="(item, index) in managerList" :key="index" :label="item.text" :value="item.userId" />
                </el-select>
            </el-form-item>
            <el-form-item label="仓库描述" prop="remark">
                <el-input type="textarea" v-model="ruleForm.remark" maxlength="100" show-word-limit rows='4'></el-input>
            </el-form-item>
            <el-form-item label="安全库存" prop="safetyStock">
                 <el-input v-model="ruleForm.safetyStock" placeholder="输入内容"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button size="mini"  @click="resetForm('ruleForm')">取 消</el-button>
                <el-button size="mini"  type="primary" @click="submitForm('ruleForm')">保 存</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { warehouseAdd, warehouseUpdate } from "@/api/basics/index.js";
import { listUser } from "@/api/production/index.js";
export default {
    data() {
        return {
            dialogVisible: false,
            warehouseTypeList: [
                { text: "排酸库", value: 1 },
                { text: "急冻库", value: 2 },
                { text: "成品库", value: 3 },
                { text: "其他", value: 4 },
            ],
            ruleForm: {
                warehouseName: '',
                warehouseType: '',
                managerUserIds: '',
                remark: '',
                safetyStock: ''
            },
            rules: {
                warehouseName: [
                    { required: true, message: '请输入仓库名称', trigger: 'blur' },
                ],
                warehouseType: [
                    { required: true, message: '请选择仓库类型', trigger: 'change' },
                ],
                managerUserIds: [
                    { required: true, message: '请选择库管员', trigger: 'change' },
                ]
            },
            managerList: []
        }
    },
    props: {
        data: Object
    },
    created() {
        this.remoteMethod()
    },
    methods: {
        showModel() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                if (this.data.warehouseId) {
                    this.ruleForm = {
                        warehouseName: this.data.warehouseName,
                        warehouseType: this.data.warehouseType,
                        managerUserIds: this.data.managerUserIds && this.data.managerUserIds.split(','),
                        remark: this.data.remark,
                        safetyStock: this.data.safetyStock
                    }
                } else {
                    this.ruleForm = {
                        warehouseName: '',
                        managerUserIds: '',
                        remark: '',
                        warehouseType: '',
                        safetyStock: ''
                    }
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.data.warehouseId) {
                        warehouseUpdate({
                            ...this.ruleForm,
                            managerUserIds: this.ruleForm.managerUserIds.join(','),
                            warehouseId: this.data.warehouseId
                        }).then(() => {
                            this.$message.success('编辑成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    } else {
                        warehouseAdd({
                            ...this.ruleForm,
                            managerUserIds: this.ruleForm.managerUserIds.join(',')
                        }).then(() => {
                            this.$message.success('添加成功')
                            this.$emit('getList')
                            this.resetForm('ruleForm')
                        })
                    }
                }
            });
        },
        resetForm(formName) {
            this.$refs.ruleForm.resetFields();
            this.dialogVisible = false
        },
        remoteMethod() {
            const userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
            listUser({
                pageNum: 1,
                pageSize: 999,
                tenantId: userInfo.tenantId
            }).then(res => {
                if(res.code == 200) {
                    this.managerList = res.result.list.map(item => {
                        item.text = item.corprateNam || item.nickName
                        return item
                    })
                } else {
                    this.managerList = [];
                }
            })
        },
    }
}
</script>

<style>

</style>