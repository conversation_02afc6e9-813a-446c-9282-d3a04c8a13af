<template>
    <div id="login" class="login" ref='page'>
        <div class="login_bg" ref="bg">
            <span>泌阳地理标志农产品云平台</span>
            <div class="bg_1">
                <div class="bg_2">
                    <h3>欢迎登录</h3>
                    <div class="input">
                        <img src="@/assets/biyang/user_icon_2.png">
                        <input type="text" style="background: transparent;" placeholder='请输入用户名' v-model="username"
                            @keyup.enter="login">
                    </div>
                    <div class="input pass">
                        <img src="@/assets/biyang/user_password_2.png">
                        <input type="password" style="background: transparent;" placeholder='请输入密码' v-model="password"
                            @keyup.enter="login">
                    </div>
                    <div class="btn" @click="login">
                        登录
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
	
	// 适配flex
	import '@/utils/flexible.js';
	import { login } from "@/api/biyang/index";
	import { getToken, setToken, removeToken } from '@/utils/auth'
export default {
    data() {
        return {
            username: '',
            password: '',
            
        };
    },
    components: {
    },
    computed: {

    },
    watch: {
       
    },
    mounted() {
       
    },
    methods: {
        

       

       
        login() {
            if (!this.username) {
                this.$message.error('请输入用户名');
                return;
            }
            if (!this.password) {
                this.$message.error('请输入密码');
                return;
            }
			login({username: this.username,password: this.password}).then(res=>{
				console.log(res)
				if(res.code==200){
					setToken(res.result.token)
					this.$router.push({
					    path: '/index'
					})
				}
				
			})
           
           
        },

        
        
    }
};
</script>

<style lang="scss" >
@import "@/assets/css/biyang.scss";	
</style>
<style lang="scss" scoped>


#app {
    .silder {
        .silderShow {
            display: none;
        }
    }
}

.login {
    color: #d3d6dd;
    // background-color: #000000;
    // height: 100%;
    background: url('~@/assets/biyang/login-bg.png');
    background-size: 100% 100%;
    width: 100%;
    // max-width: 1920px;
    height: 100vh;
    // max-height: 1080px;
    margin: 0 auto;

    .login_bg {
        // height: 100%;
        width: 100%;
        // max-width: 1920px;
        height: 100vh;
        // max-height: 1080px;
        padding: 0.2rem 0.2rem 0 0.2rem;
        // background-image: url("../assets/login_bg.png");
        background-size: 100% 100%;
        background-position: center center;

        span {
            position: absolute;
            top: 10%;
            left: 32%;
            z-index: 10;
            display: block;
            width: 11.4rem;
            height: 0.4rem;
            font-size: 0.6rem;
            font-family: PingFang SC-特粗, PingFang SC;
            // font-weight: normal;
            font-weight: 700;
            color: #FFFFFF;
            line-height: 0.4rem;
            letter-spacing: 5px;
        }
    }
}

.bg_1 {
    position: absolute;
    z-index: 999;
    // width: 8.54rem;
    // height: 6.26rem;
    left: 50%;
    top: 50%;
    transform: translate(-3.5rem, -3rem);
    width: 6.84rem;
    height: 6.46rem;
    // margin: 2.75rem auto 0;
    background-image: url("~@/assets/biyang/login.png");
    // background: rgba(0, 0, 0, 0.5);
    background-size: 100% 100%;
    background-position: center center;
    padding: 0.23rem 0.33rem 0.2rem;

}

.bg_2 {
    // width: 7.89rem;
    // height: 5.84rem;
    width: 6rem;
    height: 5.6rem;
    // background-image: url("../assets/login_bg_2.png");
    background-size: 100% 100%;
    background-position: center center;
    padding-top: 0.76rem;
    display: flex;
    flex-direction: column;
    align-items: center;

    h3 {
        height: 0.45rem;
        font-size: 0.45rem;
        color: #FFFFFF;
        letter-spacing: 5px;
        text-align: center;
        margin-bottom: .4rem;
        font-weight: 400 !important;
    }

    .input {
        width: 4.96rem;
        height: 0.73rem;
        background-color: #1a3046;
        // background-image: url("../assets/input_bg.png");
        // background: #1a2336;
        border: 1px solid #39434d;
        border-radius: 0.13rem 0.13rem 0.13rem 0.13rem;
        opacity: 0.5;

        background-size: 100% 100%;
        background-position: center center;
        display: flex;
        align-items: center;
        padding: 0 0.28rem;

        img {
            width: 0.44rem;
            height: 0.44rem;
            margin-right: 0.11rem;
            filter: brightness(100%);
        }

        input {
            flex: 1;
            height: 80%;
            background: transparent;
            // background: #FFFFFF;
            border: none;
            // color: #05E9F7;
            // color: #969696;
            color: #FFFFFF;
            font-size: 0.2rem;
            letter-spacing: 2px;
        }

        input::-webkit-input-placeholder {
            // color: #58C9D2;
            // color: #969696;
            color: #FFFFFF;
        }

        .code {
            // width: 0.93rem;
            // height: 0.38rem;
            width: 1.5rem;
            height: 0.5rem;
            cursor: pointer;
        }
    }

    .input.pass {
        margin: 0.23rem 0;
    }

    .btn {
        width: 4.94rem;
        height: 0.73rem;
        display: flex;
        align-items: center;
        justify-content: center;
        // background-image: url("../assets/btn_bg.png");
        background: #19ECFF;
        opacity: 1;
        border-radius: 0.13rem 0.13rem 0.13rem 0.13rem;
        background-size: 100% 100%;
        background-position: center center;
        font-size: 0.33rem;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #001226;
        line-height: 0.3rem;
        letter-spacing: 1px;
        margin-top: 0.38rem;
        cursor: pointer;
    }
}

.fillWidth {
    width: 100%;
    height: 100%;
    object-fit: fill;
    position: absolute;
    top: 0;
    left: 0;
}
</style>
