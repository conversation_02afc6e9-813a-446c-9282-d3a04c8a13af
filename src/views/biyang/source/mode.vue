<template>
	<div class="main" v-if="modeShow">
		<div class="box">
			<div class="header-top">
				<span>溯源码查询</span>
				<span @click="close" class="el-icon-close" style="cursor: pointer;"></span>
			</div>
			<div class="content">
				<div class="text-search">
					<el-input v-model="input" @keyup.enter="sreach" placeholder="请输入溯源码"></el-input>
					<el-button type="primary" class="btn" @click="sreach">搜索</el-button>
					<span @click="reset">重置</span>
				</div>
				<div v-if="isShow"
					style="display: flex;justify-content: center;margin-top: .99rem;padding-bottom: 1rem;">
					<div class="not">
						<img src="@/assets/biyang/camera.png" alt="">
						<span>抱歉，未查询到任何信息</span>
					</div>
				</div>
				<div class="information" v-if="textShow">
					<div style="display: flex;justify-content: center;margin-top: .45rem;">
						<div class="not">
							<span>溯源码：<i>{{traceCode}}</i></span>
							<span>累计扫码次数 {{info.scanNum}}次</span>
						</div>
					</div>
					<div class="box-slide">
						<div class="intro-box" v-for="(module,index) in info.moduleList" :key='index'>
							<div class="title">
								<span></span>
								<span>{{module.moduleName}}</span>
							</div>
							<!--  -->
							<template v-for="(item,index1) in module.detailList" >
							
								<div class="text">
									{{item.paramKey}}：
									<div class='picture'  v-if="item.paramType==6||item.paramType==3">
										<el-image 
										    style="width: 3.25rem;
								height: 2.13rem;"
										    :src="item.paramValue"
											:preview-src-list="[item.paramValue]"
										    >
										  </el-image>
										<!-- <img :src="item.paramValue" :alt="item.paramKey"> -->
									</div>
									
									<span v-else>{{item.paramValue}}</span>
								</div>
							</template>
						</div>
						
						<div class="intro-box" v-if='certificateShow'>
							<div class="title">
								<span></span>
								<span>合格证信息</span>
							</div>
							<template v-for="(item,index) in certificateInfo.imgUrl.split(',')" >
								<div class="text">
									合格证图片：
									<div class='picture'  >
										<el-image 
										    style="width: 3.25rem;
								height: 2.13rem;"
										    :src="item"
											:preview-src-list="certificateInfo.imgUrl.split(',')||[]"
										    >
										  </el-image>
									</div>
								</div>
							</template>
						</div>
						<!-- <div class="intro-box" style="display: flex;">
                            <div class="t-l">
                                <div class="title">
                                    <span></span>
                                    <span>产品信息</span>
                                </div>
                                <div class="text" v-for="(item,i) in info.moduleList[1].detailList" :key="i">
									{{item.paramKey}}：{{item.paramValue}}

                                </div>
                            </div>
                            <div class="t-r">
                                <img src="../../../assets/biyang/niu.png" alt="">
                            </div>
                        </div>
                        <div class="intro-box" style="display: flex;">
                            <div class="t-l">
                                <div class="title">
                                    <span></span>
                                    <span>生产追溯</span>
                                </div>
                                <div class="text" v-for="(item,i) in info.moduleList[2].detailList" :key="i">
                                	{{item.paramKey}}：{{item.paramValue}}
                                
                                </div>
                            </div>
                            <div class="t-r">
                                <img src="../../../assets/biyang/niu.png" alt="">
                            </div>
                        </div>
                        <div class="intro-box">
                            <div class="title">
                                <span></span>
                                <span>质检信息</span>
                            </div>
                            <div class="picture">
                                <img :src="item.paramValue" :alt="item.paramKey" v-for="(item,i) in info.moduleList[3].detailList">
                                
                            </div>
                        </div> -->
					</div>
				</div>
			</div>
		</div>

	</div>
</template>

<script>
	import {

		searchTraceCodeScan,
		byTraceCode
	} from "@/api/biyang/source";
	export default {
		props: {
			modeShow: {
				type: Boolean,
				default: false
			}
		},
		components: {


		},
		data() {
			return {
				input: '',
				isShow: false,
				textShow: false,
				info: {},
				traceCode:'',
				certificateInfo:null,//合格证信息
				certificateShow:false
			};
		},

		created() {
			var _this = this;
			document.addEventListener("keydown", _this.watchEnter);
		},
		destroyed() {
			//移除监听回车按键
			var _this = this;
			document.removeEventListener("keydown", _this.watchEnter);
		},
		mounted() {


		},
		methods: {
			sreach() {
				if (!this.input) {
					this.$message.error('请输入溯源码！');
					return
				}
				this.info = {}
				this.certificateShow=false
				searchTraceCodeScan({
					traceCode: this.input
				}).then(res => {
					console.log(res)
					this.traceCode=this.input
					if (res.code == 200 && res.result) {
						console.log(this.input, '88');

						this.info = res.result
						this.isShow = false
						this.textShow = true
						
						byTraceCode({traceCode: this.input}).then(res1=>{
							console.log(res1)
							if(res1.code==200&&res1.result){
								this.certificateShow=true
								this.certificateInfo=res1.result
							}
						})
					} else {
						this.isShow = true
						this.textShow = false

					}
				})

			},
			//监听回车按钮事件
			watchEnter(e) {
				var keyNum = window.event ? e.keyCode : e.which; //获取被按下的键值
				//判断如果用户按下了回车键（keycody=13）
				if (keyNum == 13) {
					this.sreach()
				}
			},
			close() {
				var mo = function(e) {
					e.preventDefault()
				}
				document.body.style.overflow = '' // 出现滚动条
				document.removeEventListener('touchmove', mo, false)
				this.$emit('close')
			},
			reset() {
				this.input = '',
					this.isShow = false,
					this.textShow = false
			},

		},
	};
</script>
<style lang="scss" scoped>
	.main {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.9);
		// opacity: 1;
		z-index: 999;

		.box {
			width: 11.25rem;
		}

		.header-top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 .25rem;
			width: 100%;
			height: 0.48rem;
			background: #00E0DB;
			border-radius: 0.05rem 0.05rem 0rem 0rem;
			opacity: 1;

			span {
				font-size: 0.23rem;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
			}
		}

		.content {
			width: 100%;
			min-height: 3.25rem;
			max-width: 11.46rem;
			padding: 0 .5rem .25rem .38rem;
			background: url('../../../assets/biyang/mode-bg.png');
			background-size: 100% 100%;

			.text-search {
				display: flex;
				align-items: center;
				padding-top: .29rem;
				margin-left: 1.55rem;

				.el-input {
					width: 6.12rem;
					// height: .63rem;
					background-color: #FFFFFF;
					background: #FFFFFF;
					border-radius: 0.08rem 0.08rem 0.08rem 0.08rem;
					opacity: 1;
					border: 0.01rem solid #6D6D6D;
					color: #000000;


					::v-deep .el-input__inner {
						color: #000000;
						width: 6.12rem;
						height: .63rem !important;

					}
				}

				.btn {
					width: 1.13rem;
					height: .62rem;
				}

				::v-deep .el-button--primary {
					font-size: 0.23rem;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 700;
					background-color: #00E0DB !important;
					color: #032929 !important;
					border-color: #00E0DB !important;
				}

				span {
					font-size: 0.23rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #FFFFFF;
					margin-left: .46rem;
					cursor: pointer;
				}
			}

			.not {
				display: flex;
				flex-direction: column;
				align-items: center;

				img {
					display: block;
					width: 1.23rem;
					height: 1.23rem;
				}

				span {
					font-size: 0.18rem;
					font-family: Source Han Sans CN-Normal, Source Han Sans CN;
					font-weight: 350;
					color: #FFFFFF;
				}
			}

			.information {
				.not {
					display: flex;
					flex-direction: column;
					align-items: center;

					span:nth-child(1) {
						display: flex;
						align-items: center;
						font-size: 0.3rem;
						font-family: PingFang SC-Medium, PingFang SC;
						font-weight: 400;
						color: #FFFFFF;

						i {
							font-style: normal;
							font-size: 0.43rem;
							font-family: DIN-Bold, DIN;
							font-weight: 700;
							color: #FFFFFF;
						}
					}

					span:nth-child(2) {
						margin-top: .3rem;
						font-size: 0.28rem;
						font-family: PingFang SC-Medium, PingFang SC;
						font-weight: 400;
						color: #FFFFFF;
					}

					span {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Normal, Source Han Sans CN;
						font-weight: 350;
						color: #FFFFFF;
					}
				}

				.box-slide {
					height: 8.73rem;
					overflow: auto;
					// padding-top: .25rem;

					.intro-box {
						margin-top: .37rem;

						.text {
							
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
							line-height: 0.36rem;
							margin-top: .15rem;
							margin-left: 0.25rem;
						}

						.t-l {
							flex: 1;
						}

						.t-r {
							width: 3.25rem;
							margin-top: .3rem;

							img {
								width: 100%;
								height: 2.13rem;
							}
						}

						.picture {
							// display: inline-block;
							margin-top: .29rem;
							width:30%;
							// margin-left: 3%;
							img {
								width: 3.25rem;
								height: 2.13rem;
								
							}
						}
					}
				}
			}

			.title {
				display: flex;
				align-items: center;

				span:nth-child(1) {
					width: 0.05rem;
					height: 0.2rem;
					background: #00E0DB;
					border-radius: 0.03rem 0.03rem 0.03rem 0.03rem;
					opacity: 1;
				}

				span:nth-child(2) {
					font-size: 0.2rem;
					font-family: Source Han Sans CN-Bold, Source Han Sans CN;
					font-weight: 700;
					color: #00E0DB;
					margin-left: .15rem;
				}
			}
		}

	}
</style>