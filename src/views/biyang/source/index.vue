<template>
	<div class="wrap">
		<!-- <div class="content"> -->
		<div class="left-box">
			<div class="left-box-t box-public">
				<card-head titleIcon='title41'>
				</card-head>
				<dv-scroll-board :config="config" style="width:100%;height:3.2rem;margin-top: .25rem;" />
			</div>
			<div class="left-box-c box-public">
				<card-head titleIcon='title42'>
				</card-head>
				<div id="barChart"></div>
			</div>
			<div class="left-box-b box-public">
				<card-head titleIcon='title43'>
				</card-head>
				<div class="pie-box">
					<dv-active-ring-chart :config="config1" style="width:50%;height:3rem" />
					<div class="pie-type" style="margin-left: .25rem;">
						<div class="txt"  v-for="(item,i) in companyScanNumList" :key="i">
							<span :style="{backgroundColor:colors[i]}"></span>
							<span>{{item.name}}</span>
							<span>{{item.pct}}%</span>
						</div>
					</div>
					<!-- <div class="pie-type">
						<div class="txt" v-for="(item,i) in list" :key="i">
							<span :style="{backgroundColor:item}"></span>
							<span>A企业</span>
							<span>42%</span>
						</div>
					</div> -->
				</div>
			</div>
		</div>
		<div class="right-box">
			<div class="right-box-t box-public">
				<card-head titleIcon='title44'>
				</card-head>
				<div class="number-map-box">
					<div class="number-box">
						<div class="content" style="margin-top: .4rem;">
							<span class="hand">溯源码累计发放(个)</span>
							<ul>
								<li v-for="(item, i) in totalFinalNum.split('')" :key="i">{{ item }}</li>
							</ul>
						</div>
						<div class="content" style="margin-top: .54rem;">
							<span class="hand">消费者累计核验(次)</span>
							<ul>
								<li v-for="(item, i) in totalScaningNum.split('')" :key="i">{{ item }}</li>
							</ul>
						</div>
					</div>
					<div class="map-box">
						<div class="bm-view" id="container"></div>
					</div>
				</div>
			</div>
			<div class="right-box-b box-public" style="overflow: hidden;">
				<card-head style="justify-content: space-between;" titleIcon='title45'>
					<div class="search" @click="searchMode">
						<img src="@/assets/biyang/search.png" alt="">
						<span>查询溯源码</span>
					</div>
				</card-head>
				<dv-scroll-board :config="config2" style="margin-top: .34rem;overflow: hidden;" />
			</div>
		</div>
		<mode v-if="modeShow" :modeShow="modeShow" @close="close"></mode>
		<!-- </div> -->
	</div>
</template>

<script>
import cardHead from '../components/card-head.vue'
import mode from './mode.vue'

import AMapLoader from '@amap/amap-jsapi-loader';
import heatmapData from './heatmap'
import {
		checkProductTop10,
		companyTraceCodeNum,
		companyScanNum,
		traceCodeScanHeatMap,
		traceCodeScanRecord,
		searchTraceCodeScan,
		traceCodeSingleCount
	} from "@/api/biyang/source";
export default {
	components: {
		mode,
		cardHead
	},
	data() {
		return {
			img: require('@/assets/biyang/watch.png'),
			map: null,
			centers: {
				lng: 104.114129,
				lat: 38.450339
			},
			heatmap: {},
			config: {},
			config1: {},
			config2: {},

			growdata: [],
			numberList: ['2', '6', '6', '6', '6', '9'],
			modeShow: false,

			companyScanNumList:[],
			mapData:[],
			totalFinalNum:'0',
			totalScaningNum:'0',
		};
	},

	created() {

	},
	mounted() {
		
		
		this.$nextTick(() => {
			
			
			this.getTraceCodeScanHeatMap()
		})
		this.getTraceCodeScanRecord()
		this.getCheckProductTop10()
		this.getCompanyTraceCodeNum()
		this.getCompanyScanNum()
		this.getTraceCodeSingleCount()
		
	},
	methods: {
		getTraceCodeSingleCount(){
			traceCodeSingleCount({}).then(res=>{
				if(res.code==200){
					this.totalFinalNum=res.result.finalNum
					this.totalScaningNum=res.result.scaningNum
					
				}
			})
		},
		getTraceCodeScanRecord(){
			traceCodeScanRecord({}).then(res=>{
				if(res.code==200){
					let list=[]
					res.result.forEach(item=>{
						list.push([item.createTime, item.traceCode, item.companyName, item.productName, item.nickName, `<img style="display:block;z-index:999;width:0.33rem;height:0.33rem;margin-top:.13rem" src="${item.avatarUrl}" alt="">`, item.scaningAddress],)
					})
					this.$nextTick(() => {
						
						this.config2={
							data:list,
							header: ['查询时间', '查询溯源码', '所属企业', '产品名称', '消费者昵称', '头像', '扫描位置'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum: 8,
						}
						
					})
					
				}
			})
		},
		getTraceCodeScanHeatMap(){
			traceCodeScanHeatMap({"pageNum": 1, "pageSize": 1000 }).then(res=>{
				if(res.code==200){
					
					if(res.result&&res.result.list.length>0){

							res.result.list.forEach((item,i)=>{
								this.mapData.push({
										"lng": item.longitude,
										"lat": item.latitude,
										"count": 10
								})
							})
		
						
					}
					console.log(this.mapData)
					this.initAMap()
					
				}
			})
		},
		getCompanyScanNum(){
			companyScanNum({}).then(res=>{
				if(res.code==200){
					let list=[]
					let total=res.result.reduce((accumulator, currentValue) => accumulator + Number(currentValue.scanNum), 0);
					res.result.forEach(item=>{
						list.push({
							name: item.companyName,
							value: Number(item.scanNum),
							pct:((Number(item.scanNum)/total)*100).toFixed(2)
						})
					})
					this.companyScanNumList=list
					this.config1={
						radius: '40%',
						activeRadius: '45%',
						data: list,
						digitalFlopStyle: {
							fontSize: 16
						},
						showOriginValue: true
					}
				}
			})
		},
		getCompanyTraceCodeNum(){
			companyTraceCodeNum({}).then(res=>{
				if(res.code==200){
					let xaxisList=[]
					let yaxisLeftList=[]
					let yaxisRightList=[]
					res.result.forEach(item=>{
						xaxisList.push(item.xaxis)
						yaxisLeftList.push(item.yaxisLeft)
						yaxisRightList.push(item.yaxisRight)
					})
					
					this.growdata=[
						{ title: "申码量", label:xaxisList, value:yaxisLeftList },
						{ title: "赋码量", label:xaxisList, value: yaxisRightList },
					]
					this.barChart(this.growdata, 'barChart')
				}
			})
		},
		getCheckProductTop10(){
			checkProductTop10({}).then(res=>{
				if(res.code==200){
					let list=[]
					res.result.forEach((item,index)=>{
						list.push([
							index+1,item.companyName,item.productName,item.scanNum
						])
					})
					this.config={
						header: ['排行', '所属企业', '产品名称', '查询次数'],
						data: list,
						headerBGC: '#202020',
						oddRowBGC: '#131313',
						evenRowBGC: '#131313',
						columnWidth: [50]
					}
				}
			})
		},
		searchMode() {
			console.log(12123);
			this.modeShow = true
			var mo = function (e) { e.preventDefault() }
			document.body.style.overflow = 'hidden'
			document.addEventListener('touchmove', mo, false)
		},
		close() {
			this.modeShow = false
		},
		//柱状图
		barChart(data, dom) {
			// var a=document.ge
			// console.log(data);
			var chart = this.$echarts.init(document.getElementById(dom));
			var option = {
				legend: {
					top: "3%",
					right: "2%",
					itemWidth: 8,
					itemHeight: 8,
					padding: [10, 0, 0, 0],
					data: [
						{
							name: data[0].title,
							textStyle: {
								color: '#0EDAE2'
							},
						},
						{
							name: data[1].title,
							textStyle: {
								color: '#D6FF84'
							},
							// bottom: 10
						}
					]
				},
				grid: {
					top: '25%',
					left: '8%',
					right: '2%',
					bottom: '8%',
					// containLabel: true,
				},
				xAxis: {
					type: 'category',
					axisLabel: {
						color: '#00E0DB',
					},
					axisLine: {
						show: true, // X轴 网格线 颜色类型的修改
						lineStyle: {
							color: 'rgba(0,224,219,0.1)',
						},
					},
					scale: false,
					axisTick: {
						// X轴线 刻度线
						// show: true,
						// inside: true,
						// alignWithLabel: true
						show: false,
					},
					data: data[0].label
				},
				yAxis:
				{
					name: '数量：个',
					nameTextStyle: {
						color: '#00E0DB',
						padding: [3, 40, 0, 0],
						lineHeight: 45,
						// align:'left'
					},
					// nameLocation:'end',
					type: 'value',
					splitNumber: 4,
					axisLine: {
						show: true, // Y轴线
						lineStyle: {
							color: 'rgba(0,224,219,0.1)',
						},
					},
					splitLine: {
						lineStyle: {
							type: 'dashed',
							color: 'rgba(255,255,255,0.05)'
						}
					},
					axisLabel: {
						color: '#00E0DB',
					},
				},

				series: [
					{
						name: data[0].title,
						data: data[0].value,
						type: 'bar',
						symbol: 'circle',
						barWidth: 10,
						barGap: 0,
						itemStyle: {
							opacity: 1,
							color: '#0EDAE2'
							// color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
							// 	{
							// 		offset: 1,
							// 		color: '#0BE5E1',
							// 	},
							// 	{
							// 		offset: 0,
							// 		color: '#06FDF5',
							// 	},
							// ]),
						},
					},
					{
						name: data[1].title,
						data: data[1].value,
						type: 'bar',
						symbol: 'circle',
						barWidth: 10,
						barGap: 0,
						itemStyle: {
							opacity: 1,
							color: '#D6FF84'
							// color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
							// 	{
							// 		offset: 1,
							// 		color: '#2C9FFE',
							// 	},
							// 	{
							// 		offset: 0,
							// 		color: '#00ACFC',
							// 	},
							// ]),
						},
						// backgroundStyle: {
						//     color: 'rgba(44, 118, 254, 0.2)'
						// },
					},
				],
				tooltip: {//鼠标悬浮提示数据
					trigger: 'axis',
					axisPointer: {
						type: 'line'
					},
					// formatter: function (params) {


					// },
					backgroundColor: 'rgba(0,0,0,0.8);',
					borderColor: '#292929',
					borderWidth: 1,
					textStyle: {
						color: '#fff',
					},
				}
			};
			chart.clear()
			chart.setOption(option)
			window.addEventListener('resize', () => {
				chart.resize()
			})
		},
		initAMap() {
			
			let map = new T.Map("container");
		    //设置显示地图的中心点和级别
			map.centerAndZoom(new T.LngLat(116.40969, 39.89945), 3.5);
			map.setStyle('black')//black  indigo
			
			console.log(this.mapData)
			let heatmapOverlay = new T.HeatmapOverlay({
				// "radius": 30,
				radius: 18, //给定半径
				opacity: [0, 0.8]
			});
			map.addOverLay(heatmapOverlay);
			heatmapOverlay.setDataSet({data: this.mapData, max: 60});
			return 
			
		},

	},
};
</script>
<style lang="scss" scoped>
.wrap {
	background: url(~@/assets/biyang/bg1.png);
	padding: 0.25rem;
	// padding-top: 1.25rem;
	padding-top: 1.25rem;
	background-size: 100% 100%;
	// .content {
	display: flex;

	.left-box {
		.left-box-t {
			width: 7.5rem;
			height: 4rem;

		}

		.left-box-c {
			width: 7.5rem;
			height: 3.75rem;

			#barChart {
				margin-top: .25rem;
				width: 100%;
				height: 2.8rem;
			}

		}

		.left-box-b {
			width: 7.5rem;
			height: 3.63rem;

			.pie-box {
				display: flex;
				width: 100%;

				.pie-type {
					
					display: flex;
					justify-content: flex-start;
					// flex-direction: column;
					width:50%;
					flex-wrap: wrap;
					flex: 1;
					margin-left: .5rem;
					// margin-top: .7rem;

					.txt {
						width:50%;
						display: flex;
						align-items: center;
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Normal, Source Han Sans CN;
						font-weight: 350;
						color: #FFFFFF;
						// padding:.1rem 0;

						span:nth-child(1) {
							width: 0.08rem;
							height: 0.08rem;
							// background: #0EDAE2;
							border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
							opacity: 1;
						}

						span:nth-child(2) {
							padding-left: .05rem;
							min-width: .65rem;
						}

						span:nth-child(3) {}
					}
				}
			}
		}

	}

	.box-public {
		padding: .25rem;
		margin-top: .2rem;
		background: rgba(0, 0, 0, 0.26);
		opacity: 1;
	}

	.right-box {
		width: 15.75rem;
		margin-left: .25rem;

		.right-box-t {
			width: 100%;
			height: 5.81rem;

			.number-map-box {
				margin-top: .2rem;
				display: flex;

				.number-box {
					width: 4.3rem;
					height: 4.88rem;
					margin-left: .25rem;
				}

				.map-box {
					margin-left: .25rem;
					width: 11rem;
					height: 4.88rem;

					.bm-view {

						width: 100%;
						height: 4.88rem;

						::v-deep .amap-copyright {
							display: none !important;
						}

						::v-deep .amap-logo {
							display: none !important;
						}
					}


				}
			}
		}

		.right-box-b {
			width: 100%;
			height: 5.81rem;

			.search {
				display: flex;
				align-items: center;
				width: 1.5rem;
				height: 0.38rem;
				background: rgba(1, 207, 199, 0.0588);
				opacity: 1;
				border: 0.01rem solid rgba(1, 207, 199, 0.4);
				cursor: pointer;

				img {
					display: block;
					width: .25rem;
					height: .25rem;
					margin-left: .11rem;
				}

				span {
					margin-left: .14rem;
					font-size: 0.18rem;
					font-family: Source Han Sans CN-Normal, Source Han Sans CN;
					font-weight: 350;
					color: #01CFC7;
				}
			}
		}
	}

	.hand {
		font-size: 0.18rem;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #FFFFFF;
	}

	ul {
		margin-top: .1rem;
		display: flex;

		li {
			width: 0.4rem;
			height: 0.45rem;
			background: rgba(1, 207, 199, 0.0588);
			box-shadow: inset 0rem 0rem 0.08rem 0rem rgba(0, 224, 219, 0.2);
			border-radius: 0rem 0rem 0rem 0rem;
			opacity: 1;
			border: 0.01rem solid rgba(1, 207, 199, 0.102);
			font-size: 0.3rem;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #01CFC7;
			line-height: 0.5rem;
			text-align: center;
			margin-right: .14rem;
		}
	}

	// }
	::v-deep .ceil {
		font-size: 0.15rem;
		font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		font-weight: 400;
		color: #BFBFBF;
	}
}

::v-deep .tdt-control-copyright{
		display: none !important;
	}
</style>