<template>
	<div class="wrap">
		<div class="header">
			<div class="tabs">
				<div class='tab' :class="index==activeIndex?'active':''" v-for="(item ,index) in  tabs" :key='index'
					@click="onclick(item,index)">{{item.name}}</div>
			</div>
			<h1 class="title">泌阳地理标志农产品云平台</h1>
			<div class="info">
				<span class="time">
					{{timer}}
				</span>
				<span class="date">
					{{date}}
				</span>
				<span class="week">
					{{week}}
				</span>
				<span class="logout" @click="logout">
					<img src="@/assets/biyang/logout.png" alt="">
					退出
				</span>
			</div>
		</div>
		<router-view></router-view>
	</div>

</template>

<script>
	// 适配flex
	import {
		getToken,
		setToken,
		removeToken
	} from '@/utils/auth'
	import '@/utils/flexible.js';
	import {
	
			getConfigByKey
		} from "@/api/biyang/index";
	
	export default {
		props: {
			// activeIndex: {
			// 	type: Number,
			// 	default: 0,
			// },

		},
		data() {
			return {
				activeIndex: 0,
				week: null,
				date: null,
				timer: null,
				tabs: [{
						name: '综合概览',
						path: '/index'
					},
					{
						name: '生产加工',
						path: '/slaughter'
					},
					{
						name: '活畜养殖',
						path: '/breed'
					},
					{
						name: '质量溯源',
						path: '/source'
					},
				]
			};
		},

		async created () {
			
			// let res=await getConfigByKey ({})
			// if(res.code==200){
			// 	res.result.configValue
			// }
			setInterval(() => {
				this.createClock()
			}, 1000)
			console.log(this.$route.path)
			this.activeIndex = this.tabs.findIndex(item => item.path == this.$route.path)
			if (this.$route.path == '/slaughter/detail') {
				this.activeIndex = 1
			}
			if (this.$route.path == '/breed/detail') {
				this.activeIndex = 2
			}
		},

		methods: {
			createClock() {
				var date = new Date();
				//date.getDay()拿到的值是阿拉伯数字，将他换成汉字
				var week = date.getDay()
				switch (week) {
					case 1:
						week = "星期一"
						break;
					case 2:
						week = "星期二"
						break;
					case 3:
						week = "星期三"
						break;
					case 4:
						week = "星期四"
						break;
					case 5:
						week = "星期五"
						break;
					case 6:
						week = "星期六"
						break;
					case 0:
						week = "星期日"
						break;
				}

				// 时间没有在此处一次性组合完毕是为了方便在 输出时美化
				// 组合年月日
				let year = date.getFullYear()
				let month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
				let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
				let hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
				let minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
				let seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();

				this.week = week
				this.date = year + '-' + month + '-' + day
				this.timer = hours + ':' + minutes + ':' + seconds
			},
			onclick(item, index) {
				if (this.$route.path == item.path) {
					return
				}
				this.activeIndex = index
				console.log(item, index)
				this.$router.push({
					path: item.path
				})
			},
			logout() {
				removeToken()
				location.href = '/login';

			}

		},
	};
</script>
<style lang="scss">
	@import "@/assets/css/biyang.scss";

	.el-dropdown {
		color: #fff;
	}
	
</style>
<style lang="scss" scoped>
	// .wrap{
	// 	width:100%;
	// 	height:100vh;
	// }
	.header {
		background: url(~@/assets/biyang/header.png);
		background-size: 100% 100%;
		display: flex;
		width: 100%;
		height: 1.03rem;
		justify-content: space-between;
		padding: 0 0.38rem;
		align-items: center;
		position: relative;

		z-index: 999;
		position: absolute;
		top: 0;
		left: 0;

		// width: 24rem;
		// height: 1.01rem;
		.tabs {
			display: flex;
			align-items: center;

			.tab {
				cursor: pointer;
				font-size: 0.23rem;
				font-family: Source Han Sans CN-Normal, Source Han Sans CN;
				font-weight: 350;
				color: #00E0DB;
				text-align: center;
				margin-right: 0.18rem;
				width: 1.5rem;
				height: 0.44rem;
				line-height: 0.44rem;
			}

			.active {

				background: url(~@/assets/biyang/btn.png);
				background-position: center;
				background-size: cover;
			}
		}

		.title {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			font-size: 0.5rem;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: rgba(0, 246, 236, 0.8392);
			line-height: 0.6rem;
			text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.302);
		}

		.info {
			display: flex;
			align-items: center;

			.time {
				font-size: 0.3rem;
				font-family: Source Han Sans CN-Bold, Source Han Sans CN;
				font-weight: 700;
				color: #FFFFFF;
				margin-right: 0.2rem;
			}

			.date {
				font-size: 0.23rem;
				font-family: Source Han Sans CN-Normal, Source Han Sans CN;
				font-weight: 350;
				color: #FFFFFF;
				margin-right: 0.18rem;
			}

			.week {
				font-size: 0.23rem;
				font-family: Source Han Sans CN-Normal, Source Han Sans CN;
				font-weight: 350;
				color: #FFFFFF;
				margin-right: 0.2rem;
			}

			.logout {
				    display: flex;
				    align-items: center;
				font-size: 0.25rem;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
				cursor: pointer;
				img{
					margin-right: 0.13rem;
					width:0.25rem;
					height:0.25rem;
				}
			}
		}
	}
</style>