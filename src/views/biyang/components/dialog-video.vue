<template>

	<el-dialog
	  :destroy-on-close='true'
	  :close-on-click-modal='false'
	  ref='dialog'
	  title="查看实时监控"
	  :visible="dialogVisible"
	  width="17.75rem"
	
	  :before-close="close">
	  <div class='dialog-wrap'>
		  <div class='dialog-name'>{{activeVideo?activeVideo.mountLocation:''}}</div>
		  <div class='dialog-content flex jc-between'>
			  <div id='video1'>
				  <div  v-if='showVideo'>
					  <div id='video2' style="width:100%;height:100%;" ></div>
				  </div>
				  
				  
			  </div>
			  <div class='video-list'>
				  <div class="video-item"  v-for="(item,index) in videoList" :key='index'>
					  <span class='video-name'>{{item.mountLocation}}</span>
					  <div class='video'  @click='setVideo(item,index)'>
						  <img  :src="item.coverPicture||require('@/assets/biyang/video_default.png')" alt="">
						  <div :class="activeIndex==index?'active':''">
							  <span v-if='player'>正在播放</span>
							  <span v-else style="color:#fff;">当前视频已离线</span>
						  </div>
						  
					  </div>
					  
				  </div>
			  </div>
		  </div>
	  </div>
	</el-dialog>


</template>

<script>
	import {
		getPlayUrl
	} from "@/api/biyang/home";
	export default {
		props: {
			
			dialogVisible:{
				type:Boolean,
				default:false
			},
			videoList:{
				type:Array,
				default:()=>{[]}
			},
			width:{
				Type:String,
				default:'50%'
			}
			
		},
		data() {
			return {
				player:null,
				activeVideo:{mountLocation:''},
				activeIndex:0,
				showVideo:true
			};
		},
		watch: {
			dialogVisible(e) {
				if (e) {
				
					this.setVideo(this.videoList[0],0)
				}else{
					this.player&&this.player.stop();
					this.player=null
				}
			}
		},
		created() {
	
		},
		methods: {
			setVideo(e,i){
				this.player&&this.player.stop();
				this.showVideo=false
				this.activeVideo=e
				this.activeIndex=i
				this.player&&this.player.stop();
				this.player=null
				getPlayUrl({
					deviceCode: e.deviceCode,
					channelCode: e.channelCode,
					ezvizId: e.ezvizId
				}).then(res=>{
					if(res.code==200){
						this.showVideo=true
						this.$nextTick(function() {
							
							let w=document.getElementById('video1').offsetWidth
							let h=document.getElementById('video1').clientHeight 
							
							console.log(w,h)
						    this.player = new EZUIKit.EZUIKitPlayer({
						    	id: 'video2',
						    	url: res.result.url,
						    	accessToken: res.result.accessToken,
						    	template: "pcLive",
								width: w,
								height: h,
								showPTZControl: 'flex'
						    })
				
						});
						
						
				
					}else{
						
					}
					console.log(res)
				}).catch(res=>{
					console.log('hahahahahhhhhhhhhhh')
					this.player=null
					console.log(this.showVideo)
				})
			},
			close(){
				this.$parent.dialogVisible=false
			
			},
		},
	};
</script>

<style  lang="scss" scoped>
	::v-deep .el-dialog{
		background: url('~@/assets/biyang/video_bg.png');
		background-size: 100% 100%;
		box-shadow: 0px 0px 0.15rem 0px #00E0DB inset;
		
	}
	::v-deep .el-dialog__header{
		background:#00E0DB;
		height: 0.48rem;
		padding: 0 0.25rem;
		position: relative;
	}
	::v-deep .el-dialog__title{
		color: #000;
		font-size: 0.23rem;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		line-height: 0.48rem;
	}
	::v-deep .el-dialog__headerbtn{
		top: 50%;
		transform: translateY(-50%);
		
	}
	::v-deep .el-dialog__close{
		color: #000 !important;
	}
	::v-deep .el-dialog__body{
		padding: 0;
	}
	
	.dialog-wrap{
		padding: 0.25rem;
		padding-top: 0;
		.dialog-name{
			padding-left: 0.26rem;
			font-size: 0.2rem;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: 700;
			color: #00E0DB;
			height: 0.72rem;
			line-height: 0.72rem;
			position: relative;
		}
		.dialog-name::before{
			content:'';
			width: 0.11rem;
			height: 0.2rem;
			background: #00E0DB;
			position: absolute;
			left:0;
			top: 50%;
			transform: translateY(-50%);
		}
		.dialog-content{
			#video1{
				width: 14.34rem;
				height: 8.68rem;
				background: #3c3c3c;
				.video2{
					width:100%;height:100%;
				}
			}
			.video-list{
				overflow-y: scroll;
				height: 8.68rem;
				.video-item{
					
					.video-name{
						height: 0.2rem;
						font-size: 0.15rem;
						font-family: Source Han Sans CN-Medium, Source Han Sans CN;
						font-weight: 500;
						color: #FFFFFF;
						line-height: 0.23rem;
					}
					.video{
						position: relative;
						background: #3c3c3c;
						margin-top:0.11rem ;
						margin-bottom: 0.32rem;
						width: 2.69rem;
						height: 1.63rem;
						img{
							display: block;
							width:100%;
							height: 100%;
						}
						span{
							display:none;
						}
					}
					.active{
						background: rgba(0,0, 0, 0.4);
						position: absolute;
						width:100%;
						height: 100%;
						top:0;
						left:0;
						span{
							display:block;
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
							position: absolute;
							top:50%;
							left:50%;
							transform: translate(-50%,-50%);
						}
					}
				}
			}
		}
		
	}
</style>