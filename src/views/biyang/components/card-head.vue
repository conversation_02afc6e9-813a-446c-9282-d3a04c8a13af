<template>
	<div class="warp">

		<img class="icon" src="@/assets/biyang/circle.png" alt="" v-if='iconVisible'>
		<img class="title" :src='"@/assets/icons/svg/biyang/title/"+titleIcon+".svg"' alt="">
		<img class="line" src="@/assets/biyang/decoration.png" alt="" v-if='decorationVisible'>

		<slot></slot>

	</div>
</template>

<script>
	export default {
		props: {
			titleIcon: {
				type: String,
				default: 'icon-title1',
			},
			iconVisible: {
				type: Boolean,
				default: false
			},
			decorationVisible: {
				type: Boolean,
				default: false
			},
			dropdownVisible: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {

			};
		},

		created() {

		},
		methods: {


		},
	};
</script>
<style lang="scss" scoped>
	.warp {
		display: flex;
		align-items: center;
		position: relative;

		.icon {
			width: 0.3rem;
			height: 0.3rem;
			margin-right: 0.16rem;
		}

		.title {
			margin-right: 0.16rem;
		}

	}
</style>