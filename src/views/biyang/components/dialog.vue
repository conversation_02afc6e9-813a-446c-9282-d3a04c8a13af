<template>

	<div v-if="dialogShow" class="box_">
		<div class="dialog" v-if="dialogType==0">
			<span class="text" style="text-align: center;left: 50%;transform: translateX(-50%);">{{ dialogData.title }}</span>
			<span class="el-icon-close close_icon" @click="closeIcon"></span>
			<div class="picture">
				<div class="p_left">
					<img src='https://xmb.obs.cn-north-4.myhuaweicloud.com/produce/2023/06/8i1685699786005.jpg'  alt="11">
				</div>
				<div class="p_right">
					<div class="info_1">
						<span v-for="(item, i) in dialogData.data" :key="i">{{ item.name }}</span>
					</div>
					<div class="info_">
						<span v-for="(item, i) in dialogData.data" :key="i"
							:style="{ color: item.value == '监管中' ? '#FF5A0E' : '#FFFFFF' }">{{
		                                            item.value
		                                        }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="dialog_" v-else>
			<span class="text" style="left: 50%;
					transform: translateX(-50%);">{{ dialogData.title }}</span>
			<span class="el-icon-close close_icon" @click="closeIcon"></span>
			<div class="table" style="margin-top: 1.2rem;">
				<dv-scroll-board  :config="dialogData.data" class="sheet"
					style="width:14.9rem;min-height:3rem;padding-bottom:0.2rem;margin:auto;" />
				<<!-- div v-if="12 >= 10" class="num">
					共计{{ total }}条
					<span class="el-icon-arrow-left" @click="btnClick('left')"
						:class="{ num_change: numStatus === 'left' }"></span>
					<span class="el-icon-arrow-right" @click="btnClick('right')"
						:class="{ num_change: numStatus === 'right' }"></span>
				</div> -->
			</div>
		</div>
	</div>


</template>

<script>
	export default {
		props: {
			dialogType: {
				type: Number,
				default: 0,
			},
			dialogShow: {
				type: Boolean,
				default: false
			},
			dialogData:{
				type:Object,
				default:()=>{}
			}
			
		},
		data() {
			return {
	
			};
		},
	
		created() {
	
		},
		methods: {
			closeIcon(){
				this.$parent.dialogShow=false
			},
		},
	};
</script>

<style  lang="scss" scoped>
	.box_ {
	        width: 100%;
	        height: 100%;
	        display: flex;
	        justify-content: center;
	        align-items: center;
	        position: fixed;
	        top: 0;
	        left: 0;
	        background: rgba(0, 0, 0, .8);
	        z-index: 9999;
	
	    }
		
		 .dialog_ {
		        // margin-top: -2.5rem;
		        min-width: 15.79rem;
		        min-height: 3.77rem;
		        position: relative;
		        background: url('~@/assets/biyang/info.png');
		        background-size: 100% 100%;
		
		        .text {
		            font-size: 0.3rem;
		            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		            font-weight: 500;
		            color: #032929;
		            position: absolute;
		            top: 0.2rem;
					left: 50%;
					transform: translateX(-50%);
		            // left: 7.2rem;
		        }
		
		        .close_icon {
		            position: absolute;
		            top: .25rem;
		            right: .38rem;
		            color: #00e0db;
		            font-size: .3rem;
		            cursor: pointer;
		        }
		
		        .num {
		            font-size: 0.13rem;
		            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		            font-weight: 400;
		            color: #FFFFFF;
		            display: flex;
		            justify-content: flex-end;
		            margin: 0 .38rem 0.26rem 0;
		
		            span {
		                color: #7B8A8A;
		                font-size: 0.18rem;
		                font-weight: 700;
		                margin-left: .21rem;
		                cursor: pointer;
		            }
		        }
		
		    }
		
		    .dialog {
		        // margin-top: -2.5rem;
		        // min-width: 8.79rem;
		        min-width: 10.79rem;
		        // min-height: 5.81rem;
		        min-height: 5.81rem;
		        position: relative;
		        background: url('~@/assets/biyang/info.png');
		        background-size: 100% 100%;
		
		        .text {
		            font-size: 0.3rem;
		            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		            font-weight: 500;
		            color: #032929;
		            position: absolute;
		            top: 0.25rem;
		            left: 3.8rem;
		        }
		
		        .close_icon {
		            position: absolute;
		            top: .25rem;
		            right: .38rem;
		            color: #00e0db;
		            font-size: .3rem;
		            cursor: pointer;
		        }
		
		        .picture {
		            display: flex;
		            margin-top: 1.22rem;
		            margin-left: .5rem;
		
		            .p_left {
		                // width: 3rem;
		                // height: 4.1rem;
		                width: 4.5rem;
		                height: 5.1rem;
		
		                img {
		                    width: 100%;
		                    // height: 3rem;
		                    height: 90%;
		                    border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		                    cursor: pointer;
		
		                }
		
		                .img {
		                    display: flex;
		                    margin-top: .18rem;
		
		                    img {
		                        width: .88rem;
		                        height: .88rem;
		                        border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		                        cursor: pointer;
		
		                    }
		
		                    img:nth-child(2) {
		                        margin: 0 .18rem;
		                    }
		                }
		            }
		
		            .p_right {
		                flex: 1;
		                // height: 4.1rem;
		                height: 4.6rem;
		                margin-left: .63rem;
		                display: flex;
		                // justify-content: space-around;
		
		                .info_1 {
		                    display: flex;
		                    flex-direction: column;
		                    justify-content: space-around;
		                    // align-items: center;
		                    font-size: 0.2rem;
		                    font-family: PingFang SC-中等, PingFang SC;
		                    font-weight: normal;
		                    color: #0DD3DD;
		                }
		
		                .info_ {
		                    display: flex;
		                    flex-direction: column;
		                    justify-content: space-around;
		                    // align-items: center;
		                    font-size: 0.2rem;
		                    font-family: PingFang SC-中等, PingFang SC;
		                    font-weight: normal;
		                    color: #FFFFFF;
		                    margin-left: .34rem;
		                }
		
		            }
		        }
		
		    }
		


</style>