<template>
	<div class="flex align-items-center back" @click="back">
		<img class='back-icon' src="@/assets/icons/svg/biyang/back.svg" alt="">
		<span class="back-text">返回</span>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import back from '../components/back.vue'
	export default {
		components: {
			back,
			cardHead
		},

		methods:{
			back(){
				this.$router.go(-1)
			}
		}
		
	}
</script>

<style scoped lang="scss">
	.back{
		margin-top: 0.06rem;
		cursor: pointer;
		width: 0.9rem;
		.back-icon{
			width: 0.38rem;
			height: 0.38rem;
			margin-right: 0.05rem;
		}
		.back-text{
			font-size: 0.2rem;
			font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
			font-weight: 400;
			color: #00E0DB;
		}
	}
	.back:hover{
		transform: scale(1.1);
	}
	
</style>