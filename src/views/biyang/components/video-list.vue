<template>

	<el-dialog :destroy-on-close='true' :close-on-click-modal='false' ref='dialog' title="查看实时监控"
		:visible="videoListVisible" :before-close="close">
		<div class='dialog-wrap flex' v-if='videoListVisible'>
			<div class="dialog-left">
				<el-menu default-active="0" background-color='#000' text-color="#ADF9F7" active-text-color='#DCE000' class="el-menu-vertical-demo" >
					<el-submenu :index="index.toString()" v-for="(item,index) in videoList" :key='index'>
						<template slot="title" >{{item.pastureName||item.pastureId}}</template>
						<el-menu-item @click="changeVideo(item1)"  :key='index1' :index="index.toString()+'-'+index1.toString()" v-for="(item1,index1) in item.pastureVideoList">{{item1.mountLocation||item1.videoId}}</el-menu-item>
					</el-submenu>
				</el-menu>

			</div>
			<div class="dialog-right f1 flex flex-column">
				<ul class="tabs flex">
					<li class="tab" @click="changeTab(9)">
						<i class="el-icon-s-grid " :class="count==9?'active':''"></i>
					</li>
					<li class="tab" @click="changeTab(4)">
						<i class="el-icon-menu" :class="count==4?'active':''"></i>
					</li>
					<li class="tab" @click="changeTab(1)">
						<i class="el-icon-full-screen" :class="count==1?'active':''"></i>
					</li>
				</ul>
				<div class="video-list f1 flex flex-wrap jc-between" v-if='!isChangeTab'>
					<!-- <div :class="activeIndex==i?'active':''"  class="video-item" :class="'video-item-'+count" v-for='i in count' @click="selectVideo(i)"></div> -->					
					
					<div    :class="['video-item',{'active':activeIndex==index},'video-item-'+count]" style="display: flex;"   v-for='(e,index) in count' >
						<div :id="'video'+index" style="width: 100%;" @click.stop="selectVideo(index)"></div>
						<div class='mask'  @click.stop="selectVideo(index)" v-if='count!=1'></div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>


</template>

<script>
	import {
		hardwareList,getPlayUrl
	} from "@/api/biyang/home";
	export default {
		props: {

			videoListVisible: {
				type: Boolean,
				default: false
			},
			width: {
				Type: String,
				default: '50%'
			}

		},
		data() {
			return {
				count:9,
				videoList:[],
				activeIndex:null,
				player:null,
				isChangeTab:false
			};
		},
		watch: {
			videoListVisible(e) {
				if (e) {
					this.getHardwareList()
					this.activeIndex=null
				}
			}
		},
		created() {

		},
		methods: {
			changeVideo(e){
				if(!this.activeIndex&&this.activeIndex!=0){
					return
				}
				console.log(e)
				getPlayUrl({
					deviceCode: e.deviceCode,
					channelCode: e.channelCode,
					ezvizId: e.ezvizId
				}).then(res=>{
					if(res.code==200){
						this.$nextTick(function() {
							let w=document.getElementById('video'+this.activeIndex).offsetWidth
							let h=document.getElementById('video'+this.activeIndex).clientHeight 
						    this.player = new EZUIKit.EZUIKitPlayer({
						    	id: 'video'+this.activeIndex,
						    	url: res.result.url,
						    	accessToken: res.result.accessToken,
						    	template: "pcLive",
								width: w,
								height: h-2,
								showPTZControl: this.count==1?'flex':'none'
						    	// width:'100%',
						    	// height:'100%'
						    })
						
							
							// decoder.Theme.Ptz.show()
						});
						
						

					}
					console.log(res)
				})
			},
			selectVideo(i){
				console.log(i)
				this.activeIndex=i
			},
			changeTab(val){
				this.isChangeTab=true
				this.player&&this.player.stop();
				console.log(val)
				this.count=val
				this.activeIndex=null
				setTimeout(()=>{
					this.isChangeTab=false
					this.player=null
				},300)
				
			},
			close() {
				this.player&&this.player.stop();
				this.$parent.videoListVisible = false
				this.activeIndex=null

			},
			getHardwareList() {
				hardwareList({"countyId":'367795244651909126'}).then(res => {
					console.log(res)
					if(res.code==200){
						this.videoList=res.result
						console.log(this.videoList)
					}
				})
			},
			
		},
	};
</script>

<style lang="scss" scoped>
	::v-deep .el-dialog {
		background: url('~@/assets/biyang/video_bg.png');
		background-size: 100% 100%;
		box-shadow: 0px 0px 0.15rem 0px #00E0DB inset;
		
		width: 20.13rem;
		height: 12.13rem;
		display: flex;
		flex-direction: column;

	}

	::v-deep .el-dialog__header {
		background: #00E0DB;
		height: 0.48rem;
		padding: 0 0.25rem;
		position: relative;
	}

	::v-deep .el-dialog__title {
		color: #000;
		font-size: 0.23rem;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		line-height: 0.48rem;
	}

	::v-deep .el-dialog__headerbtn {
		top: 50%;
		transform: translateY(-50%);

	}

	::v-deep .el-dialog__close {
		color: #000 !important;
	}

	::v-deep .el-dialog__body {
		padding: 0;
		flex: 1;
		height: 11.65rem;
	}
	// ::v-deep .el-menu{
	// 	    background-color: rgba(255,255,255,0);
	// 		border: none;
	// }
	
	::v-deep .el-submenu__title{
		background-color: rgba(255,255,255,0) !important;
		background: rgba(255,255,255,0);
		color:#00E0DB !important;
	}
	::v-deep .el-menu{
		background-color: rgba(255,255,255,0) !important;
		background: rgba(255,255,255,0);
		border: none;
	}
	::v-deep  .el-menu-item{
		width:100%;
		min-width:auto;
		
	}
	::v-deep .el-menu-item{
		background: rgba(255,255,255,0.05);
		background-color: rgba(255,255,255,0.05) !important;

	}
	.dialog-wrap {
		// padding: 0.25rem;
		padding-top: 0;
		height: 100%;
		width: 100%;

		.dialog-left {
			width: 2.5rem;
			border-right: 0.01rem solid rgba(0,224,219,0.15);
			padding: 0.01rem 0.08rem;
			overflow-y: scroll;
		}

		.dialog-right {

			.tabs {
				height: 0.86rem;
				justify-content: flex-end;
				padding-right: 0.36rem;
				.tab {
					cursor: pointer;
					margin-left: 0.3rem;
					line-height: 0.86rem;
					color: #fff;
					font-size: 0.38rem;
				}

				.active {
					color: #00E0DB;
				}
			}

			.video-list {

				padding: 0 0.36rem;
				.video-item {
					background: #3c3c3c;
					position: relative;
					// border:1px solid rgba(0,224,219,1);
					margin-bottom: 0.36rem;
					.mask{
						width: 100%;
						height:100%;
						position: absolute;
						top: 0;
						left:0;
						background: rgba(0,0,0,0);
					}
				}
				.active{
					border:0.01rem solid rgba(0,224,219,1);
				}
				.video-item-9 {
					width: 5.38rem;
					height: 3.25rem;
					
				}
				.video-item-4 {
					width: 8.25rem;
					height: 5.06rem;
					
				}
				.video-item-1 {
					width: 16.9rem;
					height: 10.51rem;
					
				}
			}
		}



	}
	
</style>
<style>
	/* #video0,#video1,#video2,#video3,#video4,#video5,#video6,#video7,#video8{
		width:100% !important;
		height: 100% !important;
	}
	#video4{
		width:100% !important;
		height: 100% !important;
	} */
</style>