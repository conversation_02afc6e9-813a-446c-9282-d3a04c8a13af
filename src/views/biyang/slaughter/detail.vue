<template>
	<div class="wrap">
		
		
		<div class="card-list flex jc-between">
			<div class="card-info">
				<back></back>
				<div class='card-base'>
					<h2>{{companyName}}</h2>
					<div class="card-base-info flex">
						<img class="profile" :src="userInfo.avatar||profileImg " alt="">
						<ul class="flex flex-column jc-around">
							<li>企业联系人：{{userInfo.nickName||userInfo.userName||userInfo.corprateName}}</li>
							<li>联系电话：{{userInfo.phonenumber}}</li>
							<li>详细地址：{{userInfo.enterpriseModel.detailAddress}}</li>
						</ul>
					</div>
				</div>
				<div class="card-cate flex-column flex">
					<card-head titleIcon='title221'>
						<div class="dorp">
							<el-dropdown trigger="click"  @command="handleCommand">
								<span class="el-dropdown-link">
									{{activeName}}<i class="el-icon-caret-bottom el-icon--right"></i>
								</span>
								<el-dropdown-menu slot="dropdown">
									<el-dropdown-item :command="{type:7,name:'近7日'}">近7日</el-dropdown-item>
									<el-dropdown-item :command="{type:30,name:'近30日'}">近30日</el-dropdown-item>
									<el-dropdown-item :command="{type:3,name:'近3月'}">近3月</el-dropdown-item>
								</el-dropdown-menu>
							</el-dropdown>
						</div>
						
					</card-head>
					<div class='f1 flex'>
						<div class="legends flex  flex-wrap f1 jc-between">
							<div class="legend flex jc-between" v-for="(item,index) in ringData">
								<i class='round' :style="{background:colors[index]}"></i>
								<span class="label">{{item.name}}</span>
								<span class="pct">{{item.pct}}</span>
								<span class="value">{{item.value}}只</span>
							</div>
						</div>
						
						<div class="ringChart f1" >
							<dv-active-ring-chart :config="configRingChart" style="width:100%;height:100%" />
						</div>
					</div>
				</div>
			</div>
			<div class="card flex-column flex" style="margin-top: 0.36rem;">
				<card-head titleIcon='title222'>
					<div class="dorp">
						<el-select v-model="year" :clearable='false' placeholder="请选择年份" @change="getTrendOfButcher">
							<el-option v-for="(item,index) in years" :key="item" :label="item+'年'" :value="item">
							</el-option>
						</el-select>
					</div>
				</card-head>
				<div class="total">
					<ul class="total-list flex jc-start">
						<li style="margin:0;margin-top: 0.2rem;">
							<span>累计屠宰收购牛只(头)：</span>
							<span class='count theme'>{{totalButcherNum}} </span>
						</li>
						<li style="margin:0;margin-top: 0.2rem;">
							<span>累计结算金额：</span>
							<span class='count theme'>{{totalFinalAmount}} </span>
						</li>
					</ul>

				</div>
				<div id="lineBarChart" class='f1'></div>
			</div>
			<div class="card flex-column flex">
				<card-head titleIcon='title223'>
					
				</card-head>
				<div class="total">
					<ul class="total-list flex jc-start">
						<li style="margin:0;margin-top: 0.2rem;">
							<span>今日送宰养殖户(家)：</span>
							<span class='count theme'>{{todaySupplierNum}} </span>
						</li>
						<li style="margin:0;margin-top: 0.2rem;">
							<span>今日入场牛只(头)：</span>
							<span class='count theme'>{{todayButcherNum}} </span>
						</li>
						<li style="margin:0;margin-top: 0.2rem;">
							<span>今日结算金额：</span>
							<span class='count theme'>{{todayFinalAmount}} </span>
						</li>
					</ul>
				
				</div>
				
				<div  class='f1' style="overflow: hidden;margin-top: 0.25rem;">
					<dv-scroll-board  :config="config" @click='goDetail'/>
				</div>
			</div>

			<div class="card flex-column flex">
				<card-head titleIcon='title224'>
					
				</card-head>

				<div  class='f1' style="overflow: hidden;margin-top: 0.3rem;">
					<dv-scroll-board  :config="config1"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import back from '../components/back.vue'
	import {
		materialsPercent,
		trendOfButcher,
		inFreezeTop10,
		butcherDynamic,
		queryEnterpriseAdmin
	} from "@/api/biyang/slaughter";
	import profileIcon from'@/assets/images/profile.jpg'
	import { startEndDate } from "@/utils/mixin/startEndDate.js";
	export default {
		mixins:[startEndDate],
		components: {
			back,
			cardHead
		},
		data() {
			return {
				profileImg:profileIcon,
				userInfo:{
					enterpriseModel:''
				},
				activeName:'近7日',
				date: '',
				form: {
					name: ''
				},
				tableLoading: false,
				config: {},

				tableLoading1: false,
				config1: {},
				ringData:[],
				configRingChart:{},
				years: [],
				year: '',
				todaySupplierNum:'0',//今日送宰养殖户
				todayButcherNum:'0',//今日入场牛只
				todayFinalAmount:'0',//今日结算金额
				totalButcherNum:0,//累计屠宰收购牛只
				totalFinalAmount:0,//累计结算金额
				
			};
		},

		created() {
			this.companyName=this.$route.query.companyName
			this.createYears()
		},
		mounted() {

			
			
			// this.initRingChart({
			//   dom: 'ringChart',
			//   color: this.colors,
			//   data: this.ringData
			// })
			this.$nextTick(()=> {
			    this.getEnterpriseAdmin()
			    this.getTrendOfButcher()
			    this.getButcherDynamic()
			    this.getInFreezeTop10()
			    this.handleCommand({name:'近7日',type:7})
			});
			
		},
		methods: {
			handleCommand(data){
				this.activeName=data.name
				this.getStartData(data.type)
				materialsPercent({"startTime":this.startData,"endTime": this.endDate,"tenantId":this.$route.query.id}).then(res=>{
					console.log(res)
					if(res.code==200){
						this.ringData=[]
						res.result.forEach(item=>{
							this.ringData.push({
								value:item.materialsNum,
								name:item.materialsName,
								pct:item.materialsPercent
							})
							
						})
						

						this.configRingChart={
							data:this.ringData,
							showOriginValue: true,
							radius: '70%',
							activeRadius: '65%',
							digitalFlopStyle: {
							    fontSize: '13'
							  }
						}
					}
					
				})
			},
			getInFreezeTop10(){
				inFreezeTop10({"tenantId":this.$route.query.id}).then(res=>{
					if(res.code==200){
						let list=[]
						res.result.forEach((item,index)=>{
							list.push([index+1,item.productName,item.productCode,item.inventoryNum,item.inventoryWeight+' kg'])
						})
						this.config1 = {
							data: list,
							header: ['排名', '产品名称', '产品编号', '数量','重量'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum:8
						}
					}
				})
			},
			getButcherDynamic(){
				butcherDynamic({"tenantId":this.$route.query.id}).then(res=>{
					if(res.code==200){
						let list = []
						res.result.settlementList.forEach(item=>{
							list.push([item.checkInTime,item.supplierName,item.finalButcherNum,item.finalAmount])
						})
						
						console.log(list)
						this.todaySupplierNum=res.result.supplierNum
						this.todayButcherNum=res.result.butcherNum
						this.todayFinalAmount=res.result.finalAmount
						this.config = {
							data: list,
							header: ['入场时间', '养殖户名称', '入场数量', '结算金额', ],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum:7
						}
						
					}
				})
			},
			createYears() {
				const currentYear = new Date().getFullYear();
				// 定义起始年份和结束年份
				const startYear = 2020;
				const endYear = currentYear;
				this.year = endYear
				// 存储生成的年份
				this.years = [];
			
				// 生成年份
				for (let year = startYear; year <= endYear; year++) {
					this.years.unshift(year);
				}
			},
			// 收购数量与结算金额统计
			getTrendOfButcher(){
				let startTime = this.year + '-01-01'
				let endTime = this.year + '-12-31'
				trendOfButcher({
					"tenantId":this.$route.query.id,
					"startTime": startTime,
					"endTime": endTime,
				}).then(res=>{
					if(res.code==200){
						let xAxis=[]
						let data=[]
						let data1=[]
						this.totalButcherNum=res.result.butcherNum
						this.totalFinalAmount=res.result.finalAmount
						res.result.trendList.forEach(item=>{
							xAxis.push(item.xaxis)
							data.push(item.yaxisLeft)
							data1.push(item.yaxisRight)
						})
						this.initLineBarChart({
								dom: 'lineBarChart',
								xAxis: xAxis,
								titles: ['交易金额', '屠宰量', ],
								units: ['元', '头'],
								data: data,
								data1:data1
							}
						
						)
					}
				})
			},

			getEnterpriseAdmin(){
				queryEnterpriseAdmin({tenantId:this.$route.query.id}).then(res=>{
					if(res.code==200){
						this.userInfo=res.result
					}
				})
			},
			initRingChart(data) {
			  this.chart = this.$echarts.init(document.getElementById(data.dom))
			  this.chart.setOption({
			    
			    grid: {
			      left: '10%',
			      right: '10%',
			    },
			    color: data.color, // 饼图各块颜色
			    toolbox: {
			      show: false,
			    },
			    series: [
			      {
			        type: 'pie',
			        radius: [40, 60],
			        center: ['50%', '50%'],
			        roseType: data.roseType,
			        itemStyle: {
			          borderRadius: 0,
			        },
			        emphasis: {
			          label: {
			            show: true,
			            formatter: data.roseType ? '{a|{c}}\n{b|{b}} ' : '{b|{b}}\n{a|{d}%} ',
			            rich: {
			              a: {
			                fontSize: 20,
			                lineHeight: 36,
			                color: "#fff",
			                fontWeight: 'bold'
			              },
			              b: {
			                fontSize: 12,
			                lineHeight: 24,
			                color: "#fff"
			              }
			            }
			          }
			        },
			        label: {
			          show: false,
			          position: "center",
			          color: "rgba(13, 17, 29,1)",
			          fontSize: 14,
			          formatter: '{a|{b}}\n{b|{c}} ',
			          rich: {
			            a: {
			              fontSize: 20,
			              lineHeight: 36,
			              color: "#fff"
			            },
			            b: {
			              fontSize: 16,
			              lineHeight: 24,
			              color: "#fff"
			            }
			          }
			        },
			        labelLine: {
			          show: false
			        },
			        data: data.data,
			      }
			    ],
			  })
			},
			
			goDetail(e){
				console.log(e)
				this.$router.push({
				    path:'/slaughter/detail',
					query:{
						id:11
					}
				})
			},
			activeTable(e) {
				console.log(e)
			},
			
			// 活畜收购交易统计
			initLineBarChart(data) {
				// var a=document.ge
				console.log(data);
				var myChart = this.$echarts.init(document.getElementById(data.dom));
				var option = {
					legend: {
						// top: '0%',
						// right: '15%',
						align: 'right',
						right: '15%',
						top: '50px',
						// top: 'start',
						// bottom: '60%',
						// show: true,
						itemWidth: 12,
						itemHeight: 12,
						data: [

							{
								name: data.titles[1],
								textStyle: {
									color: '#0EDAE2'
								},
								// bottom: 10
							},
							{
								name: data.titles[0],
								textStyle: {
									color: ' #DCE000'
								},
							},
						]
					},
					grid: {
						left: '3%',
						right: '3%',
						bottom: '0%',
						top: '83px',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						boundaryGap: true,
						show: true,
						axisLine: {
							lineStyle: {
								color: 'rgba(0,254,255,0.1)', //x轴线条颜色宽度
								width: 1
							}
						},
						axisLabel: {
							color: '#00E0DB'
						}, //x轴刻度文字颜色
						scale: false,
						axisTick: {
							show: false
						},
						data: data.xAxis

					},
					yAxis: [{
							type: 'value',
							// show: true,
							axisLabel: {
								// showMaxLabel: false,
								color: '#00E0DB '
							},
							nameTextStyle: {
								color: '#00E0DB'
							},
							name: data.titles[0] + '：' + data.units[0],
							axisLine: {
								show: true,

								symbol: ['none', 'arrow'], //加箭头处
								symbolOffset: [0, 0],
								lineStyle: {
									color: '#0B2D2C',

									shadowColor: '#0B2D2C',

								}
							},

							splitNumber: 4,
							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0.1)'
								},


							},
							offset: 0,
							axisTick: {
								show: false
							},
						},
						{
							type: 'value',
							axisLabel: {
								// showMaxLabel: false
							},
							show: true,
							name: data.titles[1] + '：' + data.units[1],
							axisLine: {
								lineStyle: {
									color: '#00E0DB'
								}
							},

							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0)'
								}
							},
							offset: 10,
							// splitNumber: 4,
							axisTick: {
								show: false
							},
						}
					],
					series: [{
							name: data.titles[0],
							data: data.data1,
							type: 'line',
							symbol: 'circle',
							symbolSize: 5,
							yAxisIndex: 1,
							itemStyle: {
								color: '#DCE000'
							}
						},
						{
							name: data.titles[1],
							data: data.data,
							type: 'bar',
							symbol: 'circle',
							barWidth: 10,
							itemStyle: {
								opacity: 0.8,
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
										offset: 1,
										color: '#0BE5E1',
									},
									{
										offset: 0,
										color: '#06FDF5',
									},
								]),
							},
							backgroundStyle: {
								color: 'rgba(44, 118, 254, 0.2)'
							},

							// tooltip: {
							//     trigger: 'item'
							// },
						}
					],
					tooltip: { //鼠标悬浮提示数据
						trigger: 'axis',
						axisPointer: {
							type: 'line'
						},
						formatter: function(params) {
							// console.log(params, 2)
							// return `<p>
							//           <span style="color: #FFF; font-size: 14px; font-weight: normal;">监管笔数${params.name
							//     }： ${params.data.account || 0} </span>

							//       </p>`;
							return `
			                <p style="font-size: 14px; font-weight: normal;">${params[0].axisValue
			                    }</p><br/>
			                <p>
			                  <span style="font-size: 14px; font-weight: normal;">屠宰量(只)： ${params[0].value || 0} </span>
			                </p><br/> 
			                 <p>
			                  <span style="font-size: 14px; font-weight: normal;">交易金额(元)： ${params[1].value || 0} </span>
			                 </p>`;
							// return params.name
						},
						// backgroundColor: 'rgba(11, 15, 56, 1)',
						// borderColor: 'rgba(18, 36, 132, 1)',
						// borderWidth: 2,
						// textStyle: {
						//     color: '#fff',
						// },
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#fff',
						},
					}
				};

				myChart.setOption(option)
				setTimeout(() => {
					myChart.resize()
				}, 100)
				window.addEventListener('resize', () => {
					setTimeout(() => {
						myChart.resize()
					}, 100)

				})
			},
		},
	};
</script>
<style lang="scss" scoped>
	.wrap {
		background: url(~@/assets/biyang/bg1.png);
		padding: 0.25rem;
		padding-top: 1.01rem;
		background-size: 100% 100%;
		
		.card-list {
			flex-wrap: wrap;

			.card {
				padding: 0.25rem;
				margin-top: 0.25rem;
				width: 11.63rem;
				height: 5.81rem;
				background: rgba(0, 0, 0, 0.26);
				border-radius: 0rem 0rem 0rem 0rem;
				opacity: 1;

				.dorp {
					.total-font {
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #D8DFFE;
					}

					.count {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #FCD827;
					}
				}

				.total-list {


					font-size: 0.18rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 0.18rem;

					li {
						margin-bottom: 0.39rem;
						display: flex;
						align-items: center;
						width: 33%;
						.count {
							font-size: 0.23rem;
							font-family: Source Han Sans CN-Medium;
							font-weight: 700;
							color: #FFFFFF;
							margin: 0 0.13rem;
						}

						.unit {
							font-size: 0.2rem;
							color: rgba(250, 250, 250, 0.60);
						}
					}

					.circle::before {
						content: '';
						display: flex;
						width: 0.13rem;
						height: 0.13rem;
						background: #00E0DB;
						border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
						margin-right: 0.13rem;
					}

				}
			}
			.card-info{
				.card-base{
					width: 11.63rem;
					height: 2.5rem;
					background: rgba(0,0,0,0.26);	
					margin-top: 0.18rem;
					padding: 0.25rem;
					h2{
						font-size: 0.33rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #00E0DB;
						height: 0.23rem;
						line-height: 0.23rem;
					}
					.card-base-info{
						margin-top: 0.48rem;
						.profile{
							width: 1.25rem;
							height: 1.25rem;
							margin-right: 0.25rem;
							border-radius: 50%;
							border: 0.05rem solid rgb(166,192,212)
						}
						ul li{
							height: 0.18rem;
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Normal, Source Han Sans CN;
							font-weight: 350;
							color: #FFFFFF;
							line-height: 0.18rem;
						}
					}
				}
				.card-cate{
					margin-top: 0.25rem;
					width: 11.63rem;
					height: 2.81rem;
					background: rgba(0,0,0,0.26);
					padding: 0.25rem;
					.legends {
						
						padding:0 0.4rem;
						.legend {
							align-items: center;
							box-sizing: content-box;
							padding: 0.1rem 0;
							width: 48%;
							// height: 0.26rem;
							font-size: 0.13rem;
							font-family: Source Han Sans CN-Normal, Source Han Sans CN;
							font-weight: 350;
							color: #FFFFFF;
							// line-height: 0.26rem;
							position: relative;
							padding-left: 0.7rem;
							box-sizing: border-box;
							.value{
								color:#FFF200;
							}
							.round {
								
								width: 0.08rem;
								height: 0.08rem;
								background: #0EDAE2;
								border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
								position: absolute;
								left: 0.5rem;
								top: 50%;
								transform: translateY(-50%);
							}
						}
					}
					
				}
			}
		}
	}
</style>