<template>
	<div class="wrap">
		<div class='panels jc-between flex'>
			<div class="panel flex jc-between  flex-column">
				<div>
					<span class='count'>{{baseInfo.butcherNumMonth}}</span>
					<span class="unit">头</span>
				</div>
				<div class="flex align-items-center" style="height: 0.26rem;" :class="filterStatus(baseInfo.butcherNumLastYear)">
					<span class="label">本月累计收购只数</span>
					<span class="value">同比去年 {{baseInfo.butcherNumLastYear}}%</span>
					
					<i class="arrow el-icon-minus"  v-if="filterStatus(baseInfo.butcherNumLastYear)=='flat'"></i>
					<i  class="arrow" v-else >↑</i>
				</div>
			</div>
			<div class="panel flex jc-between  flex-column">
				<div>
					<span class='count'>{{baseInfo.finalAmountMonth}}</span>
					<span class="unit">元</span>
				</div>
				<div class="flex align-items-center" style="height: 0.26rem;" :class="filterStatus(baseInfo.finalAmountYearPercent)">
					<span class="label">本月累计收购金额</span>
					<span class="value">同比去年 {{baseInfo.finalAmountYearPercent}}%</span>
					
					<i class="arrow el-icon-minus"  v-if="filterStatus(baseInfo.finalAmountYearPercent)=='flat'"></i>
					<i  class="arrow" v-else >↑</i>
				</div>
			</div>
			<div class="panel flex jc-between  flex-column">
				<div>
					<span class='count'>{{baseInfo.butcherNumToday}}</span>
					<span class="unit">头</span>
				</div>
				<div class="flex align-items-center" style="height: 0.26rem;" :class="filterStatus(baseInfo.butcherNumYesterdayPercent)">
					<span class="label">今日收购只数</span>
					<span class="value">同比昨日 {{baseInfo.butcherNumYesterdayPercent}}%</span>
					
					<i class="arrow el-icon-minus"  v-if="filterStatus(baseInfo.butcherNumYesterdayPercent)=='flat'"></i>
					<i  class="arrow" v-else >↑</i>
				</div>
			</div>
			<div class="panel flex jc-between  flex-column">
				<div>
					<span class='count'>{{baseInfo.butcherWeightToday}}</span>
					<span class="unit">kg</span>
				</div>
				<div class="flex align-items-center" style="height: 0.26rem;" :class="filterStatus(baseInfo.butcherWeightYesterdayPercent)">
					<span class="label">今日收购重量</span>
					<span class="value">同比昨日 {{baseInfo.butcherWeightYesterdayPercent}}%</span>
					
					<i class="arrow el-icon-minus"  v-if="filterStatus(baseInfo.butcherWeightYesterdayPercent)=='flat'"></i>
					<i  class="arrow" v-else >↑</i>
				</div>
			</div>
			<div class="panel flex jc-between  flex-column">
				<div>
					<span class='count'>{{baseInfo.averageAmountToday}}</span>
					<span class="unit">元</span>
				</div>
				<div class="flex align-items-center"  style="height: 0.26rem;" :class="filterStatus(baseInfo.averageAmountYesterdayPercent)">
					<span class="label">今日收购均价</span>
					<span class="value">同比昨日 {{baseInfo.averageAmountYesterdayPercent}}%</span>
					<i class="arrow el-icon-minus"  v-if="filterStatus(baseInfo.averageAmountYesterdayPercent)=='flat'"></i>
					<i  class="arrow" v-else >↑</i>
				</div>
			</div>
		</div>
		<div class="card-list flex jc-between">
			<div class="card flex-column flex">
				<card-head titleIcon='title21'>
					<div class="dorp">
						<el-select v-model="year" :clearable='false' placeholder="请选择年份" @change="getTrendOfButcher">
							<el-option v-for="(item,index) in years" :key="item" :label="item+'年'" :value="item">
							</el-option>
						</el-select>

					</div>
				</card-head>
				<div class="total">
					<ul class="total-list flex jc-start">
						<li style="margin:0;margin-top: 0.2rem;margin-right: 2.08rem;">
							<span>累计入场牛只数量(头)：</span>
							<span class='count theme'>{{butcherNum}} </span>
						</li>
						<li style="margin:0;margin-top: 0.2rem;">
							<span>牛只累计收购金额：</span>
							<span class='count theme'>{{finalAmount}} </span>
						</li>
					</ul>

				</div>
				<div id="lineBarChart" class='f1'></div>
			</div>
			<div class="card flex-column flex">
				<card-head titleIcon='title22'>
					<div class="dorp">
						<span class="total-font">共计<span class="count">{{config.data.length}}</span> 家</span>
					</div>
				</card-head>
				<div class="search">
					<el-form :inline="true" :model="form" ref="form" class="form-inline">
						<el-form-item prop="companyName">
							<el-input v-model="form.companyName" placeholder="输入屠宰加工企业名称检索"></el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="custom" @click='getCompanyList'>搜索</el-button>
							<el-button type="custom" @click="handlRest('form')">重置</el-button>
						</el-form-item>

					</el-form>
				</div>
				<div class='f1' style="overflow: hidden;">
					<dv-scroll-board v-if='tableLoading' :config="config" />
				</div>
			</div>
			<div class="card flex-column flex">
				<card-head titleIcon='title23'>
				</card-head>
				<div class="total">
					<ul class="total-list flex jc-start">
						<li style="margin:0;margin-top: 0.2rem;margin-right: 2.08rem;">
							<span>库存产品总重(kg)：</span>
							<span class='count theme'>{{inventoryWeight}} </span>
						</li>
					</ul>

				</div>
				<div id="barChart" class='f1'></div>
			</div>
			<div class="card flex-column flex">
				<card-head titleIcon='title24'>
					<div class="dorp">
						<el-date-picker :clearable='false' v-model="date" @change="getCompanyRankInFreeze" value-format="yyyy-MM-dd"
							type="date" placeholder="选择日期">
						</el-date-picker>
					</div>
				</card-head>

				<div class='f1' style="overflow: hidden;margin-top: 0.3rem;">
					<dv-scroll-board v-show='tableLoading1' :config="config1" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import {
		singleCount,
		trendOfButcher,
		companyList,
		trendOfInventory,
		companyRankInFreeze

	} from "@/api/biyang/slaughter";

	export default {
		components: {

			cardHead
		},
		data() {
			return {
				year: '',
				date: '',
				baseInfo: {},
				form: {
					name: ''
				},
				tableLoading: false,
				config: {
					data: [],
					header: ['排行', '企业名称', '收购牛只', '收购重量', '累计交易金额'],
					headerBGC: '#202020',
					oddRowBGC: '#131313',
					evenRowBGC: '#131313'
				},

				tableLoading1: false,
				config1: {
					data: [],
					header: ['生产日期', '企业名称', '生产产品总数', '生产产品总重'],
					headerBGC: '#202020',
					oddRowBGC: '#131313',
					evenRowBGC: '#131313'
				},
				years: [],
				butcherNum:0,//累计入场牛只数量
				finalAmount:0,//牛只累计收购金额(元)
				inventoryWeight:0,//库存产品总重
			};
		},

		created() {
			window.goDetail=this.goDetail
			
			this.createYears()
			this.getNowDate()
		},
		mounted() {

			setTimeout(() => {

				
				this.tableLoading = true
				this.tableLoading1 = true
			}, 20)



			this.getSingleCount()
			this.getTrendOfButcher()
			this.getCompanyList()
			this.getTrendOfInventory()
			this.getCompanyRankInFreeze()
		},
		methods: {
			filterStatus(val){
				if(Number(val)==0){
					return 'flat'
				}
				if(Number(val)>0){
					return 'up'
				}else{
					return 'down'
				}
				
			},
			getCompanyRankInFreeze() {
				console.log(this.date)
				let date = this.date
				companyRankInFreeze({
					"materialsType": 2,
					"startTime": date,
					"endTime": date
				}).then(res => {
					if (res.code == 200) {
						let list = []
						res.result.forEach((item, index) => {
							list.push([item.productDate, item.companyName, item.productNum, item
								.productWeight + ' kg'
							])
						})
						this.config1 = {
							data: list,
							header: ['生产日期', '企业名称', '生产产品总数', '生产产品总重'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum: 6
						}
					}
				})
			},
			getTrendOfInventory() {
				trendOfInventory({}).then(res => {
					if (res.code == 200) {
						this.inventoryWeight=res.result.inventoryWeight
						let xAxis = []
						let data = []
						res.result.trendList.forEach((item, index) => {
							xAxis.push(item.xaxis)
							data.push(item.yaxisLeft)
						})
						this.initBarChart({

							dom: 'barChart',
							xAxis: xAxis,
							titles: [''],
							units: ['重量：kg'],
							data: data,

						})
					}
				})
			},
			handlRest(formName) {
				this.$refs[formName].resetFields();
				this.getCompanyList()
			},
			getCompanyList() {
				companyList({
					"materialsType": 2,
					"companyName": this.form.companyName
				}).then(res => {
					if (res.code == 200) {
						console.log(res)
						let list = []
						res.result.forEach((item, index) => {
							list.push([index + 1, item.companyName, item.butcherNum + ' 头', item
								.butcherWeight + ' kg', item.finalAmount,
								`<span style="padding:.05rem .1rem;color:#fff;cursor:pointer;background: rgba(255, 255, 255, 0.1);" onClick="goDetail('${item.tenantId}','${item.companyName}')">查看</span>`
							])

						})
						this.config = {
							data: list,
							header: ['排行', '企业名称', '收购牛只', '收购重量', '累计交易金额'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							// columnWidth: [100]
						}
					}
				})
			},
			createYears() {
				const currentYear = new Date().getFullYear();
				// 定义起始年份和结束年份
				const startYear = 2020;
				const endYear = currentYear;
				this.year = endYear
				// 存储生成的年份
				this.years = [];

				// 生成年份
				for (let year = startYear; year <= endYear; year++) {
					this.years.unshift(year);
				}
			},
			getNowDate() {
				const today = new Date();

				// 获取当前日期的年、月、日
				const year = today.getFullYear();
				const month = (today.getMonth() + 1).toString().padStart(2, '0');
				const day = today.getDate().toString().padStart(2, '0');

				// 格式化为 yyyy-MM-dd 格式
				const formattedDate = `${year}-${month}-${day}`;
				this.date = formattedDate
			},
			getTrendOfButcher() {
				console.log(this.year)
				let startTime = this.year + '-01-01'
				let endTime = this.year + '-12-31'
				trendOfButcher({
					"materialsType": 2,
					"startTime": startTime,
					"endTime": endTime
				}).then(res => {
					if (res.code == 200) {
						console.log(res)
						let xAxis = []
						let data = []
						let data1 = []
						this.butcherNum=res.result.butcherNum
						this.finalAmount=res.result.finalAmount
						
						res.result.trendList.forEach(item => {
							xAxis.push(item.xaxis)
							data.push(item.yaxisLeft)
							data1.push(item.yaxisRight)
						})
						this.initLineBarChart({
								dom: 'lineBarChart',
								xAxis: xAxis,
								titles: ['交易金额', '屠宰量', ],
								units: ['元', '头'],
								data: data,
								data1: data1
							}

						)

					}
				})
			},
			getSingleCount() {
				singleCount({
					"materialsType": 2
				}).then(res => {
					if (res.code == 200) {
						console.log(res)
						res.result.averageAmountYesterdayPercent='-1'
						this.baseInfo = res.result
					}
				})
			},
			goDetail(id,companyName) {
				console.log(id)
				this.$router.push({
					path: '/slaughter/detail',
					query: {
						id: id,
						companyName:companyName
					}
				})
			},
			activeTable(e) {
				console.log(e)
			},
			// 加工企业当前库存统计
			initBarChart(data) {
				console.log(data);
				var myChart = this.$echarts.init(document.getElementById(data.dom));
				var option = {
					legend: {
						align: 'right',
						// right: '15%',
						left: 'center',
						top: '150px',
						itemWidth: 12,
						itemHeight: 12,
						data: [{
							name: data.titles[0],
							textStyle: {
								color: '#0EDAE2'
							},
							// bottom: 10
						}, ]
					},
					grid: {
						left: '3%',
						right: '3%',
						bottom: '0%',
						top: '50px',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						boundaryGap: true,
						show: true,
						axisLine: {
							lineStyle: {
								color: 'rgba(0,254,255,0.1)', //x轴线条颜色宽度
								width: 1,
								
							}
						},
						axisLabel: {
							color: '#00E0DB',
							// rotate: 15  
						}, //x轴刻度文字颜色
						scale: false,
						axisTick: {
							show: false
						},
						data: data.xAxis

					},
					yAxis: [{
							type: 'value',
							// show: true,
							axisLabel: {
								// showMaxLabel: false,
								color: '#00E0DB '
							},
							nameTextStyle: {
								color: '#00E0DB',
								padding: [0, 0, 0, 0]
							},
							name:  data.units[0],
							axisLine: {
								show: true,

								symbol: ['none', 'arrow'], //加箭头处
								symbolOffset: [0, 0],
								lineStyle: {
									color: '#0B2D2C',

									shadowColor: '#0B2D2C',

								}
							},

							splitNumber: 4,
							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0.1)'
								},


							},
							offset: 0,
							axisTick: {
								show: false
							},
						},

					],
					series: [{
						name: '',
						type: 'bar',
						barWidth: 10,
						barGap: 0,
						data: data.data,
						emphasis: {
							focus: 'series',
						},
						label: {
							normal: {
								show: false,
								position: 'top',
								fontSize: 12,
								color: '#FFFFFF',
								offset: [0, 0],
								formatter: '\n{c}kg'
							},
						},
						itemStyle: {
							//   barBorderRadius: [20, 20, 0, 0],
							color: '#00FEFF',
						},
					}, ],
					tooltip: { //鼠标悬浮提示数据
						trigger: 'axis',
						axisPointer: {
							type: 'line'
						},
						// formatter: function(params) {
						// 	return `
						//           <p style="font-size: 14px; font-weight: normal;">${params[0].axisValue
						//               }</p><br/>
						//           <p>
						//             <span style="font-size: 14px; font-weight: normal;">监管笔数(笔)： ${params[0].value || 0} </span>
						//           </p><br/> 
						//            <p>
						//             <span style="font-size: 14px; font-weight: normal;">监管金额(万)： ${params[1].value || 0} </span>
						//            </p>`;
						// 	// return params.name
						// },
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#fff',
						},
					}
				};

				myChart.setOption(option)
				setTimeout(() => {
					myChart.resize()
				}, 100)
				window.addEventListener('resize', () => {
					setTimeout(() => {
						myChart.resize()
					}, 100)

				})
			},
			// 活畜收购交易统计
			initLineBarChart(data) {
				// var a=document.ge
				console.log(data);
				var myChart = this.$echarts.init(document.getElementById(data.dom));
				var option = {
					legend: {
						// top: '0%',
						// right: '15%',
						align: 'right',
						right: '15%',
						top: '50px',
						itemWidth: 12,
						itemHeight: 12,
						data: [

							{
								name: data.titles[1],
								textStyle: {
									color: '#0EDAE2'
								},
								// bottom: 10
							},
							{
								name: data.titles[0],
								textStyle: {
									color: ' #DCE000'
								},
							},
						]
					},
					grid: {
						left: '3%',
						right: '3%',
						bottom: '0%',
						top: '83px',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						boundaryGap: true,
						show: true,
						axisLine: {
							lineStyle: {
								color: 'rgba(0,254,255,0.1)', //x轴线条颜色宽度
								width: 1
							}
						},
						axisLabel: {
							color: '#00E0DB'
						}, //x轴刻度文字颜色
						scale: false,
						axisTick: {
							show: false
						},
						data: data.xAxis

					},
					yAxis: [{
							type: 'value',
							// show: true,
							axisLabel: {
								// showMaxLabel: false,
								color: '#00E0DB '
							},
							nameTextStyle: {
								color: '#00E0DB'
							},
							name: data.titles[0] + '：' + data.units[0],
							axisLine: {
								show: true,

								symbol: ['none', 'arrow'], //加箭头处
								symbolOffset: [0, 0],
								lineStyle: {
									color: '#0B2D2C',

									shadowColor: '#0B2D2C',

								}
							},

							splitNumber: 4,
							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0.1)'
								},


							},
							offset: 0,
							axisTick: {
								show: false
							},
						},
						{
							type: 'value',
							axisLabel: {
								// showMaxLabel: false
							},
							// show: false,
							name: data.titles[1] + '：' + data.units[1],
							axisLine: {
								lineStyle: {
									color: '#00E0DB'
								}
							},

							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0)'
								}
							},
							offset: 10,
							// splitNumber: 4,
							axisTick: {
								show: false
							},
						}
					],
					series: [{
							name: data.titles[0],
							data: data.data,
							type: 'line',
							symbol: 'circle',
							symbolSize: 5,
							yAxisIndex: 0,
							itemStyle: {
								color: '#DCE000'
							}
						},
						{
							name: data.titles[1],
							data: data.data1,
							type: 'bar',
							symbol: 'circle',
							barWidth: 10,
							yAxisIndex: 1,
							itemStyle: {
								opacity: 0.8,
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
										offset: 1,
										color: '#0BE5E1',
									},
									{
										offset: 0,
										color: '#06FDF5',
									},
								]),
							},
							backgroundStyle: {
								color: 'rgba(44, 118, 254, 0.2)'
							},
						}
					],
					tooltip: { //鼠标悬浮提示数据
						trigger: 'axis',
						axisPointer: {
							type: 'line'
						},
						backgroundColor: 'rgba(0,0,0,0.8);',
						formatter: function(params) {
							return `
			                <p style="font-size: 14px; font-weight: normal;">${params[0].axisValue
			                    }</p><br/>
			                <p>
			                  <span style="font-size: 14px; font-weight: normal;">交易金额(元)： ${params[0].value || 0} </span>
			                </p><br/> 
			                 <p>
			                  <span style="font-size: 14px; font-weight: normal;">屠宰量(头)： ${params[1].value || 0} </span>
			                 </p>`;
							// return params.name
						},
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#fff',
						},
					}
				};

				myChart.setOption(option)
				setTimeout(() => {
					myChart.resize()
				}, 100)
				window.addEventListener('resize', () => {
					setTimeout(() => {
						myChart.resize()
					}, 100)

				})
			},
		},
	};
</script>
<style lang="scss" scoped>
	.wrap {
		background: url(~@/assets/biyang/bg1.png);
		padding: 0.25rem;
		padding-top: 1.25rem;
		background-size: 100% 100%;
		.panels {
			.panel {
				width: 3.75rem;
				height: 1.5rem;
				background: rgba(0, 0, 0, 0.4);
				opacity: 1;
				padding: 0.38rem 0 0.31rem 0.29rem;
				position: relative;
				// border-image-source: radial-gradient(at 55% 90%, transparent 0, cyan 100%);

				border-image-source: radial-gradient(55% 90%, transparent 0px, transparent 100%, cyan 100%);
				border-image-slice: 2;
				border-width: 0.01rem;
				border-style: solid;
				border-image-outset: 1;

				.count {
					font-size: 0.38rem;
					font-family: Impact-Regular, Impact;
					font-weight: 400;
					color: #FFF200;
					height: 0.4rem;
					line-height: 0.38rem;
					padding-right: 0.1rem;
				}

				.unit {
					font-size: 0.18rem;
					font-family: Source Han Sans CN-Normal, Source Han Sans CN;
					font-weight: 350;
					color: #FFFFFF;
				}

				.label {
					font-size: 0.18rem;
					font-family: Source Han Sans CN-Normal, Source Han Sans CN;
					font-weight: 350;
					color: #FFFFFF;
					
					padding-right: 0.28rem;
				}

				.value {
					font-size: 0.13rem;
					font-family: Source Han Sans CN-Normal, Source Han Sans CN;
					font-weight: 350;
					// color: #00FF9D;
				}
				.arrow{
					margin-left: 0.11rem;
					
					
					font-size: 0.11rem;
					
				}
				.up{
					color:#00FF9D;
					
					
				}
				.down{
					.arrow{
						transform: rotate(180deg) translateY(-0.02rem);
					}
					color:#FF5A0E;
				}
				.flat{
					color:#00E0DB;
				}
			}

		}

		.card-list {
			flex-wrap: wrap;

			.card {
				padding: 0.25rem;
				margin-top: 0.25rem;
				width: 11.63rem;
				height: 5rem;
				background: rgba(0, 0, 0, 0.26);
				border-radius: 0rem 0rem 0rem 0rem;
				opacity: 1;

				.dorp {
					.total-font {
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #D8DFFE;
					}

					.count {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #FCD827;
					}
				}

				.total-list {


					font-size: 0.18rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 0.18rem;

					li {
						margin-bottom: 0.39rem;
						display: flex;
						align-items: center;

						.count {
							font-size: 0.23rem;
							font-family: Source Han Sans CN-Medium;
							font-weight: 700;
							color: #FFFFFF;
							margin: 0 0.13rem;
						}

						.unit {
							font-size: 0.2rem;
							color: rgba(250, 250, 250, 0.60);
						}
					}

					.circle::before {
						content: '';
						display: flex;
						width: 0.13rem;
						height: 0.13rem;
						background: #00E0DB;
						border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
						margin-right: 0.13rem;
					}

				}
			}
		}
	}
	
	
	
</style>