<template>
	<div class="wrap">

		<back></back>

		<div class="card-list flex jc-between">
			<div class="card-left">
				<div class='card-info flex flex-column'>
					<card-head titleIcon='title321'>
					</card-head>
					<div class="card-base-info flex f1">
						<img class="profile" :src="profileImg" alt="">
						<ul class="flex flex-column jc-around">
							<li>企业联系人：{{userData.nickName||userData.corprateName||userData.userName}}</li>
							<li>联系电话：{{userData.phonenumber||'未知'}}</li>
							<li>详细地址：{{userData.homeAddressInfo||'未知'}}</li>
						</ul>
					</div>
				</div>
				<div class="card-watch flex-column flex">
					<card-head titleIcon='title323'>
					</card-head>
					<div class="plane flex-wrap flex">
						<div class="plane-item flex align-items-center" style="margin-bottom: 0.44rem;" >
							<img class='plane-icon' src="@/assets/icons/svg/biyang/temp.svg" alt="">
							<span class='plane-label'>气温：</span>
							<span class='plane-value'>{{weather.temperature}}℃</span>
						</div>
						<div class="plane-item flex align-items-center" style="margin-bottom: 0.44rem;" >
							<img class='plane-icon' src="@/assets/icons/svg/biyang/rh.svg" alt="">
							<span class='plane-label'>湿度：</span>
							<span class='plane-value'>{{weather.humidity}}%</span>
						</div>
						
						<div class="plane-item flex align-items-center"  >
							<img class='plane-icon' src="@/assets/icons/svg/biyang/pa.svg" alt="">
							<span class='plane-label'>天气：</span>
							<span class='plane-value'>{{weather.weather}}</span>
						</div>
						<div class="plane-item flex align-items-center"  >
							<img class='plane-icon' src="@/assets/icons/svg/biyang/height.svg" alt="">
							<span class='plane-label'>风力：</span>
							<span class='plane-value'>{{weather.windpower}}</span>
						</div>
					</div>
					<div class='f1 flex flex-column'>
						<div class="title flex jc-between align-items-center">
							<div class="tag flex">
								<i ></i>
								<span class="title-name">{{pastureName||'--'}}</span>
							</div>
							<div class="tag flex align-items-center" style="cursor: pointer;" @click="showVideo">
								<img src="@/assets/icons/svg/biyang/full.svg" alt="">
								<span class="full">全屏</span>
							</div>
							
						</div>
						<div class='decora  f1'>
							<div class="watch-border">
								<div id='video'>
									<span >当前视频已离线</span>
								</div>
							</div>
						</div>
						
					</div>
				</div>

			</div>
			<div class="card-right flex flex-column">
				<card-head titleIcon='title322'>
				</card-head>
				<div class="flex jc-between" style="margin-top: 0.44rem;height: 3.98rem;">
					<div style="width:50%;">
						<span class="title">活畜存栏量</span>
						<ul class="flex nums">
							<li v-for="(item,index) in String(ivestockInfo.num).split('')" :key='index'>{{item}}</li>
					
						</ul>
						<div>
							<ul class='tab flex'>
								<li class="flex flex-column">
									
									<span class='tab-value'>{{ivestockInfo.earTagNum}}</span>
									<span class='tab-name'>建档牛只(头)</span>
								</li>
								<li class="flex flex-column">
									<span class='tab-value'>{{ivestockInfo.femaleNum}}</span>
									<span class='tab-name'>基础母牛(头)</span>
								</li>
							</ul>
						</div>
					</div>
					<div class="flex flex-column" style="width:50%;">
						
						<div class="tag flex">
							<i></i>
							<span class="tag-name">存栏活畜品类分布</span>
						</div>
							
						<div class="flex-1 flex jc-around" >
							<div id="ringChart"></div>
							
							<ul class="legend flex flex-column">
								<li class="flex align-items-center" v-for="(item,index) in cateData" :key='index'>
									<i class="circle" :style="{background:colors[index]}"></i>
									{{item.name}} {{item.pct}}% {{item.value}}头
								</li>
							</ul>
							
						</div>
					</div>
				</div>
				<div class="search">
					<el-form :inline="true" :model="form" ref="form" class="form-inline">
						<el-form-item prop="theEarEncipher">
							<el-input v-model="form.theEarEncipher" placeholder="输入耳标编号搜索活畜"></el-input>
						</el-form-item>
						<el-form-item prop="liveVariety">
							<el-select v-model="form.liveVariety" placeholder="活畜品类">
							    <el-option
							      v-for="item in cateData"
							      :key="item.categoryId"
							      :label="item.name"
							      :value="item.name">
							    </el-option>
							  </el-select>
						</el-form-item>
						<el-form-item prop="monthAge">
							<el-select v-model="form.monthAge" placeholder="活畜月龄">
							    <el-option
							      v-for="item in monthAgeData"
							      :key="item.dictValue"
							      :label="item.dictLabel"
							      :value="item.dictLabel">
							    </el-option>
							  </el-select>
						</el-form-item>
						<el-form-item>
							<el-button type="custom" @click="getUserLivestockList()">搜索</el-button>
							<el-button type="custom" @click="handlRest('form')">重置</el-button>
						</el-form-item>
					</el-form>
					
				</div>

				<div  class='f1' style="overflow: hidden;">
					<dv-scroll-board  :config="config" />
				</div>
			</div>

		</div>
		<dialog-video :dialogVisible='dialogVisible' :videoList='videoList'></dialog-video>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import back from '../components/back.vue'
	import dialogVideo from '../components/dialog-video.vue'
	import {
		userLivestockInfo,
		userLivestockList,
		weatherInfo,
		userDeviceList,
		userInfo
	
	} from "@/api/biyang/breed";
	import {
		getDict
	} from "@/api/biyang/index";
	
	import profileIcon from'@/assets/images/profile.jpg'
	import {
		hardwareList,getPlayUrl
	} from "@/api/biyang/home";
	export default {
		components: {
			back,
			cardHead,
			dialogVideo
		},
		data() {
			return {
				profileImg:profileIcon,
				form: {
					theEarEncipher: '',
					userId:this.$route.query.id,
					pageNum:1,
					pageSize:10,
					monthAge:'',
					liveVariety:'',
					
				},
				tableLoading: false,
				config: {},
				cateData:[],
				monthAgeData:[],
				ivestockInfo:{
					num:0,//存栏
					earTagNum:0,//建档
					femaleNum:0//基础母牛
				},
				weather:{
					humidity:'0',
					temperature:'0',
					weather:'未知',
					windpower:'未知'
				},
				userData:{},
				videoList:[],//视频列表
				player:null,
				dialogVisible:false,
				pastureName:'--'

			};
		},

		created() {

		},
		mounted() {
			window.goArchives=this.goArchives
			this.pastureName=this.$route.query.pastureName
			
			this.getUserInfo()
			this.getUserLivestockInfo()
			this.getWeather()
			this.getUserLivestockList()
			this.getMonthAgeList()
			this.getUserDeviceList()
		},
		methods: {
			showVideo(){
				this.dialogVisible=true
			},
			getUserDeviceList(){
				userDeviceList({userId:this.$route.query.id}).then(res=>{
					if(res.code==200){
						
						if(res.result&&res.result.length>0){
							let deviceInfo=res.result[0]
							
							this.videoList=res.result
							getPlayUrl({
								deviceCode: deviceInfo.deviceCode,
								channelCode: deviceInfo.channelCode,
								ezvizId: deviceInfo.ezvizId
								
							}).then(res=>{
								if(res.code==200){
									this.$nextTick(function() {
										let w=document.getElementById('video').offsetWidth
										let h=document.getElementById('video').clientHeight 
									    this.player = new EZUIKit.EZUIKitPlayer({
									    	id: 'video',
									    	url: res.result.url,
									    	accessToken: res.result.accessToken,
									    	template: "pcLive",
											width: w,
											height: h,
											showPTZControl: 'none'
									    })

									});
									
									
							
								}
								console.log(res)
							})
						}
					}
					
				})
			},
			getUserInfo(){
				userInfo({userId:this.$route.query.id}).then(res=>{
					console.log(res)
					this.userData=res.result
					this.profileImg=res.result.avatar||profileIcon
				})
			},
			handlRest(formName){
				this.$refs[formName].resetFields();
				this.getUserLivestockList()
			},
			getMonthAgeList(){
				getDict({dictType: "livestock_age"}).then(res=>{
					console.log(res)
					this.monthAgeData=res.result
				})
			},
			getUserLivestockList(){
				userLivestockList(this.form).then(res=>{
					if(res.code==200){
						if(res.result.length==0){
							this.config = {
								data: [],
								header: ['耳标编码', '活畜类别', '活畜品类', '月龄(月)', '操作'],
								align:['center','center','left','right','center'],
								headerBGC: '#202020',
								oddRowBGC: '#131313',
								evenRowBGC: '#131313',
								rowNum:10
							}
							return
						}
						console.log(res)
						let list=[]
						res.result.records.forEach(item=>{
							list.push([item.theEarEncipher, '牛', item.categoryName, item.monthAge||'未知',
								`<span style="border: 0.01rem solid rgba(0,0,0,0.26);padding:.05rem .1rem;color:#fff;cursor:pointer;background: rgba(255, 255, 255, 0.1);" onClick="goArchives('${item.theEarEncipher}')">查看档案</span>`
							])
						})
						
						this.config = {
							data: list,
							header: ['耳标编码', '活畜类别', '活畜品类', '月龄(月)', '操作'],
							align:['center','center','left','right','center'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum:10
						}
					}
				})
			},
			getWeather(){
				weatherInfo({districtId:'411726'}).then(res=>{
					
						console.log(res)

						this.weather.humidity=res.humidity
						this.weather.temperature=res.temperature
						this.weather.weather=res.weather
						this.weather.windpower=res.windpower
					
				})
			},
			getUserLivestockInfo(){
				userLivestockInfo({userId:this.$route.query.id}).then(res=>{
					if(res.code==200){
						console.log(res)
						this.ivestockInfo.num=res.result.num
						this.ivestockInfo.earTagNum=res.result.earTagNum
						this.ivestockInfo.femaleNum=0
						let data=[]
						let total=0
						res.result.livestockCountUserModels.forEach(item=>{
							data.push({ value: item.num, name: item.categoryName,categoryId:item.categoryId})
							total=total+item.num
						})

						this.cateData=data.map(item=>{
							item.pct=((item.value/total)*100).toFixed(2);return item
						})
						console.log(this.cateData)
						this.initRingChart({
						  dom: 'ringChart',
						  color: this.colors,
						  data: this.cateData
						})
					}
				})
			},
			goArchives(id){
				this.$router.push({
				    path:'/breed/archives',
					query:{
						id:id
					}
				})
			},
			// 初始化top-pie
			initRingChart(data) {
			  this.chart = this.$echarts.init(document.getElementById(data.dom))
			  this.chart.setOption({
			    
			    grid: {
			      left: '10%',
			      right: '10%',
			    },
			    color: data.color, // 饼图各块颜色
			    toolbox: {
			      show: false,
			    },
			    series: [
			      {
			        type: 'pie',
			        radius: [70, 110],
			        center: ['50%', '50%'],
			        roseType: data.roseType,
			        itemStyle: {
			          borderRadius: 0,
			        },
			        emphasis: {
			          label: {
			            show: true,
			            formatter: data.roseType ? '{a|{c}}\n{b|{b}} ' : '{b|{b}}\n{a|{d}%} ',
			            rich: {
			              a: {
			                fontSize: 20,
			                lineHeight: 36,
			                color: "#fff",
			                fontWeight: 'bold'
			              },
			              b: {
			                fontSize: 12,
			                lineHeight: 24,
			                color: "#fff"
			              }
			            }
			          }
			        },
			        label: {
			          show: false,
			          position: "center",
			          color: "rgba(13, 17, 29,1)",
			          fontSize: 14,
			          formatter: '{a|{b}}\n{b|{c}} ',
			          rich: {
			            a: {
			              fontSize: 20,
			              lineHeight: 36,
			              color: "#fff"
			            },
			            b: {
			              fontSize: 16,
			              lineHeight: 24,
			              color: "#fff"
			            }
			          }
			        },
			        labelLine: {
			          show: false
			        },
			        data: data.data,
			      }
			    ],
			  })
			},

		},
	};
</script>
<style lang="scss" scoped>
	.wrap {
		background: url(~@/assets/biyang/bg1.png);
		padding: 0.25rem;
		padding-top: 1.01rem;
		background-size: 100% 100%;
		.card-list{
			margin-top:0.16rem;
			.card-left{
				.card-info{
					padding: 0.25rem;
					width: 8.88rem;
					height: 2.5rem;
					background: rgba(0,0,0,0.26);
					margin-bottom:0.23rem;
					.card-base-info{
						margin-top:0.45rem;
						.profile{
							width: 1.25rem;
							height: 1.25rem;
							margin-right: 0.25rem;
							border-radius: 50%;
							border: 0.05rem solid rgb(166,192,212)
						}
						ul li{
							height: 0.18rem;
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Normal, Source Han Sans CN;
							font-weight: 350;
							color: #FFFFFF;
							line-height: 0.18rem;
						}
					}
					
				}
				.card-watch{
					padding: 0.25rem;
					width: 8.88rem;
					height: 8.94rem;
					background: rgba(0,0,0,0.26);
					.plane{
						padding: 0.6rem 0;
						.plane-item{
							width: 50%;
							padding-left: 0.25rem;
							.plane-icon{
								width: 0.24rem;
								height:0.24rem;
								margin-right: 0.28rem;
							}
							.plane-label{
								font-size: 0.18rem;
								font-family: Source Han Sans CN-Regular, Source Han Sans CN;
								font-weight: 400;
								color: #FFFFFF;
							}
							.plane-value{
								font-size: 0.23rem;
								font-family: Source Han Sans CN-Regular, Source Han Sans CN;
								font-weight: 700;
								color: #FFFFFF;
							}
						}
					}
					.title{
						margin-bottom: 0.33rem;
						.title-name{
							font-size: 0.2rem;
							font-family: Source Han Sans CN-Bold, Source Han Sans CN;
							font-weight: 700;
							color: #00E0DB;
							margin-left: 0.09rem;
						}
						.tag i{
							content: '';
							width: 0.11rem;
							height: 0.2rem;
							background: #00E0DB;
							border-radius: 0rem 0rem 0rem 0rem;
						}
						.tag img{
							width: 0.25rem;
							height:0.25rem;
						}
						.full{
							font-size: 0.15rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #00E0DB;
							margin-left: 0.09rem;
						}
					}
					.decora{
						border-image-source: radial-gradient(60% 85%, transparent 0px, transparent 100%, cyan 100%);
						border-image-slice: 2;
						border-width: 0.02rem;
						border-style: solid;
						border-image-outset: 0;
						.watch-border{
							padding: 0.23rem 0.28rem 0.2rem;
							width: 100%;
							height: 100%;
							border: 0.01rem solid rgba(0,224,219,0.08);
						}
						#video{
							width: 100%;
							height: 100%;
							background: #aaa;
							text-align: center;
		
							span{	
									
									color:#000;
									line-height: 4rem;
							}
						}
					}
					
				}
			}
			.card-right{
				padding: 0.25rem;
				width: 14.38rem;
				height: 11.67rem;
				background: rgba(0,0,0,0.26);
				.title{
					height: 0.3rem;
					font-size: 0.25rem;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 0.3rem;
				}
				.nums{
					margin-top: 0.26rem;
					li{
						margin-right: 0.14rem;
						width: 0.56rem;
						height: 0.63rem;
						line-height: 0.63rem;
						text-align: center;
						background: rgba(0,224,219,0.05);
						box-shadow: inset 0rem 0rem 0.08rem 0rem rgba(0,224,219,0.2);
						border-radius: 0rem 0rem 0rem 0rem;
						opacity: 1;
						border: 0.01rem solid rgba(0,224,219,0.1);
						font-size: 0.45rem;
						font-family: Impact-Regular, Impact;
						font-weight: 400;
						color: #00E0DB;
					}
					
					
				}
				.tab{
					margin-top: 0.5rem;
					li{
						width: 50%;
						.tab-name{
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
							height: 0.23rem;
							line-height: 0.23rem;
							margin-top: 0.05rem;
						}
						.tab-value{
							font-size: 0.38rem;
							font-family: AlibabaPuHuiTi-Bold, AlibabaPuHuiTi;
							font-weight: 700;
							color: #FFFFFF;
							height: 0.5rem;
							line-height: 0.5rem;
						}
					}
					
				}
				.tag{
					.tag-name{
						height: 0.2rem;
						font-size: 0.2rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #00E0DB;
						line-height: 0.23rem;
					}
					i{
						width: 0.11rem;
						height: 0.2rem;
						background: #00E0DB;
						margin-right: 0.09rem;

					}
				}
				#ringChart{
					width: 50%;
					height: 100%;
				}
				.legend{
					width: 50%;
					height: 100%;
					justify-content: center;
					
					li{
						height: 0.26rem;
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Normal, Source Han Sans CN;
						font-weight: 350;
						color: #00E0DB;
						line-height: 0.25rem;
						margin: 0.13rem 0;
						.circle{
							margin-right: 0.06rem;
							width: 0.08rem;
							height: 0.08rem;
							background: #3AFFFB;
							border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
						}
					}
				}
			}
		}
	}
</style>