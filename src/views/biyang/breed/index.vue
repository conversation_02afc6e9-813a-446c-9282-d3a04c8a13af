<template>
	<div class="wrap">

		<div class="card-list flex jc-between">
			<div class="card flex-column flex">
				<card-head titleIcon='title31'>

				</card-head>
				<div class='f1' style="overflow: hidden;">
					<dv-scroll-ranking-board :config="config" />
				</div>

			</div>
			<div class="card flex-column flex" style="width: 8rem;">
				<card-head titleIcon='title32'>
				</card-head>
				<div class='f1 flex' style="overflow: hidden;position: relative;" >
					<div id='mapMain' class='f1'></div>
					<div class='total flex'>
						<div class="total-item flex flex-column">
							<span class="count-value">{{totalEnterpriseNum}}</span>
							<span class='count-name'>牧场总数</span>
						</div>
						<div class="total-item flex flex-column">
							<span class="count-value">{{totalLivestockNum}}</span>
							<span class='count-name'>牛只存栏总数（头）</span>
						</div>
					</div>
				</div>

			</div>
			<div class="card flex-column flex">
				<card-head titleIcon='title33'>
				</card-head>
				<div id="ringChart" class="f1 flex jc-around" style="overflow: hidden;"></div>
				<div class="legends flex  flex-wrap">
					<div class="legend flex jc-between" v-for="(item,index) in ringData">
						<i class='round' :style="{background:colors[index]}"></i>
						<span class="label">{{item.name}}</span>
						<span class="value">{{item.value}}</span>
					</div>
				</div>
			</div>



			<div class="card flex-column flex">
				<card-head titleIcon='title34'>
				</card-head>
				<div class="f1 flex jc-between " style="align-items: flex-end;">
					<div class="panel flex flex-column align-items-center">
						<img class="panel-icon" src="@/assets/icons/svg/biyang/cattle.svg" alt="">
						<div class="panel-midle flex flex-column align-items-center">
							<span class="value">{{deviceCountInfo.eartagNum}}</span>
							<span class='label'>耳标数量</span>
						</div>
						<div class="panel-bottom">
							<span class='label'>耳标绑定只数：</span>
							<span class="value">{{deviceCountInfo.eartagUseNum}}</span>
						</div>
					</div>
					<div class="panel flex flex-column align-items-center">
						<img class="panel-icon" src="@/assets/icons/svg/biyang/watch.svg" alt="">
						<div class="panel-midle flex flex-column align-items-center">
							<span class="value">{{deviceCountInfo.ezvizNum}}</span>
							<span class='label'>监控设备</span>
						</div>
						<div class="panel-bottom">
							<span class='label'>监控安装（家）：</span>
							<span class="value">{{deviceCountInfo.ezvizUserNum}}</span>
						</div>
					</div>
				</div>
			</div>
			<div class="card flex-column flex" style="width: 15.75rem;">
				<card-head titleIcon='title35'>
					<div class="dorp">
						<span class="total-font">共计<span class="count">{{total}}</span> 条</span>
					</div>
				</card-head>


				<div class="">
					<el-form :inline="true" :model="form" ref="form" class="form-inline">
						<el-form-item prop="pastureName">
							<el-input v-model.trim="form.pastureName" placeholder="输入养殖户名称检索"></el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="custom" @click="getBreedList()">搜索</el-button>
							<el-button type="custom" @click="handlRest('form')">重置</el-button>
						</el-form-item>

					</el-form>
				</div>
				<div class='f1' style="overflow: hidden;">
					<dv-scroll-board :config="config1"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import {
		breedList,
		deviceCount,
		farmerListGroupByAddress
	} from "@/api/biyang/breed";
	import {
		livestockCate,	
	} from "@/api/biyang/home";
	import {
		getDict
	} from "@/api/biyang/index";
	const MapData = require('./biyang.json')
	export default {
		components: {

			cardHead
		},
		data() {
			return {
				form: {
					pastureName: ''
				},
				loadRingChart: false,
				config: {},
				ringData: [],
				tableLoading: false,
				config1: {},
				deviceCountInfo:{
					eartagNum:0,
					eartagUseNum:0,
					ezvizNum:0,
					ezvizUserNum:0
				},
				total:0,
				pastureNature:[],
				totalLivestockNum:0,//牛只存栏总数
				totalEnterpriseNum:0,//养殖户总量
			};
		},

		created() {

		},
		async mounted() {

			window.goDetail=this.goDetail
			try{
				await this.getPastureNatureDict()
			}catch(e){
				
			}
			
			
			
			
			this.getBreedList()
			this.getLivestockCate()
			this.getDeviceCount()
			this.getFarmerListGroupByAddress()
			
		},
		methods: {
			getFarmerListGroupByAddress(){
				farmerListGroupByAddress({}).then(res=>{
					if(res.code==200){
						let townList=[
							{
								name: '赊湾镇',
								
								value: 0
							},
							{
								name: '羊册镇',
								
								value: 0
							},
							{
								name: '付庄乡',
								
								value: 0
							},
							{
								name: '官庄镇',
								
								value: 0
							},
							{
								name: '春水镇',
								
								value: 0
							},
							{
								name: '盘古乡',
								
								value: 0
							},
							{
								name: '下碑寺乡',
								
								value: 0
							},
							{
								name: '象河乡',
								
								value: 0
							},
							{
								name: '杨家集乡',
								
								value: 0
							},{
								name:'王店镇',
								value:0
							},
							{
								name:'贾楼乡',
								value:0
							},
							{
								name:'高邑乡',
								value:0
							},
							{
								name:'郭集镇',
								value:0
							},
							{
								name:'王店镇',
								value:0
							},
							{
								name:'铜山乡',
								value:0
							},
							{
								name:'高店乡',
								value:0
							},{
								name:'双庙街乡',
								value:0
							},{
								name:'古城街道',
								value:0
							},{
								name:'泌水街道',
								value:0
							},{
								name:'花园街道',
								value:0
							},{
								name:'泰山庙镇',
								value:0
							},{
								name:'黄山口乡',
								value:0
							},
							{
								name:'马谷田镇',
								value:0
							}
						]
						let data=townList.map(item1=>{
							let matchingItem =res.result.find(item2=>item1.name==item2.townName)
							if (matchingItem) {
								return {name: item1.name, value: matchingItem.livestockNum};
							} else {
								return item1;
							}
						})
						this.totalLivestockNum=0
						this.totalEnterpriseNum=0
						res.result.forEach(item=>{
							this.totalLivestockNum+=item.livestockNum
							this.totalEnterpriseNum+=item.enterpriseNum
						})
						console.log(data)
						this.drawMap(data)
					}
				})
			},
			async getPastureNatureDict(){
				let res=await getDict({dictType: "pasture_nature"})
				if(res.code==200){
					this.pastureNature=res.result
				}
			},
			getDeviceCount(){
				deviceCount({countyId:'367795245104893970'}).then(res=>{
					if(res.code==200){
						console.log(res)
						this.deviceCountInfo=res.result
					}
				})
			},
			getLivestockCate(){
				livestockCate({}).then(res=>{
					if(res.code==200){
						console.log(res)
						let data=[]
						res.result.forEach(item=>{
							data.push({
								value: item.num,
								name: item.categoryName
							})
						})
						this.ringData = data
						this.initRingChart({
							dom: 'ringChart',
							data: data,
						})
					}
				})
			},
			handlRest(formName){
				this.$refs[formName].resetFields();
				this.getBreedList()
			},
			filterPastureNature(val){
				let res=this.pastureNature.find(item=>item.dictValue==val)
				if(res){
					return res.dictLabel
				}else{
					'未知'
				}
				
			},
			getBreedList(){
				breedList({
					countyId:'367795245104893970',
					pageNum:1,
					pageSize:100,
					pastureName:this.form.pastureName
				}).then(res=>{
					if(res.code==200){
						// 处理养殖户排行与查询
						this.total=res.result.total
						let list = []
						res.result.list.forEach((item,index)=>{
							
							list.push([index + 1, item.pastureName, this.filterPastureNature(item.pastureNature) , item.countyName+"-"+item.address , item.livestockNum,
								`<span style="padding:.05rem .1rem;color:#fff;cursor:pointer;background: rgba(255, 255, 255, 0.1);" onClick="goDetail('${item.userId}','${item.pastureName}')">查看</span>`
							])
						})
					
						this.config1 = {
							data: list,
							header: ['排行', '名称', '养殖户类型', '所在地域', '养殖活畜(头/只)', ''],
							align:['center','left','left','left','left','center'],
							headerBGC: '#202020',
							oddRowBGC: '#131313',
							evenRowBGC: '#131313',
							rowNum: 7,
							columnWidth: [50]
						}
						
						// 处理养殖规模分布数据
						if(!this.config.data){
							let list1=[]
							res.result.list.forEach((item,index)=>{
								list1.push({
									name: item.pastureName,
									value: item.livestockNum
								})
							})
							this.config = {
								rowNum: 6,
								data: list1,
								unit: '头'
							}
						}
					}
					console.log(res)
				})
			},
			goDetail(userId,pastureName){
				console.log(userId,pastureName)

				this.$router.push({
				    path:'/breed/detail',
					query:{
						id:userId,
						pastureName:pastureName
					}
				})
			},
			drawMap(data) {
				const _this = this
				// 基于准备好的dom，初始化echarts实例
				var chart = this.$echarts.init(document.getElementById('mapMain'))

				this.$echarts.registerMap('bayannaoer', MapData)
				// 隐藏动画加载效果。
				chart.hideLoading()

				let cahrtOption = {

					//视觉映射组件,用于进行『视觉编码』，也就是将数据映射到视觉元素（视觉通道）
					tooltip: {
						trigger: 'item',
						formatter: '{b}<br/>{c} 头'
					},
					visualMap: {
						// type:'piecewise',
						right:0,
						bottom:0,
						min: 0,
						max: 600,
						align:'left',
						itemSymbol:'circle',
						// text: ['低产值', '高产值'],
						realtime: false,
						calculable: true,
						maxOpen:true,
						minOpen:true,
						textStyle:{
							color:'#fff',
						},
						//   dimension: 2,
						inRange: {
							color:['rgba(40 ,40 ,40,1)', 'rgba(140 ,150 ,150,1)', 'rgba(244,165,47,1)',
								'rgba(242,150,43,1)', 'rgba(247,130,24,1)', 'rgba(249,105,33,1)']   //显示颜色
						},

					},
					series: [{
						//地图，用于地理区域数据的可视化，配合visualMap使用更好
						type: 'map3D',
						//系列名称
						name: 'bayannaoer',
						// colorBy: 'data',
						map: 'bayannaoer',
						//三维地图每个区域的高度,厚度
						regionHeight: 6,
						//背景环境贴图  右/下/左/上四个方位
						// environment: parkCenter,
						left: '-10%',
						bottom:'-6%',
						viewControl: {
							autoRotate: false,
							rotateSensitivity: 1,
							zoomSensitivity: 1,
							//X轴绕
							alpha: 45,
							//y轴绕
							beta: 0,
							distance: 150
						},

						itemStyle: {
							//普通样式
							normal: {
								opacity: 1,
								borderWidth: 1,
								borderColor: 'rgb(140 ,177, 187)', //边界分割样式
								label: {
									show: false,
									//   formatter: () => {
									//     return '<div style="color:red">123456</div>';
									//   },
									textStyle: {
										color: 'rgba(0,0,0,0)', //文字颜色
										fontSize: 14 //文字大小
									}
								}
							},
							//高亮样式
							emphasis: {
								// 选中样式
								borderWidth: 1.5,
								// borderColor: 'rgba(185,253,0,0)',
								// color: 'rgb(185,253,252)',
								label: {
									show: false,
									textStyle: {
										color: '#FFFFFF'
									}
								}
							}
						},
						shading: 'color',
						//针对 GeoJSON 要素的自定义属性名称，作为主键用于关联数据点和 GeoJSON 地理要素
						nameProperty: 'name',
						data: data,
						//center: [119.929284, 28.473734],
						//地图的长宽比
						aspectScale: 1.0,
						//选择类型,
						selectedMode: 'single',
						//鼠标经过高亮
						hoverable: false,
						//鼠标滚轮缩放
						roam: true
					}]
				}


				chart.setOption(cahrtOption)
			},
			initRingChart(data) {
				var myChart = this.$echarts.init(document.getElementById(data.dom));
				var option = {
					color: this.colors,
					grid: {
						top: '0%',
						left: '10%',
					},
					tooltip: {
						trigger: 'item',
						backgroundColor: 'rgba(255,255,255,0.8)',
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#050F37',
						},
						padding: 5,
						extraCssText: "box-shadow: rgba(47, 244, 225, 0.5) 0px 0px 10px;",
					},
					series: [{
						type: 'pie',
						radius: ['35%', '55%'],
						label: {
							show: true,
							position: 'outside',
							color: '#FFFFFF',
							padding: [0, 0],
							alignTo: 'labelLine'
						},
						emphasis: {
							label: {
								show: true,
							}
						},
						labelLine: {
							show: true,
							length: 5,
							length2: 50,
							lineStyle: {
								color: '#73c0de',
							}
						},
						data: data.data
					}]
				};
				myChart.clear();
				myChart.setOption(option)
				this.loadRingChart = true
				this.$nextTick(()=>{
					myChart.resize()
				})
				
				window.addEventListener('resize', () => {
					setTimeout(() => {
					  myChart && myChart.resize()
					}, 100)
					
				})
			},
		},
	};
</script>
<style lang="scss" scoped>
	.wrap {
		background: url(~@/assets/biyang/bg1.png);
		padding: 0.25rem;
		padding-top: 1.13rem;
		background-size: 100% 100%;


		.card-list {
			flex-wrap: wrap;

			.card {
				padding: 0.25rem;
				margin-top: 0.25rem;
				width: 7.5rem;
				height: 5.81rem;
				background: rgba(0, 0, 0, 0.26);
				border-radius: 0rem 0rem 0rem 0rem;
				opacity: 1;

				.dorp {
					.total-font {
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #D8DFFE;
					}

					.count {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #FCD827;
					}
				}

				.total-list {


					font-size: 0.18rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 0.18rem;

					li {
						margin-bottom: 0.39rem;
						display: flex;
						align-items: center;

						.count {
							font-size: 0.23rem;
							font-family: Source Han Sans CN-Medium;
							font-weight: 700;
							color: #FFFFFF;
							margin: 0 0.13rem;
						}

						.unit {
							font-size: 0.2rem;
							color: rgba(250, 250, 250, 0.60);
						}
					}

					.circle::before {
						content: '';
						display: flex;
						width: 0.13rem;
						height: 0.13rem;
						background: #00E0DB;
						border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
						margin-right: 0.13rem;
					}

				}

				.legends {

					// padding: 0.4rem;
					.legend {
						box-sizing: content-box;
						padding: 0.1rem 0;
						width: 1.36rem;
						height: 0.26rem;
						font-size: 0.13rem;
						font-family: Source Han Sans CN-Normal, Source Han Sans CN;
						font-weight: 350;
						color: #FFFFFF;
						line-height: 0.26rem;
						position: relative;
						margin-left: 0.7rem;

						.round {
							
							width: 0.08rem;
							height: 0.08rem;
							background: #0EDAE2;
							border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
							position: absolute;
							left: -0.2rem;
							top: 50%;
							transform: translateY(-50%);
						}
					}
				}

				.panel {
					width: 3.38rem;
					height: 4.56rem;
					background: linear-gradient(154deg, rgba(255, 255, 255, 0.09) 0%, rgba(11, 40, 106, 0.19) 57%, rgba(255, 255, 255, 0.07) 100%);
					opacity: 0.94;

					.panel-icon {
						width: 0.94rem;
						height: 1.01rem;

						margin-top: 0.6rem;
					}

					.panel-midle {
						margin-top: 0.63rem;

						.value {
							height: 0.43rem;
							font-size: 0.38rem;
							font-family: Impact-Regular, Impact;
							font-weight: 400;
							color: #FFF200;
						}

						.label {
							margin-top: 0.24rem;
							height: 0.18rem;
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
						}

					}

					.panel-bottom {
						margin-top: 0.9rem;

						.value {
							font-size: 0.22rem;
							font-family: Impact-Regular, Impact;
							font-weight: 400;
							color: #FFFFFF;
							letter-spacing: 1px;
						}

						.label {
							font-size: 0.18rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
						}
					}
				}
				.total{
					position: absolute;
					top:0.46rem;
					left:0;
					z-index:999;
					.total-item{
						width: 1.46rem;
						margin-right: 0.75rem;
						.count-value{
							height: 0.4rem;
							font-size: 0.38rem;
							font-family: Impact-Regular, Impact;
							font-weight: 400;
							color: #FFF200;
						}
						.count-name{
							height: 0.19rem;
							font-size: 0.15rem;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #FFFFFF;
						}
					}
				}
			}
		}
	}
</style>