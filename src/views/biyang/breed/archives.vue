<template>
	<div class="wrap flex jc-between">
		<video src="@/assets/biyang/bg.mp4" autoplay="autoplay" loop class="fillWidth" muted='muted'>
		                </video>

		<div class="back">
			<back></back>
		</div>
		<div class='side'>
			<div class="chart-box flex flex-column">
				<card-head titleIcon='title331'>
					<div class="dorp flex">
						<span v-for='item in tabs' :class="active1==item.value?'active':''"
							@click="changeTab1(item.value)">{{item.name}}</span>

					</div>
				</card-head>
				<div class='avg'>
					<span class="avg-label">平均运动量：</span>
					<span class="avg-value">{{avg1}}</span>
				</div>
				<div class='f1' id='lineChart' style="overflow: hidden;"></div>
			</div>
			<div class="map-box flex flex-column">
				<card-head titleIcon='title333'>
				</card-head>
				<div class='decora  f1'>
					<div class="watch-border">
						<div class="map" id='map'></div>
					</div>
				</div>
			</div>
		</div>
		<div class='middle f1 flex flex-column'>
			<div class="panels flex">
				<div class="panel flex flex-column">
					<span class='panel-label'>耳标编码</span>
					<span class='panel-value'>{{baseInfo.earTagNo}}</span>
				</div>
				<div class="panel flex flex-column">
					<span class='panel-label'>活畜品种</span>
					<span class='panel-value'>{{baseInfo.categoryName}}</span>
				</div>
				<div class="panel flex flex-column">
					<span class='panel-label'>月龄</span>
					<span class='panel-value'>{{baseInfo.livestockAge||'未知'}}</span>
				</div>
				<div class="panel flex flex-column">
					<span class='panel-label'>出生日期</span>
					<span class='panel-value'>{{baseInfo.birthday||'未知'}}</span>
				</div>
			</div>
			<div class="flex-1 niu">
				<!-- <div class="heart info">
					<div class="card-info flex">
						<div class='card-left flex flex-column align-items-center'>
							<img  src="@/assets/icons/svg/biyang/heart.svg" alt="">
							<div class='card-line'></div>
						</div>
						<div class='card-right'>
							<div class='card-value'>97</div>
							<div class='card-label'>当前心率</div>
						</div>
					</div>
				</div> -->

				<div class="activation info">
					<div class="card-info flex">
						<div class='card-left flex  align-items-center'>
							<div class='card-line'></div>
							<img src="@/assets/icons/svg/biyang/heart.svg" alt="">

						</div>
						<div class='card-right'>
							<div class='card-value'>活跃</div>
							<div class='card-label'>活跃度</div>
						</div>
					</div>
				</div>
				<div class="sport info">
					<div class="card-info flex flex-column">
						<div class='card-left flex  align-items-center'>

							<img src="@/assets/icons/svg/biyang/sport.svg" alt="">
							<div class='card-line'></div>
						</div>
						<div class='card-right'>
							<div class='card-value'>{{nowSport}}</div>
							<div class='card-label'>今日运动量</div>
						</div>
					</div>
				</div>
				<div class="temp info">
					<div class="card-info flex ">
						<div class='card-left flex  align-items-center'>
							<div class='card-line'></div>
							<img src="@/assets/icons/svg/biyang/temp1.svg" alt="">

						</div>
						<div class='card-right'>
							<div class='card-value'>{{nowTemperature}}</div>
							<div class='card-label'>当前体温</div>
						</div>
					</div>
				</div>
				<img class="niu-img" src="@/assets/biyang/niu.png" alt="">
			</div>
			<div class="footer flex jc-around">
				<div class="tab flex flex-column align-items-center" @click="showDetail(index,item)"
					:class="{ tabActive: index ==3 }" v-for="(item,index) in footTabs">
					<img class="tab-icon" :src='"@/assets/icons/svg/biyang/"+item.icon+".svg"' alt="">
					<span class="tab-name">{{ item.name }}</span>
				</div>
			</div>
		</div>
		<div class='side'>
			<div class="chart-box flex flex-column">
				<card-head titleIcon='title332'>
					<div class="dorp flex">
						<span v-for='item in tabs' :class="active2==item.value?'active':''"
							@click="changeTab2(item.value)">{{item.name}}</span>

					</div>
				</card-head>
				<div class='avg'>
					<span class="avg-label">平均体温：</span>
					<span class="avg-value">{{avg2}}</span>
				</div>
				<div class='f1' id='lineChart1' style="overflow: hidden;"></div>
			</div>
			<div class="map-box flex flex-column">
				<card-head titleIcon='title334'>
				</card-head>
				<div class='decora  f1'>
					<div class="watch-border">
						<div class="map" id='map1'></div>
					</div>
				</div>
			</div>
		</div>

		<my-dialog :dialogType='dialogType' :dialogShow='dialogShow' :dialogData='dialogData'></my-dialog>
	</div>
</template>

<script>
	import cardHead from '../components/card-head.vue'
	import back from '../components/back.vue'
	import myDialog from '../components/dialog.vue'
	import {
		livestockDetail,
		livestockInfo,
		livestockAssembly,
	} from "@/api/biyang/breed";
	
	import {
		getDict
	} from "@/api/biyang/index";
	
	const Dict={}
	export default {
		components: {
			back,
			cardHead,
			myDialog
		},
		data() {
			return {
				dialogType: 0,
				dialogShow: false,
				dialogData: {
					title: '',
					data: []
				},
				tabs: [{
						name: '近7日',
						value: 'seven'
					},
					{
						name: '近15日',
						value: 'fifteen'
					},
					{
						name: '近1月',
						value: 'thirty'
					},
				],
				active1: 'seven',
				avg1: 0,
				active2: 'seven',
				avg2: 0,
				temperatureAllList: {},
				nowTemperature: 0,
				sportAllList: {},
				nowSport: 0,
				liveStockLocation: {},
				electronicFences: {},
				footTabs: [{
						name: '基础信息',
						icon: 'tab1'
					},
					{
						name: '免疫记录',
						icon: 'tab2',
						actionType: 2
					},
					{
						name: '检疫记录',
						icon: 'tab3',
						actionType: 6
					},
					{
						name: '活畜动态',
						icon: 'tab4'
					},
					{
						name: '称重记录',
						icon: 'tab5',
						actionType: 1
					},
					{
						name: '疾病治疗记录',
						icon: 'tab6',
						actionType: 3
					},
					{
						name: '繁育记录',
						icon: 'tab7',
						actionType: 5
					},

				],
				baseInfo: {},


			};
		},

		created() {
			this.getDicts()
		},
		mounted() {

			setTimeout(() => {







			}, 10)


			this.getLivestockInfo()

		},
		methods: {
			getDicts(){
				// 检疫方式
				getDict({dictType: "livestock_quarantine_type"}).then(res=>{
					console.log(res)
					Dict.quarantine_mode=res.result
				})
				// 免疫方式
				getDict({dictType: "livestock_vaccination_way"}).then(res=>{
					console.log(res)
					Dict.immune_mode=res.result
				})
				// 分娩方式
				getDict({dictType: "birthing_type"}).then(res=>{
					console.log(res)
					Dict.birthing_mode=res.result
				})
				// 分娩结果
				getDict({dictType: "birthing_result"}).then(res=>{
					console.log(res)
					Dict.birthing_result=res.result
				})
				// 疾病类型
				getDict({dictType: "livestock_disease_type"}).then(res=>{
					console.log(res)
					Dict.diseaseType=res.result
				})
				// 检疫项目
				getDict({dictType: "livestock_quarantine_project"}).then(res=>{
					console.log(res)
					Dict.quarantineProject=res.result
				})

			},
			getArrLast(arr) {
				return arr[arr.length - 1]
			},
			getLivestockAssembly() {
				let animalId = this.baseInfo.livestockId
				let iccid = this.baseInfo.earTagNo
				livestockAssembly({
					animalId,
					iccid
				}).then(res => {
					if (res.code == 200) {
						this.temperatureAllList = res.result.result.temperatureDisMap
						this.sportAllList = res.result.result.exerciseDisMap
						this.liveStockLocation = res.result.result.liveStockLocation
						this.electronicFences = res.result.result.electronicFences
						console.log(this.sportAllList)
						console.log(this.temperatureAllList)
						this.nowTemperature = this.getArrLast(this.temperatureAllList['seven'].value)
						this.nowSport = this.getArrLast(this.sportAllList['seven'].value)
						this.changeTab1('seven')
						this.changeTab2('seven')
						this.initMap({
							dom: 'map',
							x: this.liveStockLocation[0],
							y: this.liveStockLocation[1],
						})
						this.initMap({
							dom: 'map1',
							x: this.liveStockLocation[0],
							y: this.liveStockLocation[1],
							dataList: this.electronicFences
						})
					}
					// console.log(res)
				})
			},
			getLivestockInfo() {
				livestockInfo({
					earTagNo: this.$route.query.id
				}).then(res => {
					if (res.code == 200) {
						this.baseInfo = res.result
						this.getLivestockAssembly()

					}
					console.log(res)
				})
			},
			filterDict(val,key){
				let dict=Dict[key].find(item=>item.dictValue==val)
				if(dict){
					return dict.dictLabel
				}else{
					return '其他'
				}
			},
			getLivestockDetail(item) {
				let livestockId = this.baseInfo.livestockId
				console.log(livestockId)
				livestockDetail({
					livestockId: livestockId,
					actionType: item.actionType,
					pageNum: 1,
					pageSize: 10
				}).then(res => {
					let list=[]
					let header=[]
					// 称重
					if(item.actionType==1){
						header=['称重日期','本次称重重量','上次称重重量','比上次增重','称重人员']
						if(res.result&&res.result.list){
							res.result.list.forEach(item=>{
								list.push([item.operateTime,item.weightCurrent ,item.weightCurrent-item.weightIncrease ,item.weightIncrease,item.operatePeopleName||'-'])
							})
						}
						
					}
					// 免疫
					console.log(Dict)
					if(item.actionType==2){
						header=['免疫日期','疫苗名称','剂量','接种方式','疫苗厂家','疫苗批次','操作人员','免疫说明']
						if(res.result&&res.result.list){
							res.result.list.forEach(item=>{
								list.push([
									item.operateTime,
									item.vaccineName,
									item.vaccineDose ,
									this.filterDict(item.operateType,'immune_mode'),
									item.company,
									item.vaccineBatches,
									item.operatePeopleName,
									item.remark||'-'
								])
							})
						}
					}
					// 疾病治疗记录
					if(item.actionType==3){
						header=['治病日期','疾病类型','症状及并发症','治疗方法','兽医姓名','治疗结果']
						if(res.result&&res.result.list){
							res.result.list.forEach(item=>{
								list.push([
									item.operateTime,
									this.filterDict(item.diseaseType,'diseaseType'),
												
									item.diseaseSymptoms,
									item.diseaseTreatmentOptions,
									item.operatePeopleName,
									item.diseaseHealResult
								])
							})
						}
						
					}
					
					// 繁育记录
					if(item.actionType==5){
						header=['分娩日期','分娩结果','产犊数量','繁育说明']
						if(res.result&&res.result.list){
							res.result.list.forEach(item=>{
								list.push([
									item.operateTime,
											
									this.filterDict(item.operateResult,'birthing_result'),
									item.livestockNumber ,
									item.remark||'-'
								])
							})
						}
						
					}
					
					// 检疫
					if(item.actionType==6){
						header=['检疫日期','检疫项目','检疫方式','操作人员','检疫说明']
						if(res.result&&res.result.list){
							res.result.list.forEach(item=>{
								list.push([
									item.operateTime,
									this.filterDict(item.quarantineProject,'quarantineProject'),
									this.filterDict(item.operateType,'quarantine_mode'),
									item.operatePeopleName||'-',
									item.remark||'-'
								])
							})
						}
						
						
					}
					this.dialogData = {
						title: item.name,
						data: {
							data: list,
							header: header,
							headerBGC: '#202020',
							// oddRowBGC: '#131313',
							// evenRowBGC: '#131313',
					
						}
					}
					this.dialogShow = true
					
				})
			},
			showDetail(index, item) {
				console.log(index)
				if(index==3){
					return
				}
				if (index == 0) {
					this.dialogData = {
						title: item.name,
						data: [{
								name: '耳标编码',
								value: this.baseInfo.earTagNo
							},
							{
								name: '活畜类别',
								value: this.baseInfo.categoryName||'-'
							},
							{
								name: '出生日期',
								value: this.baseInfo.birthday||'-'
							},
							{
								name: '月 龄',
								value:  this.baseInfo.livestockAge||'-'
							},
							
							{
								name: '父亲编号',
								value:  this.baseInfo.fatherTagNo||'-'
							},
							{
								name: '父亲编号类型',
								value:  this.baseInfo.fatherTagType||'-'
							},
							{
								name: '母亲编号',
								value:  this.baseInfo.motherTagNo||'-'
							},
							{
								name: '母亲编号类型',
								value:  this.baseInfo.motherTagType||'-'
							},
						]
					}
					this.dialogShow = true
				} else {
					this.getLivestockDetail(item)
				}
				this.dialogType = index
				
			},
			changeTab1(val) {
				this.active1 = val
				this.avg1 = this.sportAllList[this.active1].avg
				this.initChart({
					name: '运动量',
					dom: 'lineChart',
					unit: '步',
					data: {
						xAxis: this.sportAllList[this.active1].label,
						yAxis: this.sportAllList[this.active1].value
					}

				})
			},
			changeTab2(val) {
				this.active2 = val
				this.avg2 = this.temperatureAllList[this.active2].avg
				this.initChart({
					name: '体温',
					dom: 'lineChart1',
					unit: "℃",
					data: {
						xAxis: this.temperatureAllList[this.active2].label,
						yAxis: this.temperatureAllList[this.active2].value.map(item => {
							return Number(item)
						})
					}
				})
			},
			initMap(data) {
				let map = new T.Map(data.dom);
				map.centerAndZoom(new T.LngLat(data.x, data.y), 15);
				var icon = new T.Icon({
					iconUrl: "http://api.tianditu.gov.cn/img/map/markerA.png",
					iconSize: new T.Point(20, 30),
					iconAnchor: new T.Point(10, 30)
				});
				//向地图上添加自定义标注
				var marker = new T.Marker(new T.LngLat(data.x, data.y), {
					icon: icon
				});
				map.addOverLay(marker);
				if (data.dom == 'map1') {
					var points = [];
					console.log(data)
					data.dataList.forEach(item => {
						points.push(new T.LngLat(item.longs, item.lat));
					})
					console.log(points)
					var polygon = new T.Polygon(points, {
						color: "#00E0DB",
						weight: 3,
						opacity: 0.5,
						fillColor: "#FFFFFF",
						fillOpacity: 0.5
					});
					map.addOverLay(polygon);
				}
			},

			initChart(data) {

				this.chart = this.$echarts.init(document.getElementById(data.dom))

				var option = {
					color: ['#00E0DB'],
					tooltip: {
						trigger: 'axis',
						formatter: function(params) {
							return `<p style="font-size: 14px; font-weight: normal;">${params[0].name}</p><br/>
				                    <p style="font-size: 14px; font-weight: normal;">${params[0].value + data.unit}</p>`;
						},
						padding: [5, 20],
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#fff',
						},
					},
					grid: {
						// top: '20%',
						left: '4%',
						right: '4%',
						bottom: '0%',
						// height:'120px',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						boundaryGap: true,
						axisLabel: {
							color: '#00E0DB'
						},
						axisLine: {
							show: true, // X轴 网格线 颜色类型的修改
							lineStyle: {
								color: '#075F73',
							},
						},
						axisTick: {
							// X轴线 刻度线
							show: true,
							inside: true,
							alignWithLabel: true
						},
						data: data.data.xAxis,
					},
					yAxis: {
						name: data.name,
						nameTextStyle: {
							color: '#00E0DB',
							padding: [0, 0, 0, -40],

							lineHeight: 30,

						},
						type: 'value',
						// splitNumber: 4,
						axisLine: {
							show: false, // Y轴线
							lineStyle: {
								color: 'rgba(85, 193, 255, 1)',
							},
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: 'rgba(255,255,255,0.05)'
							}
						},
						axisLabel: {
							color: '#00E0DB',
						},
					},
					series: [{
						data: data.data.yAxis,
						type: 'line'
					}],
				}
				// this.chart.clear();
				console.log(option)
				// this.$nextTick(()=>{

				// });
				this.chart.setOption(option)
				window.addEventListener('resize', () => {
					this.chart.resize()
				})

			}

		},
	};
</script>
<style lang="scss" scoped>
	.wrap {
		
		// background: url(~@/assets/biyang/bg1.png);
		padding: 0.25rem;
		padding-bottom: 0.57rem;
		padding-top: 1.01rem;
		height: 13.5rem;

		.back {
			position: absolute;
			// top: 1.08rem;
			left: 0.24rem;
		}

		.side {

			.chart-box {
				padding: 0.25rem;
				margin-top: 0.6rem;
				width: 6.25rem;
				height: 5.41rem;
				background: rgba(0, 0, 0, 0.26);

				.dorp span {
					cursor: pointer;
					margin-left: 0.13rem;
					text-align: center;
					padding: 0.05rem 0.06rem;
					background: rgba(255, 255, 255, 0.05);
					border-radius: 0.03rem 0.03rem 0.03rem 0.03rem;

					font-size: 0.13rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #7B8A8A;
					line-height: 0.23rem;
				}

				.dorp .active {
					margin-left: 0.13rem;
					text-align: center;
					padding: 0.05rem 0.06rem;
					background: rgba(0, 224, 219, 0.2);
					border-radius: 0.03rem 0.03rem 0.03rem 0.03rem;

					font-size: 0.13rem;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #00E0DB;
					line-height: 0.23rem;
				}

				.avg {
					margin-top: 0.29rem;

					.avg-label {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #FFFFFF;
					}

					.avg-value {
						font-size: 0.23rem;
						font-family: Impact-Regular, Impact;
						font-weight: 400;
						color: #00E0DB;
					}
				}
			}

			.map-box {
				padding: 0.25rem;
				margin-top: 0.25rem;
				width: 6.25rem;
				height: 5rem;
				background: rgba(0, 0, 0, 0.26);
				border-radius: 0rem 0rem 0rem 0rem;

				.decora {
					margin-top: 0.25rem;
					border-image-source: radial-gradient(60% 85%, transparent 0px, transparent 100%, cyan 100%);
					border-image-slice: 2;
					border-width: 0.02rem;
					border-style: solid;
					border-image-outset: 0;

					.watch-border {
						padding: 0.16rem 0.19rem;
						width: 100%;
						height: 100%;
						border: 0.01rem solid rgba(0, 224, 219, 0.08);
					}

					.map {
						width: 100%;
						height: 100%;

					}
				}
			}
		}

		.middle {
			.panels {
				justify-content: center;
				margin-top: 0.26rem;

				.panel {
					padding: 0 0.06rem;
					align-items: center;

					.panel-label {
						font-size: 0.18rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #FFFFFF;
						margin-bottom: 0.08rem;
					}

					.panel-value {
						font-size: 0.25rem;
						font-family: Source Han Sans CN-Bold, Source Han Sans CN;
						font-weight: 700;
						color: #00E0DB;
						line-height: 0.75rem;
						width: 2.25rem;
						height: 0.75rem;
						background: rgba(0, 0, 0, 0.3);
						text-align: center;
						border-image-source: radial-gradient(55% 90%, transparent 0px, transparent 100%, cyan 100%);
						border-image-slice: 2;
						border-width: 0.01rem;
						border-style: solid;
						border-image-outset: 1;

					}
				}
			}

			.niu {
				position: relative;

				.niu-img {
					position: absolute;
					top: 1.34rem;
					left: 1.6rem;
					width: 6.46rem;
					height: 5.35rem;
					// transform: translate(-50%,-50%);
				}

				.info {
					position: absolute;

					z-index: 9;

					.card-info {
						.card-left {

							img {
								width: 0.78rem;
								height: 0.73rem;
								margin: 0.11rem;
							}

							.card-line {

								opacity: 1;
								border: 0.01rem solid rgba(0, 223, 218, 0.15);

								position: relative;
							}

							.card-line::before {
								content: '';
								width: 0.13rem;
								height: 0.13rem;
								border-radius: 50%;
								background: #012D40;
								position: absolute;

							}

							.card-line::after {
								content: '';
								width: 0.13rem;
								height: 0.13rem;
								border-radius: 50%;
								background: rgba(255, 255, 255, 1);
								position: absolute;

								-webkit-animation: warn 3s ease-out;
								-moz-animation: warn 3s ease-out;
								animation: warn 3s ease-out;
								-webkit-animation-iteration-count: infinite;
								-moz-animation-iteration-count: infinite;
								animation-iteration-count: infinite;

							}
						}

						.card-right {

							// margin-left: 0.11rem;
							.card-value {
								height: 0.5rem;
								font-size: 0.45rem;
								font-family: 汉仪菱心体简-regular, 汉仪菱心体简;
								font-weight: 400;
								color: #FFF200;
								line-height: 0.5rem;
							}

							.card-label {
								height: 0.31rem;
								font-size: 0.18rem;
								font-family: Source Han Sans CN-Normal, Source Han Sans CN;
								font-weight: 350;
								color: #E1E3EF;
								line-height: 0.31rem;
							}
						}

					}
				}

				.heart {
					left: 3.65rem;
					top: 0.7rem;

					.card-line {
						width: 0rem;
						height: 1.34rem;
					}

					.card-line::before {

						top: -0.06rem;
						left: 50%;
						transform: translateX(-50%);
					}

					.card-line::after {
						bottom: 0;
						left: -0.06rem;

					}
				}

				.activation {
					right: 0.9rem;
					top: 4.24rem;

					.card-line {
						width: 1.4rem;
						height: 0rem;
					}

					.card-line::before {

						top: -0.06rem;
						right: -0.06rem;

					}

					.card-line::after {
						top: -0.06rem;
						left: 0%;

					}
				}

				.sport {
					left: 1.27rem;
					top: 4.96rem;

					.card-line {
						width: 2.01rem;
						height: 0rem;
					}

					.card-line::before {
						top: -0.06rem;
						left: -0.06rem;


					}

					.card-line::after {
						top: -0.06rem;
						right: -0.06rem;


					}
				}

				.temp {
					right: 1.45rem;
					top: 1.36rem;

					.card-line {
						width: 1.1rem;
						height: 100%;
						top: 50%;
						border-right: none !important;
						border-bottom: none !important;
						// width: 2.01rem;
						// height: 0rem;
					}

					.card-line::before {
						top: -0.06rem;
						right: -0.06rem;


					}

					.card-line::after {
						bottom: -0.13rem;
						left: -0.06rem;


					}
				}

			}

			.footer {
				align-items: flex-end;

				.tab {
					cursor: pointer;

					.tab-icon {
						width: 0.78rem;
						height: 0.75rem;
					}

					.tab-name {
						margin-top: 0.13rem;
						font-size: 0.23rem;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #CCD0D4;
					}

				}

				.tab:nth-child(1) {
					margin-bottom: 0.06rem;
				}

				.tab:nth-child(2) {
					margin-bottom: 0.20rem;
				}

				.tab:nth-child(3) {
					margin-bottom: 0.30rem;
				}


				.tab:nth-child(5) {
					margin-bottom: 0.30rem;
				}

				.tab:nth-child(6) {
					margin-bottom: 0.20rem;
				}

				.tab:nth-child(7) {
					margin-bottom: 0.06rem;
				}

				.tabActive {
					.tab-icon {
						width: 1.73rem;
						height: 1.79rem;
					}

					.tab-name {
						margin-top: 0.18rem;

						font-size: 0.25rem;
						font-family: Source Han Sans CN-Medium, Source Han Sans CN;
						font-weight: 500;
						color: #00E0DB;
					}

				}
			}
		}

	}



	@keyframes warn {
		0% {
			transform: scale(0);
			opacity: 0.0;
		}

		25% {
			transform: scale(0.4);
			opacity: 0.45;
		}

		50% {
			transform: scale(0.8);
			opacity: 0.65;
		}

		75% {
			transform: scale(1.2);
			opacity: 0.85;
		}

		100% {
			transform: scale(1.5);
			opacity: 0.0;
		}
	}
	.fillWidth {
	        width: 100%;
	        min-height: 100%;
	        object-fit: fill;
	        position: absolute;
	        top: 0;
	        left: 0;
			bottom:0;
	        z-index: -99;
	    }

</style>