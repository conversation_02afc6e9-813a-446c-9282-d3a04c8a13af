<template>
    <div class="innerMap">
        <div id="proMap"></div>
        <div class="shrink">
            <img src="@/assets/img/triangle.png" alt="" class="triangle" v-if="flag" @click="toggleImg" />
            <img v-else src="@/assets/img/triangleLong.png" alt="" class="triangleLong" />
            <div v-if="!flag" class="inputInfo">
                <el-input
                    placeholder="企业名称"
                    @input="changeCompanyName"
                    maxlength="20"
                    v-model="searchFrom.companyName" clearable>
                </el-input>
                <el-button type="primary" size="mini" @click="search">搜索</el-button>
                <el-button size="mini" class="resetBtn" @click="reset">重置</el-button>
                <i class="el-icon-close closeBtn" @click="closeQuery"></i>
            </div>
             <div v-if="flag2" class="opts">
                <ul class="opts-list">
                    <li
                        :title="item.companyName"
                        :key="index"
                        v-for="(item, index) in searchOptionList"
                        class="opts-item"
                        @click="checkedInfo(item)">
                        <span class="icon-dot"></span>
                        {{ item.companyName }}
                    </li>
                </ul>
            </div>
        </div>
        <!-- 地图右下角 -->
        <ul class="map-right">
            <li class="map-select-item">
                <i class="icon-pillar"></i>
                支柱产业：{{ typeNumObj.pillarNum }}
                <el-switch v-model="pillarSelect" active-color="#13ce66" inactive-color="#566278" ></el-switch>
            </li>
            <li class="map-select-item">
                <i class="icon-rising"></i>
                新兴产业：{{ typeNumObj.risingNum }}
                <el-switch v-model="risingSelect" active-color="#13ce66" inactive-color="#566278" ></el-switch>
            </li>
            <li class="map-select-item">
                <i class="icon-tradition"></i>
                传统产业：{{ typeNumObj.traditionNum }}
                <el-switch v-model="traditionSelect" active-color="#13ce66" inactive-color="#566278" ></el-switch>
            </li>
            <li class="map-select-item">
                <i class="icon-stable"></i>
                稳定产业：{{ typeNumObj.stableNum }}
                <el-switch v-model="stableSelect" active-color="#13ce66" inactive-color="#566278" ></el-switch>
            </li>
        </ul>

    </div>
</template>

<script>
// import { getCompanyMapCountApi, selectCompanyMapApi } from "@/api/home.js"
// import iconPillar from  '@/assets/img/icon-pillar.png';
// import iconRising from  '@/assets/img/icon-rising.png';
// import iconTradition from '@/assets/img/icon-tradition.png';
// import iconStable from '@/assets/img/icon-stable.png';

export default {
    data() {
        return {
            map: {},
            zoom: 9,
            flag: true,
            flag2: false,
            searchFrom: {
                companyName: '',
            },
            pillarSelect: true,
            risingSelect: true,
            traditionSelect: true,
            stableSelect: true,
            mapList: [],
            searchOptionList: [],
            typeNumObj: {
                pillarNum: 0,
                risingNum: 0,
                traditionNum: 0,
                stableNum: 0
            },
            pillarList: [],
            risingList: [],
            traditionList: [],
            stableList: [],
        }
    },
    watch: {
        pillarSelect(val) {
            if(!val) {
                this.pillarList = [];
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            } else {
                this.pillarList = this.mapList.filter(item => item.typeNames.includes('支柱产业'));
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            }
        },
        risingSelect(val) {
            if(!val) {
                this.risingList = [];
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            } else {
                this.risingList = this.mapList.filter(item => item.typeNames.includes('新兴产业'));
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            }
        },
        traditionSelect(val) {
            if(!val) {
                this.traditionList = [];
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            } else {
                this.traditionList = this.mapList.filter(item=> item.typeNames.includes('传统产业'));
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            }
        },
        stableSelect(val) {
            if(!val) {
                this.stableList = [];
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                    this.loadMap(allList);
                });
            } else {
                this.stableList = this.mapList.filter(item => item.typeNames.includes('稳定产业'));
                let allList = [...this.pillarList, ...this.risingList, ...this.traditionList, ...this.stableList];
                this.$nextTick(function() {
                   this.loadMap(allList);
                });
            }
        }
    },
    created() {
        this.getMapList();
        this.getCompanyMapCount();
    },
    methods: {
        // 搜索框切换
        toggleImg() {
            this.flag = false;
        },
        search() {
            this.getMapList();
        },
        reset() {
            this.searchFrom.companyName = '';
            this.map.clearOverLays();
            this.getMapList();
        },
        changeCompanyName(val) {
            let list = [];
            if(val) {
                this.flag2 = true;
                this.mapList.forEach( item => {
                    if( item.companyName.indexOf(val) != -1 ) {
                        list.push(item);
                    }
                })
            } else {
                this.flag2 = false;
            }
            this.searchOptionList = list;
        },
         // 选中项目信息
        checkedInfo(item) {
            this.searchFrom.companyName = item.companyName;
            this.flag2 = false;
        },
        // 关闭搜索
        closeQuery() {
            this.flag = true;
        },
        // 天地图地理编码
        getCoder(address) {
            return new Promise((resolve)=> {
                let url = `http://api.tianditu.gov.cn/geocoder?ds={"keyWord": "${address}"}&tk=958aa355f2fe94c1817c458ba57da90d`;
                this.$axios.get(url).then(res => {
                    let lonLatObj = {};
                    if (res.data.status === '0') {
                        lonLatObj = {
                            lon: res.data.location.lon,
                            lat: res.data.location.lat,
                        }
                    }
                    resolve(lonLatObj);
                })
            });
        },
        // 天地图-默认查所有
        getMapList() {
            let params = {
                companyName: this.searchFrom.companyName
            }
            selectCompanyMapApi(params).then(res => {
                this.mapList = res.data.data;
                this.pillarList = this.mapList.filter(item => item.typeNames.includes('支柱产业'));
                this.risingList = this.mapList.filter(item => item.typeNames.includes('新兴产业'));
                this.traditionList = this.mapList.filter(item => item.typeNames.includes('传统产业'));
                this.stableList = this.mapList.filter(item => item.typeNames.includes('稳定产业'));
                this.$nextTick(function() {
                    this.loadMap(this.mapList);
                });
            });
        },

        // 天地图-右下方数量查询
        getCompanyMapCount() {
            getCompanyMapCountApi().then(res => {
                this.typeNumObj = res.data.data;
                for( let key in this.typeNumObj ) {
                    if(key === '支柱产业') {
                        this.typeNumObj.pillarNum = this.typeNumObj[key];
                    } else if (key === '新兴产业') {
                        this.typeNumObj.risingNum = this.typeNumObj[key];
                    } else if (key === '传统产业') {
                        this.typeNumObj.traditionNum = this.typeNumObj[key];
                    } else if (key === '稳定产业') {
                        this.typeNumObj.stableNum = this.typeNumObj[key];
                    }
                }
            });
        },
        // 天地图加载
        loadMap(mapList) {
            // 通过Gw这个属性判断地图是否存在
            if(this.map.Gw) {
                // 清除标注
                this.map.clearOverLays();
                this.map.removeStyle();
            }
            // 初始化地图对象
            this.map = new T.Map("proMap");
            // 设置显示地图的中心点和级别
            this.map.centerAndZoom(new T.LngLat(119.82654, 32.23699), this.zoom);
            this.map.enableDoubleClickZoom();
            this.map.setStyle('indigo');
            this.map.enableInertia();
            // 加载标注
            if(mapList && mapList.length > 0) {
                for(var i = 0; i < mapList.length; i++) {
                    var itemLonLatObj = {};
                    let _this = this;
                    // for循环，闭包问题解决
                    (function(j) {
                        var item = mapList[j];
                        _this.getCoder(item.address).then(res => {
                            itemLonLatObj = res;
                            var icon1 = new T.Icon({
                                iconUrl: iconPillar,
                                iconSize: new T.Point(24, 24),
                            });
                            var icon2 = new T.Icon({
                                iconUrl: iconRising,
                                iconSize: new T.Point(24, 24),
                            });
                            var icon3 = new T.Icon({
                                iconUrl: iconTradition,
                                iconSize: new T.Point(24, 24),
                            });
                            var icon4 = new T.Icon({
                                iconUrl: iconStable,
                                iconSize: new T.Point(24, 24),
                            });
                            // 根据类型判断不同的标注
                            if(item.typeNames.includes('支柱产业')) {
                                var marker = new T.Marker(new T.LngLat( itemLonLatObj.lon, itemLonLatObj.lat ), {icon: icon1});
                            } else if (item.typeNames.includes('新兴产业')) {
                                var marker = new T.Marker(new T.LngLat( itemLonLatObj.lon, itemLonLatObj.lat ), {icon: icon2});
                            } else if (item.typeNames.includes('传统产业')) {
                                var marker = new T.Marker(new T.LngLat( itemLonLatObj.lon, itemLonLatObj.lat ), {icon: icon3});
                            } else if (item.typeNames.includes('稳定产业')) {
                                var marker = new T.Marker(new T.LngLat( itemLonLatObj.lon, itemLonLatObj.lat ), {icon: icon4});
                            };
                            let content = `
                                <div>
                                    <div style="margin-bottom: 14px;">
                                        <p
                                            style="font-size: 16px;
                                            margin-right: 10px;">${ item.companyName }
                                        </p>
                                        <p
                                            style="font-size: 12px;
                                            padding: 2px 5px;
                                            background: rgba(76, 122, 246, 0.2);
                                            border: 1px solid #4C7AF6;
                                            border-radius: 3px;"
                                        >${ item.typeNames.join(',') }</p>
                                    </div>
                                    <p>
                                        <span style="color: #999;">行业：</span>${ item.companyIndustry }
                                        <span
                                            style="color: #999;
                                            margin-left: 10px;">
                                            税收：<span style="color: #FFC702;">${ item.shui ? item.shui : 0}</span>
                                            万元
                                        </span>
                                    </p>
                                    <p><span style="color: #999;">类型：</span>${ item.gongshangqiyeleixing }</p>
                                    <p><span style="color: #999;">地址：</span>${ item.address }</p>
                                </div>
                            `;
                            _this.map.addOverLay(marker);
                            addClickHandler(content, marker);
                            function addClickHandler(content, marker) {
                                marker.addEventListener("click", function(e) {
                                    openInfo(content, e)
                                });
                            };
                            function openInfo(content, e) {
                                var point = e.lnglat;
                                marker = new T.Marker(point);
                                var markerInfoWin = new T.InfoWindow(content, {offset: new T.Point(0, -10)});
                                _this.map.openInfoWindow(markerInfoWin, point);
                            };
                        });
                    })(i);
                }
            } else {

            }
        },
        onClose() {
            this.map.removeOverLay(customerWinInfo);
        },
    }
}
</script>

<style lang="scss" scoped>
.innerMap {
    height: 780px;
    position: absolute;
    top: 124px;
    left: 27%;
    border: 1px solid #182E59;
    overflow: hidden;
    #proMap {
        width: 100%;
        height: 100%;
    }
    .bg {
        background-color: rgb(65, 10, 144);
    }
    .shrink {
        position: absolute;
        top: 0;
        right: 0;
        color: #fff;
        z-index: 999;
        height: 46px;
        line-height: 46px;
        .triangle {
            height: 100%;
            float: right;
            cursor: pointer;
        }
        .triangleLong {
            width: 460px;
            height: 50px;
        }
        .inputInfo {
            width: 100%;
            position: absolute;
            top: 0;
            left: 35px;
            height: 50px;
            display: flex;
            align-items: center;
            .el-input {
                width: 260px;
                margin-right: 10px;
            }
            .area-ipt {
                width: 120px;
                margin-right: 0;
            }
        }
        .opts {
            width: 162px;
            background: rgba(13,89,141,.8);
            border-radius: 3px;
            position: absolute;
            top: 46px;
            right: 0;
            overflow: hidden;
            overflow-y: auto;
            color: #fff;
            font-size: 14px;
            .opts-list {
                padding: 10px;
                color: #B1CFE8;
                line-height: 30px;
                cursor: pointer;
            }
            .opts-item {
                width: 130px;
                color: #B1CFE8;
                height: 30px;
                line-height: 30px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                display: inline-block;
                vertical-align: bottom;
                .icon-dot {
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    background: #B1CFE8;
                    border-radius: 4px;
                    margin-right: 5px;
                }
                &:hover {
                    color: #44CDDB;
                }
            }
        }
        /deep/.el-input__inner {
            height: 28px;
            line-height: 28px;
        }
        .resetBtn {
            color: #fff;
            background: #079CA7;
            border-color: #079CA7;
        }
        .closeBtn {
            font-size: 20px;
            margin-left: 6px;
            cursor: pointer;
        }
    }
    .map-right {
        position: absolute;
        right: 20px;
        bottom: 10px;
        z-index: 1000;
        font-size: 14px;
        color: #fff;
        .map-select-item {
            height: 32px;
            display: flex;
            align-items: center;
            [class^="icon-"] {
                display: inline-block;
                width: 12px;
                height: 12px;
                background: #1F5CFF;
                border-radius: 6px;
                margin-right: 6px;
                &.icon-rising {
                    background: #039BFE;
                }
                &.icon-tradition {
                    background: #55F6FC;
                }
                &.icon-stable {
                    background: #A0EF91;
                }
            }
            /deep/ .el-switch__core {
                width: 38px !important;
                margin-left: 10px;
            }
        }
    }
    /deep/ .tdt-infowindow-content-wrapper {
        width: 464px;
        height: 264px;
        box-sizing: border-box;
        box-shadow: none;
        color: #fff;
        padding: 30px;
        // background: url("../../../../assets/img/map-window-bg-mid.png") left top no-repeat;
        background-size: 100% 100%;
    }
    /deep/ .tdt-infowindow-content p {
        font-size: 12px;
        line-height: 18px;
        margin: 0 0 10px 0;
    }
    /deep/ .tdt-infowindow-tip-container {
        background-position: center;
        background-size: cover;
        height: 34px;
        // background-image: url('../../../../assets/img/dingwei.png');
        .tdt-infowindow-tip {
            display: none;
        }
    }
}
</style>