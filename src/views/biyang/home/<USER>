<template>
	<div>
		<div id="mapDiv"></div>
		<!-- <div class="header_box">
			<headers></headers>
		</div> -->
		<div class="left_box">
			<div class="card f5">
				<card-head titleIcon='title1' :iconVisible='true' :decorationVisible='true'></card-head>
				<div class="total f1">
					<div class='total-all f2'>
						<span class='total-label'>养殖企业(家)</span>
						<div class='total-nums'>
							<span class='total-num' v-for="(item,index) in livestockEnterpriseNum.split('')">{{item}}</span>
							<!-- <span class='total-num'>2</span> -->
						</div>
					</div>

					<ul class="total-list f3">
						<li class="circle">
							<span>牛只存栏量：</span>
							<span class='count'>{{livestockNum}} </span>
							<span class='unit'> 头</span>
							<img class='watchIcon' @click='goVideoList' style="cursor: pointer;"
								src="@/assets/icons/svg/biyang/watch1.svg" alt="">
						</li>
						<li class="circle" style="display: none;">
							<span>养殖棚舍：</span>
							<span class='count'>20163 </span>
							<span class='unit'> 个</span>
							
						</li>
					</ul>

				</div>

			</div>
			<div class="card f6">
				<card-head titleIcon='title2'></card-head>
				<div id="barChart" class='f1'></div>
			</div>
			<div class="card f6">
				<card-head titleIcon='title3'>
					<div class="dorp">
						<el-dropdown trigger="click"  @command="handleCommand1">
							<span class="el-dropdown-link">
								{{activeName1}}<i class="el-icon-caret-bottom el-icon--right"></i>
							</span>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item :command="{type:3,name:'近3月'}">近3月</el-dropdown-item>
								<el-dropdown-item :command="{type:6,name:'近6月'}">近6月</el-dropdown-item>
								<el-dropdown-item :command="{type:12,name:'本年度'}">本年度</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</card-head>
				<div class="total">
					<ul class="total-list">
						<li style="margin-top: 0.39rem;">
							<span>累计出栏(头)：</span>
							<span class='count theme'>{{totalOutCount}} </span>

						</li>
						<li>
							<span>累计入栏(头)：</span>
							<span class='count theme'>{{totalInCount}} </span>

						</li>
					</ul>

				</div>
				<div id="doubleLineChart" class='f1'></div>
			</div>
		</div>
		<div class="bottom_box">
			<div class="card f3">
				<card-head titleIcon='title4' :iconVisible='true' :decorationVisible='true'></card-head>
				<div class="total f1 jc-around">
					<div class='total-all  jc-around ai-start flex-column'>
						<span class='total-label'>屠宰收购累计交易金额({{unitName}})</span>
						<div class='total-nums' style="margin-top: 0.16rem;">
							<span class='total-num' v-for="(item,index) in String(totalTransAmount).split('')">{{item}}</span>
							<!-- <span class='total-num'>2</span> -->
						</div>
					</div>

					<ul class="total-list ">
						<li class="circle">
							<span>生产加工企业：</span>
							<span class='count'>{{companyNum}}</span>
							<span class='unit'> 家</span>
						</li>
					</ul>

				</div>

			</div>
			<div class="card f5" style="margin-right: 1.15rem;">
				<card-head titleIcon='title5'>
					<div class="dorp">
						<el-dropdown trigger="click"  @command="handleCommand2">
							<span class="el-dropdown-link">
								{{activeName2}}<i class="el-icon-caret-bottom el-icon--right"></i>
							</span>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item :command="{type:7,name:'近7日'}">近7日</el-dropdown-item>
								<el-dropdown-item :command="{type:30,name:'近30日'}">近30日</el-dropdown-item>
								<el-dropdown-item :command="{type:3,name:'近3月'}">近3月</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
						
					</div>
				</card-head>
				<div id="lineChart" class='f1'></div>

			</div>
			<div class="card f5">
				<card-head titleIcon='title6'>
					<div class="dorp">
						<el-dropdown trigger="click"  @command="handleCommand3">
							<span class="el-dropdown-link">
								{{activeName3}}<i class="el-icon-caret-bottom el-icon--right"></i>
							</span>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item :command="{type:7,name:'近7日'}">近7日</el-dropdown-item>
								<el-dropdown-item :command="{type:30,name:'近30日'}">近30日</el-dropdown-item>
								<el-dropdown-item :command="{type:3,name:'近3月'}">近3月</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</card-head>
				<div class="total">
					<ul class="total-list">
						<li style="margin:0;margin-top: 0.2rem;">
							<span>累计入场牛只数量(头)：</span>
							<span class='count theme'>{{totalbutcherNum}} </span>
						</li>

					</ul>

				</div>
				<div id="lineBarChart" class='f1'></div>
			</div>
		</div>
		<div class="lines">
			<div class="line line-x" style="top:1.35rem;left:0.23rem"></div>
			<div class="line line-y" style="top:1.35rem;left:0.23rem"></div>
			<div class="line line-x" style="top:1.35rem;right:0.23rem"></div>
			<div class="line line-y" style="top:1.35rem;right:0.23rem"></div>

			<div class="line line-x" style="bottom:0.24rem;left:0.23rem"></div>
			<div class="line line-y" style="bottom:0.24rem;left:0.23rem"></div>
			<div class="line line-x" style="bottom:0.24rem;right:0.23rem"></div>
			<div class="line line-y" style="bottom:0.24rem;right:0.23rem"></div>
		</div>
		<dialog-video :dialogVisible='dialogVisible' :videoList='videoList'></dialog-video>
		<video-list :videoListVisible='videoListVisible'></video-list>
	</div>
</template>

<script>
	import {
		createVNode
	} from 'vue';
	import cardHead from '../components/card-head.vue'
	import dialogVideo from '../components/dialog-video.vue'
	import videoList from '../components/video-list.vue'
	import icons from './icon.json'
	import {
		livestockCount,
		livestockCate,
		livestockCountOutAndIn,
		enterpriseList,
		
		singleCount,
		trendOfPrice,
		trendOfButcher
		
	} from "@/api/biyang/home";
	import { startEndDate } from "@/utils/mixin/startEndDate.js";
	import {

		userDeviceList,

	
	} from "@/api/biyang/breed";
	export default {
		mixins:[startEndDate],
		components: {
			dialogVideo,
			videoList,
			cardHead
		},
		data() {
			return {
				dialogVisible: false,
				videoListVisible: false,
				totalInCount:0,//总入栏
				totalOutCount:0,//总出栏
				activeName1:'本年度',
				activeName2:'近7日',
				activeName3:'近7日',
				livestockEnterpriseNum:'0',//养殖户总数
				livestockNum:0,//牛只存栏数
				
				companyNum:0,//生产加工企业数
				totalTransAmount:0,//屠宰总额
				totalbutcherNum:0,//累计入场牛只数量
				unitName:'',//屠宰收购累计交易金额单位
				videoList:[]
			};
		},

		created() {

		},
		mounted() {
			window.goVideo = this.goVideo;
			this.initMap()
			// polygon1.addEventListener("mouseout", (i) => {
			// 	console.log('1被点击了')
			// 	polygon1.closeInfoWindow()
			// });




			
			this.getEnterpriseList()
			this.getLivestockCount()
			this.getLivestockCate()
			this.chart = this.$echarts.init(document.getElementById('doubleLineChart'));
			this.getLivestockCountOutAndIn(12)
			
			this.getSingleCount()
			this.handleCommand2({name:'近7日',type:7})
			this.handleCommand3({name:'近7日',type:7})
		},
		methods: {
			initMap(){
				var map;
				let companyList=[
					{	
						userId:'10959',
						companyType:1, //1：养殖  2：屠宰
						companyName:'1xxx公司',
						cityName:'1xx市',
						countyName:'1xx区',
						detailAddress:'天谷八路1号',
						userName:'张三',
						nickName:'张三1',
						phoneNumber:'15517xxxxxx',
						// coordinateCenter:{x:113.314070,y:32.732100},
						// coordinateList:[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}]
					},
					{
						userId:'10959',
						companyType:2, //1：养殖  2：屠宰
						companyName:'2xxx公司',
						cityName:'2xx市',
						countyName:'2xx区',
						detailAddress:'天谷八路2号',
						userName:'张三',
						nickName:'张三2',
						phoneNumber:'15517xxxxxx',
						// coordinateCenter:{x:113.314070,y:32.732100},
						// coordinateList:[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}]
					},
					{
						userId:'10959',
						companyType:1, //1：养殖  2：屠宰
						companyName:'3xxx公司',
						cityName:'3xx市',
						countyName:'3xx区',
						detailAddress:'天谷八路3号',
						userName:'张三',
						nickName:'张三3',
						phoneNumber:'15517xxxxxx',
						// coordinateCenter:{x:113.314070,y:32.732100},
						// coordinateList:[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}]
					},
					{
						userId:'10959',
						companyType:2, //1：养殖  2：屠宰
						companyName:'4xxx公司',
						cityName:'4xx市',
						countyName:'4xx区',
						detailAddress:'天谷八路10号',
						userName:'张三',
						nickName:'张三4',
						phoneNumber:'15517xxxxxx',
						// coordinateCenter:{x:113.314070,y:32.732100},
						// coordinateList:[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}]
					},
					{
						userId:'10959',
						companyType:1, //1：养殖  2：屠宰
						companyName:'5xxx公司',
						cityName:'5xx市',
						countyName:'5xx区',
						detailAddress:'天谷八路5号',
						userName:'张三',
						nickName:'张三5',
						phoneNumber:'15517xxxxxx',
						// coordinateCenter:{x:113.314070,y:32.732100},
						// coordinateList:[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}]
					},
				]
				//初始化地图对象
				map = new T.Map("mapDiv", {
					projection: 'EPSG:900913'
				});
				//设置显示地图的中心点和级别
				map.centerAndZoom(new T.LngLat(113.312840, 32.726480), 14);
				
				map.setMapType(TMAP_HYBRID_MAP);
				let coordinateCenterList=[
					{x:113.314070,y:32.732100},
					{x:113.316200,y:32.724480},
					{x:113.326880,y:32.733130},
					{x:113.330580,y:32.722270},
					{x:113.307980,y:32.722770},
				]
				let coordinateList=[
					[{x:113.313590,y:32.732810},{x:113.314650,y:32.732770},{x:113.314560,y:32.731250},{x:113.313650,y:32.731260}],
					[{x:113.315820,y:32.724920},{x:113.316580,y:32.725090},{x:113.316690,y:32.723960},{x:113.315750,y:32.723860},{x:113.315690,y:32.724560}],
					[{x:113.326550,y:32.733410},{x:113.327170,y:32.733350},{x:113.327190,y:32.732860},{x:113.326570,y:32.732900}],
					[{x:113.329810,y:32.722880},{x:113.330980,y:32.722970},{x:113.331320,y:32.721850},{x:113.330890,y:32.721570},{x:113.330170,y:32.721450},{x:113.329980,y:32.722220}],
					[{x:113.307500,y:32.723450},{x:113.308790,y:32.723520},{x:113.308600,y:32.722030},{x:113.307320,y:32.722580}]
				]
				// 创建中心点icon
				var icon1 = new T.Icon({
					iconUrl: icons.mark1Icon,
					iconSize: new T.Point(35, 35),
					iconAnchor: new T.Point(15, 25)
				});
				var icon2 = new T.Icon({
					iconUrl: icons.mark2Icon,
					iconSize: new T.Point(35,35),
					iconAnchor: new T.Point(15, 25)
				});
				companyList.forEach((item,index)=>{
					var points = [];
					coordinateList[index].forEach(e=>{
						points.push(new T.LngLat(e.x,e.y));
						
					})
					//创建面对象
					var polygon = new T.Polygon(points, {
						color: item.companyType==1?"#00E0DB":"#FFF200",
						weight: 2,
						opacity: 1,
						fillColor: item.companyType==1?"#00E0DB":"#FFF200",
						fillOpacity: 0.28
					});
					//向地图上添加面
					map.addOverLay(polygon);
					// 向地图上添加中心点icon
					var marker = new T.Marker(new T.LngLat(coordinateCenterList[index].x,coordinateCenterList[index].y), {icon: item.companyType==1?icon1:icon2});
					map.addOverLay(marker);
					// 创建弹窗
					var infoWin1 = new T.InfoWindow();
					console.log(icons)
					var sContent = `
						<div class="dialog">
							<div class="dialog-title flex jc-between">
								<h5>${item.companyName}</h5>
								${
									(()=>{
										if(item.companyType == 1){ 
											return `<img style="cursor: pointer;" src="${icons.watchIcon}" alt="" onClick="goVideo('${item.userId}')">`;
										}else{
											return '';
										}
									})()
								}

								
							</div>
							<ul>
								<li class="flex ">
									<img src="${icons.userIcon}" alt="">
									<span class='dialog-value'>${item.userName}</span>
								</li>
								<li class="flex ">
									<img src="${icons.telIcon}" alt="">
									<span class='dialog-value'>${item.phoneNumber}</span>
								</li>
								<li class="flex ">
									<img src="${icons.locationIcon}" alt="">
									<span class='dialog-value'>${item.cityName+item.countyName+item.detailAddress}</span>
								</li>
							</ul>
						</div>
					`
					
					marker.addEventListener("click", (i) => {
						console.log('1被点击了')
						var markerInfoWin = new T.InfoWindow(sContent, {
							offset: new T.Point(0, -10)
						});
						polygon.openInfoWindow(markerInfoWin);
					});
					polygon.addEventListener("click", (i) => {
						console.log('1被点击了')
						var markerInfoWin = new T.InfoWindow(sContent, {
							offset: new T.Point(0, -10)
						});
						polygon.openInfoWindow(markerInfoWin);
					});
					
					if(item.companyType==1){
						// 养殖
						
					}else{
						//屠宰
						
					}
				})
				
				return
				coordinateList.forEach(coordinate=>{
					var points = [];
					coordinate.forEach(item=>{
						points.push(new T.LngLat(item.x,item.y));
					})
					//创建面对象
					var polygon = new T.Polygon(points, {
						color: "#00E0DB",
						weight: 2,
						opacity: 1,
						fillColor: "#00E0DB",
						fillOpacity: 0.28
					});
					//向地图上添加面
					map.addOverLay(polygon);
				})
				
				
				
				
				
				var points1 = [];
				
				
				
				points1.push(new T.LngLat(113.324146, 32.726003));
				points1.push(new T.LngLat(113.324173, 32.725938));
				points1.push(new T.LngLat(113.324128, 32.72587));
				points1.push(new T.LngLat(113.324083, 32.725684));
				points1.push(new T.LngLat(113.324047, 32.725653));
				points1.push(new T.LngLat(113.323553, 32.725703));
				points1.push(new T.LngLat(113.32354, 32.725733));
				points1.push(new T.LngLat(113.323553, 32.726018));
				points1.push(new T.LngLat(113.323589, 32.726056));
				//创建面对象
				var polygon1 = new T.Polygon(points1, {
					color: "#FFF200",
					weight: 2,
					opacity: 0.9,
					fillColor: "#FFF200",
					fillOpacity: 0.28
				});
				var infoWin1 = new T.InfoWindow();
				console.log(icons)
				var sContent = `
					<div class="dialog">
						<div class="dialog-title flex jc-between">
							<h5>额尔敦屠宰加工企业</h5>
							<img style="cursor: pointer;" src="${icons.watchIcon}" alt="" onClick="goVideo('${1}')">
						</div>
						<ul>
							<li class="flex ">
								<img src="${icons.telIcon}" alt="">
								<span class='dialog-value'>15517152150</span>
							</li>
							<li class="flex ">
								<img src="${icons.telIcon}" alt="">
								<span class='dialog-value'>内蒙古呼和浩特市新城区爱民街46号啊大大撒旦撒啊大大大大大</span>
							</li>
						</ul>
					</div>
				`
				map.addOverLay(polygon1);
				
				
				
				
				// polygon.addEventListener("click", (i) => {
				// 	console.log(createVNode)
				
				
				// 	console.log('被点击了')
				
				// });
				polygon1.addEventListener("click", (i) => {
					console.log('1被点击了')
					var markerInfoWin = new T.InfoWindow(sContent, {
						offset: new T.Point(0, -10)
					});
					polygon1.openInfoWindow(markerInfoWin);
				});
			},
			getSingleCount(){
				singleCount({"materialsType": 2}).then(res=>{
					if(res.code==200){
						this.companyNum=res.result.companyNum
						this.totalTransAmount=res.result.totalTransAmount
						this.unitName=res.result.unitName
						console.log(res)
					}
					
				})
			},
			getEnterpriseList(){
				enterpriseList({"countyId":'367795244651909126'}).then(res=>{
					if(res.code==200){
						console.log(res)

					}
				})
			},
			getLivestockCount(){
				livestockCount({"countyId":'367795244651909126'}).then(res=>{
					if(res.code==200){
						console.log(res)

						this.livestockEnterpriseNum=String(res.result.enterpriseNum)
						this.livestockNum=res.result.livestockNum
					}
				})
			},
			handleCommand1(data){
				console.log(data)
				this.activeName1=data.name
				this.getLivestockCountOutAndIn(data.type)
			},
			handleCommand2(data){
				this.activeName2=data.name
				this.getStartData(data.type)
				trendOfPrice({"materialsType":2,"startTime":this.startData,"endTime": this.endDate}).then(res=>{
					console.log(res)
					if(res.code==200){
						let xAxis=[]
						let yAxis=[]
						res.result.forEach(item=>{
							xAxis.push(item.xaxis)
							yAxis.push(Number(item.yaxisLeft))
						})
						
						this.initLineChart({
							dom: 'lineChart',
							xAxis: xAxis,
							data: yAxis,
							title: '价格：元/kg',
							type: true
						})
					}
					
				})
			},
			handleCommand3(data){
				this.activeName3=data.name
				this.getStartData(data.type)
				trendOfButcher({"materialsType": 2,"startTime":this.startData,"endTime": this.endDate}).then(res=>{
					if(res.code==200){
						let xAxis=[]
						let data=[]
						let data1=[]
						res.result.trendList.forEach(item=>{
							xAxis.push(item.xaxis)
							data.push(Number(item.yaxisLeft))
							data1.push(Number(item.yaxisRight))
						})
						this.totalbutcherNum=res.result.butcherNum
						this.initLineBarChart({
								dom: 'lineBarChart',
								xAxis: xAxis,
								titles: ['交易金额', '屠宰量', ],
								units: ['元', '头'],
								data: data,
								data1: data1
							}
						
						)
					}
					
					
					console.log(res)
				})
			},

			getLivestockCountOutAndIn(type){
				livestockCountOutAndIn({type:type,"countyId":'367795244651909126'}).then(res=>{
					if(res.code==200){
						console.log(res)
						if(!res.result){
							return
						}
						this.totalInCount=res.result.inNumber
						this.totalOutCount=res.result.outNumber
						
						let times=[]
						let data=[]
						let data1=[]
						res.result.xmbLivestockOutAndInModelList.forEach(item => {
							
							times.push(item.month.split('-')[1]+'月')
							data.push(item.inNumber)
							data1.push(item.outNumber)
						})
						this.lineChart1({
							dom: 'doubleLineChart',
							xAxis:times,
							data: data,
							data1:data1
						})
					}
				})
			},
			getLivestockCate() {
				livestockCate({"countyId":'367795244651909126'}).then(res => {
					console.log(res)
					if (res.code == 200) {
						let data = []
						let cate = []
						res.result.forEach(item => {
							data.push(item.num)
							cate.push(item.categoryName)
						})
						this.initColumnChart({
							dom: 'barChart',
							data: data,
							cate: cate
						})
					}

				})
			},
			goVideoList() {
				this.videoListVisible = true
			},
			goVideo(userId) {
				userDeviceList({userId:userId}).then(res=>{
					if(res.code==200){
						this.dialogVisible = true
						if(res.result&&res.result.length>0){
							
							let deviceInfo=res.result[0]
							
							this.videoList=res.result
							getPlayUrl({
								deviceCode: deviceInfo.deviceCode,
								channelCode: deviceInfo.channelCode,
								ezvizId: deviceInfo.ezvizId
								
							}).then(res=>{
								if(res.code==200){
									this.$nextTick(function() {
										let w=document.getElementById('video').offsetWidth
										let h=document.getElementById('video').clientHeight 
									    this.player = new EZUIKit.EZUIKitPlayer({
									    	id: 'video',
									    	url: res.result.url,
									    	accessToken: res.result.accessToken,
									    	template: "pcLive",
											width: w,
											height: h,
											showPTZControl: 'none'
									    })
					
									});
									
									
							
								}
								console.log(res)
							})
						}
					}
				})
				
				
				
			},
			// 活畜交易分布
			initLineBarChart(data) {
				// var a=document.ge
				console.log(data);
				var myChart = this.$echarts.init(document.getElementById(data.dom));
				var option = {
					legend: {
						top: '10%',
						// // left: '3%',
						right: '15%',
						// bottom: '60%',
						// show: true,
						itemWidth: 12,
						itemHeight: 12,
						data: [

							{
								name: data.titles[1],
								textStyle: {
									color: '#0EDAE2'
								},
								// bottom: 10
							},
							{
								name: data.titles[0],
								textStyle: {
									color: ' #DCE000'
								},
							},
						]
					},
					grid: {
						left: '8%',
						right: '5%',
						bottom: '3%',
						top: '28%',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						show: true,
						axisLine: {
							lineStyle: {
								color: 'rgba(0,254,255,0.1)', //x轴线条颜色宽度
								width: 1
							}
						},
						axisLabel: {
							color: '#00E0DB'
						}, //x轴刻度文字颜色
						scale: false,
						axisTick: {
							show: false
						},
						data: data.xAxis

					},
					yAxis: [{
							type: 'value',
							name: data.titles[0] + '：' + data.units[0],
							axisLine: {
								lineStyle: {
									color: '#00E0DB'
								}
							},
							splitNumber: 4,
							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0.1)'
								}
							},
							offset: 10,
							axisTick: {
								show: false
							},
						},
						{
							type: 'value',
							show: true,
							name: data.titles[1] + '：' + data.units[1],
							axisLine: {
								lineStyle: {
									color: '#00E0DB'
								}
							},
							splitLine: {
								lineStyle: {
									// type: 'dashed',
									color: 'rgba(0,254,255,0)'
								}
							},
							offset: 10,
							// splitNumber: 4,
							axisTick: {
								show: false
							},
						}
					],
					series: [{
							name: data.titles[0],
							data: data.data1,
							type: 'line',
							symbol: 'circle',
							symbolSize: 5,
							yAxisIndex: 1,
							itemStyle: {
								color: '#DCE000'
							}
						},
						{
							name: data.titles[1],
							data: data.data,
							type: 'bar',
							symbol: 'circle',
							barWidth: 10,
							itemStyle: {
								opacity: 0.8,
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
										offset: 1,
										color: '#0BE5E1',
									},
									{
										offset: 0,
										color: '#06FDF5',
									},
								]),
							},
							backgroundStyle: {
								color: 'rgba(44, 118, 254, 0.2)'
							},

							// tooltip: {
							//     trigger: 'item'
							// },
						}
					],
					tooltip: { //鼠标悬浮提示数据
						trigger: 'axis',
						backgroundColor: 'rgba(0,0,0,0.8)',
						axisPointer: {
							// type: 'none'
						},
						formatter: function(params) {
							// console.log(params, 2)
							// return `<p>
							//           <span style="color: #FFF; font-size: 14px; font-weight: normal;">监管笔数${params.name
							//     }： ${params.data.account || 0} </span>

							//       </p>`;
							return `
			                <p style="font-size: 14px; font-weight: normal;">${params[0].axisValue
			                    }</p><br/>
			                <p>
			                  <span style="font-size: 14px; font-weight: normal;">屠宰量 (头)： ${params[0].value || 0} </span>
			                </p><br/> 
			                 <p>
			                  <span style="font-size: 14px; font-weight: normal;">交易金额(元)： ${params[1].value || 0} </span>
			                 </p>`;
							// return params.name
						},
						// backgroundColor: 'rgba(11, 15, 56, 1)',
						// borderColor: 'rgba(18, 36, 132, 1)',
						// borderWidth: 2,
						// textStyle: {
						//     color: '#fff',
						// },
						
						borderColor: '#292929',
						borderWidth: 1,
						textStyle: {
							color: '#fff',
						},
					}
				};

				myChart.setOption(option)
				console.log(111)
				setTimeout(() => {
					myChart.resize()
				}, 100)
				window.addEventListener('resize', () => {
					setTimeout(() => {
						myChart.resize()
					}, 100)

				})
			},
			// 初始化折线图
			initLineChart(data) {
				var chart = this.$echarts.init(document.getElementById(data.dom))
				chart.setOption({
					color: ['rgba(0,246,236,1)'],
					title: {
						text: '',
					},
					legend: {
						icon: 'rectangle',
						data: [{
							name: '',
							textStyle: {
								fontSize: 12,
								color: 'rgba(0,246,236,1)',
							},
						}, ],
						x: 'right',
						y: 'top',
						itemWidth: 15,
						itemHeight: 2,
						padding: [30, 0, 0, 0],
					},

					tooltip: {
						trigger: 'axis',
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: ' rgba(0,246,236,0.2)',
						boxShadow: 'inset 0rem 0rem 0.06rem 0rem rgba(0,117,255,0.6)',
						borderWidth: 2,
						textStyle: {
							color: '#fff',
						},
					},
					grid: {
						left: '5%',
						right: '3%',
						bottom: '3%',
						top: '20%',
						containLabel: true,
					},
					xAxis: [{
						type: 'category',
						boundaryGap: true,
						axisLabel: {
							color: '#00E0DB',
							interval: 0,
							// rotate: 40,
						},
						axisLine: {
							show: true, // X轴 网格线 颜色类型的修改
							lineStyle: {
								color: ' rgba(0,254,255,0.1)',
							},
						},
						axisTick: {
							// X轴线 刻度线
							show: false,
						},
						data: data.xAxis,
					}, ],
					yAxis: [{
						name: data.title,
						nameTextStyle: {
							color: '#00E0DB',
							padding: [0, 0, 0, 40]
						},
						splitLine: false,
						type: 'value',
						axisLine: {
							show: true, // Y轴线
							lineStyle: {
								color: ' rgba(0,254,255,0.1)',
							},
						},
						axisLabel: {
							color: '#00E0DB',
							// align: 'center', // 设置刻度居中

						},
					}, ],
					series: [{
						name: '',
						type: 'line',
						lineStyle: {
							width: 2,
						},
						showSymbol: data.type,
						areaStyle: {
							opacity: 0.8,
							color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
									offset: 1,
									color: 'rgba(91,233,255,0)',
								},
								{
									offset: 0,
									color: 'rgba(0,246,236,0.48)',
								},
							]),
						},
						data: data.data,
					}, ],
				})
				setTimeout(() => {
					chart.resize()
				}, 100)

				window.addEventListener('resize', () => {
					chart.resize()
				})
			},
			lineChart1(data) {
				
				this.chart.setOption({
					color: ['rgba(0,246,236,1)', "rgba(255,217,28,1)"],
					title: {
						text: "",
					},
					legend: {
						icon: 'rectangle',
						data: [{
							name: '入栏',
							textStyle: {
								fontSize: 12,
								color: 'rgba(0,246,236,1)'
							}
						}, {
							name: '出栏',
							textStyle: {
								fontSize: 12,
								color: 'rgba(255,217,28,1)'
							}
						}],
						x: 'right',
						y: 'top',
						itemWidth: 15,
						itemHeight: 2,
						padding: [10, 0, 0, 0]
					},
					tooltip: {
						trigger: 'axis',
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: " rgba(0,246,236,0.2)",
						boxShadow: "inset 0rem 0rem 0.06rem 0rem rgba(0,117,255,0.6)",
						borderWidth: 2,
						textStyle: {
							color: "#fff"
						},
						formatter: function(data1) {
							let dom = '';
							// console.log(data)
							data1.forEach(item => {
								console.log(item)
								let timeLine = ''
								// if (data.data.length == 3 || data.data.length == 6) {
								// 	let year = item.axisValueLabel.split('-')[0] + '年'
								// 	let month = item.axisValueLabel.split('-')[1] + '月'
								// 	timeLine = year + month
								// } else {
								// 	let month = item.axisValueLabel.split('-')[0] + '月'
								// 	let day = item.axisValueLabel.split('-')[1] + '日'
								// 	timeLine = month + day
								// }
								timeLine = item.axisValueLabel
								dom += `<p>
											${timeLine}
			                                ${item.marker}
			                                ${item.seriesName}： 
			                                ${item.value}只
			                            </p>`
							})
							return `<div>${dom}</div>`
						},
					},
					grid: {
						top: '20%',
						left: "0%",
						right: "0%",
						bottom: "0%",
						containLabel: true,
					},
					xAxis: [{
						type: "category",
						boundaryGap: true,
						axisLabel: {
							color: "#05E9F7",
							// interval: 0,
							// rotate: 45	,
						},
						axisLine: {
							show: true, // X轴 网格线 颜色类型的修改
							lineStyle: {
								color: " rgba(0,254,255,0.1)",
							},
						},
						axisTick: {
							// X轴线 刻度线
							show: false,
						},
						data: data.xAxis,
					}, ],
					yAxis: [{
						name: "数量",
						nameTextStyle: {
							color: "#05E9F7",
							padding: [0, 0, 0, 0],
						},
						splitLine: {
							show: false // 关闭分隔线
						},
						type: "value",
						axisLine: {
							show: true, // Y轴线
							lineStyle: {
								color: " rgba(0,254,255,0.1)",
							},
						},
						axisLabel: {
							color: "#05E9F7",
						},
					}],
					series: [{
							name: "入栏",
							type: "line",
							lineStyle: {
								width: 2,
							},
							showSymbol: false,
							areaStyle: {
								opacity: 0.8,
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
										offset: 1,
										color: "rgba(91,233,255,0)",
									},
									{
										offset: 0,
										color: "rgba(0,246,236,0.48)",
									},
								]),
							},
							data: data.data,
						},
						{
							name: "出栏",
							type: "line",
							lineStyle: {
								width: 2,
							},
							showSymbol: false,
							areaStyle: {
								opacity: 0.8,
								color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
										offset: 1,
										color: "rgba(255,217,28,0)",
									},
									{
										offset: 0,
										color: "rgba(255,217,28,0.48)",
									},
								]),
							},
							data: data.data1,
						},
					],
				});


			},
			// 初始化柱状图
			initColumnChart(data) {
				var chart = this.$echarts.init(document.getElementById(data.dom))
				// 隐藏动画加载效果。
				chart.setOption({
					color: this.colors,

					tooltip: {
						trigger: 'axis',
						backgroundColor: 'rgba(0,0,0,0.8);',
						borderColor: ' rgba(0,246,236,0.2)',
						boxShadow: 'inset 0rem 0rem 0.06rem 0rem rgba(0,117,255,0.6)',
						borderWidth: 2,
						textStyle: {
							color: '#fff',
						},
						formatter: function(data) {
							const name = data[0].name.split('\n')[0]
							let dom = `<div style='width: 150px'>
                              <h3 style='
                                  text-align: left;
                                  font-size: 16px;
                                  font-weight: 400;
                                  color: #FFFFFF;'>${name}</h3>
                              <div style='display: flex; flex-wrap: wrap'>
                          `
							data.forEach((item) => {
								dom += `<div style='width: 50%; display: flex;'>
                                  <p>${item.marker}&nbsp</p>
                                  <div>
                                      <p style='color: #00FEFF;font-weight: 700;'>${item.value}<span style='color: #fff;'>&nbsp 头</span></p>
                                     
                                  </div>
                              </div>`
							})
							return `<div>${dom}</div>`
						},
					},
					grid: {
						left: '1%',
						right: '3%',
						bottom: '10%',
						containLabel: true,
					},
					xAxis: [{
						type: 'category',
						textStyle: {
							lineHeight: 16,
							formatter: ['{a}', '{b}'].join('\n'),
						},
						data: data.cate,
						axisPointer: {
							type: 'line',
						},
						axisLine: {
							show: true, // Y轴线
							lineStyle: {
								color: ' rgba(0,254,255,0.1)',
							},
						},
						axisTick: {
							// X轴线 刻度线
							show: false,
						},
						axisLabel: {
							color: '#00E0DB',
							rotate: 45 // 设置旋转角度，单位为度
						},
					}, ],
					yAxis: [{
						name: '数量',

						nameTextStyle: {
							color: '#00E0DB',
							padding: [0, 0, 0, -35],
						},
						splitLine: false,
						type: 'value',
						axisLine: {
							show: true, // Y轴线
							lineStyle: {
								color: ' rgba(0,254,255,0.1)',
							},
						},
						axisLabel: {
							color: '#00E0DB',
						},
					}, ],
					series: [{
						name: '',
						type: 'bar',
						barWidth: 10,
						barGap: 0,
						data: data.data,
						emphasis: {
							focus: 'series',
						},
						label: {
							normal: {
								show: true,
								position: 'top',
								fontSize: 12,
								color: '#FFFFFF',
								offset: [0, 0],
								formatter: '\n{c}头'
							},
						},
						itemStyle: {
							//   barBorderRadius: [20, 20, 0, 0],
							color: '#00FEFF',
						},
					}, ],
				})
				window.addEventListener('resize', () => {
					chart.resize()
				})
			},

		},
	};
</script>
<style lang="scss" scoped>
	#mapDiv {
		width: 100%;
		height: 100vh;

	}

	::v-deep .tdt-infowindow-tip {
		background: #00E0DB;
	}

	::v-deep .tdt-infowindow-close-button {
		color: #00E0DB !important;
		right:0.1rem !important;
		top:0.1rem !important;
	}

	::v-deep .tdt-infowindow-content-wrapper {
		background: url(~@/assets/biyang/dialog_bg.png);
		background-size: 100% 100%;
		width: 2.87rem;
		padding:0.4rem;
		cursor: default;
		padding-left: 0.2rem;
		padding-bottom: 0.2rem;

		.tdt-infowindow-content {
			margin: 0;

			.dialog-title {

				// width: 100%;
				h5 {
					font-size: 0.2rem;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #00E0DB;
				}

				img {
					width: 0.34rem;
					height: 0.34rem;
				}
			}

			ul {
				li {
					margin: 0.15rem 0;

					img {
						width: 0.15rem;
						height: 0.15rem;
						line-height: 0.2rem;
						margin-right: 0.04rem;
						vertical-align: text-bottom;
						align-self: center;
					}

					.dialog-value {
						font-size: 0.15rem;
						font-family: Source Han Sans CN-Normal, Source Han Sans CN;
						font-weight: 350;
						color: #FFFFFF;
						height: 0.2rem;
						line-height: 0.2rem;
					}
				}
			}
		}
	}

	.tdt-infowindow-content {
		margin: 0 !important;
	}

	.header_box {
		z-index: 999;
		position: absolute;
		top: 0;
		left: 0;
		width: 24rem;
		height: 1.01rem;
	}

	.left_box {
		padding: 0.2rem;
		z-index: 999;
		position: absolute;
		bottom: 0.25rem;
		top: 1.38rem;
		left: 0.25rem;
		width: 4.96rem;
		// height: 11.88rem;
		background: rgba(0, 0, 0, 0.7);
		border-radius: 0rem 0rem 0rem 0rem;
		opacity: 1;
		display: flex;
		flex-direction: column;
	}

	.bottom_box {
		display: flex;
		padding: 0.2rem;
		z-index: 999;
		position: absolute;
		bottom: 0.25rem;
		right: 0.25rem;
		left: 5.48rem;
		height: 3.39rem;
		// width: 18.25rem;
		background: rgba(0, 0, 0, 0.7);
		border-radius: 0rem 0rem 0rem 0rem;
		opacity: 1;
	}

	.line {
		background-color: #00E0DB;
		z-index: 9999;
		position: absolute;
	}

	.line-x {
		width: 1.05rem;
		height: 0.05rem;
	}

	.line-y {
		width: 0.05rem;
		height: 1.03rem;
	}

	.card {
		display: flex;
		flex-direction: column;

		// box-sizing: border-box;
		.total {

			display: flex;
			flex-direction: column;
			justify-content: space-around;

		}

		.total-all {
			display: flex;
			align-items: center;

			.total-label {
				font-size: 0.2rem;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
				margin-right: 0.24rem;
			}

			.total-nums {
				display: flex;
			}

			.total-num {
				margin-right: 0.13rem;
				width: 0.44rem;
				height: 0.44rem;
				line-height: 0.44rem;
				text-align: center;
				background: rgba(0, 224, 219, 0.1216);
				box-shadow: inset 0rem 0rem 0.08rem 0rem rgba(0, 224, 219, 0.2);
				border-radius: 0rem 0rem 0rem 0rem;
				opacity: 1;
				border: 0.01rem solid rgba(0, 224, 219, 0.102);
				font-size: 0.33rem;
				font-family: Impact-Regular, Impact;
				font-weight: 400;
				color: #FFF200;
			}
		}

		.total-list {


			font-size: 0.18rem;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 0.18rem;
			display: flex;
			flex-direction: column;



			li {
				margin-bottom: 0.39rem;
				display: flex;
				align-items: center;

				.count {
					font-size: 0.23rem;
					font-family: Source Han Sans CN-Medium;
					font-weight: 700;
					color: #FFFFFF;
					margin: 0 0.13rem;
				}

				.unit {
					font-size: 0.2rem;
					color: rgba(250, 250, 250, 0.60);
				}
			}

			.circle::before {
				content: '';
				display: flex;
				width: 0.13rem;
				height: 0.13rem;
				background: #00E0DB;
				border-radius: 1.25rem 1.25rem 1.25rem 1.25rem;
				margin-right: 0.13rem;
			}

			.watchIcon {
				width: 0.38rem;
				height: 0.38rem;
				margin-left: 0.5rem;
			}
		}
	}
	::v-deep .tdt-control-copyright{
		display: none !important;
	}
	// #lineChart,#lineChart{
	// 	height: 2.65rem;
	// 	width:100%;
	// }
</style>