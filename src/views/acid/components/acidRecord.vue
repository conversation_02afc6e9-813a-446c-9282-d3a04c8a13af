<template>
    <div class="main">
        <div class="box-card">
            <div id="print">
                <div class="fc">
                    <span class="point_icon">排酸信息</span>
                </div>

                <el-alert
                    title="入排酸库时，如未进行检斤操作，将不计算入库总重量和排酸损耗。"
                    type="warning"
                    class="mb10"
                    show-icon>
                </el-alert>
                <el-descriptions :title='dataInfo.divisionTaskName' :column="3" size="medium" border>
                    <el-descriptions-item label='排酸开始时间：'>{{ dataInfo.acidStartTime }}</el-descriptions-item>
                    <el-descriptions-item label='排酸结束时间：'>{{ dataInfo.acidEndTime }}</el-descriptions-item>
                    <el-descriptions-item label='排酸时长：'>{{ dataInfo.acidHour }}</el-descriptions-item>
                    <el-descriptions-item label='排酸库：'>{{ dataInfo.warehouseName }}</el-descriptions-item>
                    <el-descriptions-item label='入库方式：'>称重入库</el-descriptions-item>
                    <el-descriptions-item label='关联屠宰任务：'>{{ dataInfo.butcherCode }}</el-descriptions-item>
                    <el-descriptions-item label='供应商：'>{{ dataInfo.supplierName }}</el-descriptions-item>
                    <el-descriptions-item label='类别：'>{{ supplierType[dataInfo.supplierType] }}</el-descriptions-item>
                    <el-descriptions-item label='联系人：'>{{ dataInfo.supplierContactName }}</el-descriptions-item>
                    <el-descriptions-item label='联系电话：'>{{ dataInfo.supplierContactPhone }}</el-descriptions-item>
                    <el-descriptions-item label='所属地区：'>{{ dataInfo.supplierAddress }}</el-descriptions-item>
                    <el-descriptions-item label='入库数量：'>{{ dataInfo.inWarehouseNum }}</el-descriptions-item>
                    <el-descriptions-item label=''>
                        <div slot="label" class="descriptions-label">
                            入库总重量：
                            <el-tooltip class="item" effect="dark" placement="top">
                                <div slot="content">
                                    未检斤入库时为空，当后续变更为检斤入库时，该字段将显示系统估值，<br/>
                                    计算方式=结算检斤重量-热线检斤重量 
                                </div>
                                    <img style="width: 16px;height: 16px;" src="../../../assets/images/info-circle.png" />
                            </el-tooltip>
                        </div>
                        {{ dataInfo.remark == '0' ? '-' : dataInfo.inWarehouseWeight + 'kg' }}
                    </el-descriptions-item>
                    <el-descriptions-item label='出库数量：'>{{ dataInfo.acidStatus == 4 ? dataInfo.outWarehouseNum : '-'}}</el-descriptions-item>
                    <el-descriptions-item label='出库总重量：'>{{ dataInfo.acidStatus == 4 ? dataInfo.outWarehouseWeight + 'kg' : '-' }}</el-descriptions-item>
                    <el-descriptions-item label=''>
                        <div slot="label" class="descriptions-label">
                            排酸损耗：
                            <el-tooltip class="item" effect="dark" placement="top">
                                <div slot="content">未检斤入库时为空，当后续变更为检斤入库时，该字段将根据系统估值计算 </div>
                                    <img style="width: 16px;height: 16px;" src="../../../assets/images/info-circle.png" />
                            </el-tooltip>
                        </div>
                        {{ dataInfo.remark == '0' ? '-' : dataInfo.acidStatus == 4 ? dataInfo.lossWeight + 'kg' : '-' }}
                    </el-descriptions-item>
                </el-descriptions>
                <div class="fc"  v-if="dataInfo.acidStatus == 4">
                    <span class="point_icon">出库记录</span>
                </div>
                <el-table :data="tableData" v-if="dataInfo.acidStatus == 4" border>
                    <el-table-column type="index" align="center" label="序号"></el-table-column>
                    <el-table-column prop="carcassDirection" align="center" label="胴体流向"></el-table-column>
                    <el-table-column prop="statusName" align="center" label="排酸状态"></el-table-column>
                    <el-table-column prop="updateTime" align="center" label="出库日期" sortable></el-table-column>
                    <el-table-column prop="weightNum" align="right" label="出库数量" sortable></el-table-column>
                    <el-table-column prop="netWeight" align="right" label="出库总重量（kg）" sortable></el-table-column>
                    <el-table-column prop="updateUserName" align="center" label="出库人员"></el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            tableData: [],
            supplierType:{1:'企业',3:'养殖户',2:'中间商'}
        }
    },
    components: {
    },
    props: {
        dataInfo: Object
    },
    watch: {
        dataInfo() {
            if (this.dataInfo && this.dataInfo.outWarehouseList){
                this.setMaterialsData(this.dataInfo.outWarehouseList)
            }
        }
    },
    created() {
        if (this.dataInfo && this.dataInfo.outWarehouseList){
            this.setMaterialsData(this.dataInfo.outWarehouseList)
        }
    },
    methods: {
        setMaterialsData(data) {
            if (data) {
                this.tableData = data.map((item) => {
                    item.statusName = '已出库'
                    return item
                })
            }
        },
        showModel() {
            this.$refs.creatMaterials.showModel()
        }
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-8{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
.descriptions-label{
    display: flex;
    align-items: center;
}
</style>