<template>
    <div class="main">
        <div class="box-card">
            <div id="print">
                <!-- <div slot="header" class="clearfix">
                    <span class="point_icon">操作记录</span>
                </div> -->
                <div class="fc">
                    <span class="point_icon">操作记录</span>
                </div>
                <!-- <div class="fc card-title">
                    <div class="fast"></div>
                    <span>操作记录</span>
                </div> -->
                <el-table :data="tableData" border>
                    <el-table-column type="index" align="center" label="序号"></el-table-column>
                    <el-table-column prop="operateUserName" align="center" label="操作人"></el-table-column>
                    <el-table-column prop="acidTime" align="center" label="操作时间" sortable></el-table-column>
                    <el-table-column prop="operateStatus" align="center" label="操作内容"></el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script>
import { productMaterialsCollectList } from "@/api/production/index.js"
export default {
    data() {
        return {
            tableData: [],
            dataItem: {}
        }
    },
    props: {
        dataInfo: Object
    },
    watch: {
        dataInfo() {
            if (this.dataInfo){
                this.setOperateData()
            }
        }
    },
    created() {
        if (this.dataInfo){
            this.setOperateData()
        }
    },
    components: {
    },
    methods: {
        setOperateData() {
            this.tableData.push({
                acidTime: this.dataInfo.acidStartTime,
                operateUserName: this.dataInfo.createUserName,
                operateStatus: '排酸入库'
            })
            if(this.dataInfo.acidEndTime) {
                this.tableData.push({
                    acidTime: this.dataInfo.acidEndTime,
                    operateUserName: this.dataInfo.updateUserName,
                    operateStatus: '排酸出库'
                })
            }
        },
        getList() {
            productMaterialsCollectList({
                divisionTaskCode: this.dataInfo.divisionTaskCode,
                pageNum: 1,
                pageSize: 10000,
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result.list
                } else {
                    this.$message.error(res.message)
                }
            })
        }
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-8{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
</style>