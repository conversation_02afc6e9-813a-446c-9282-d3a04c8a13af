<template>
    <div>
        <div id="print">
            <div slot="header" class="clearfix">
                <span>{{dataInfo.divisionTaskName}}</span>
            </div>
            <el-row :gutter="20" class="fcc">
                <el-col :span="20">
                    <el-descriptions class="mt20" :title='dataInfo.divisionTaskName' :column="3" size="medium" border>
                        <el-descriptions-item label='操作人：'>{{ dataInfo.createUserName }}</el-descriptions-item>
                        <el-descriptions-item label='创建时间：'>{{ dataInfo.createTime }}</el-descriptions-item>
                        <el-descriptions-item label='最后修改人：'>{{ dataInfo.updateUserName }}</el-descriptions-item>
                        <el-descriptions-item label='更新时间：'>{{ dataInfo.updateTime }}</el-descriptions-item>
                    </el-descriptions>
                </el-col>
                <el-col :span="4" class="fcc">
                    <div class="status"></div>
                    <span>{{acidStatusHash[dataInfo.acidStatus]}}</span>
                </el-col>
            </el-row>
        </div>
        <el-tabs v-model="activeName" style="margin-top: 30px">
            <el-tab-pane label="排酸信息" name="1">
            </el-tab-pane>
            <el-tab-pane label="操作记录" name="2">
            </el-tab-pane>
        </el-tabs>
        <AcidRecord v-if="activeName == 1" :dataInfo='dataInfo' @confirmTask='getInfo'></AcidRecord>
        <Operate v-if="activeName == 2" :dataInfo='dataInfo'></Operate>
    </div>
</template>

<script>
import {
    acidTaskInfo
} from "@/api/production/index.js";
import AcidRecord from './acidRecord.vue'
import Operate from './operate.vue'
export default {
    data() {
        return {
            activeName: '1',
            dataInfo: {},
            acidStatusHash: {
                '4': '已结束',
                '1': '进行中',
                '2': '进行中',
                '3': '进行中',
            }
        }
    },
    props: {
        acidTaskId: String
    },
    components: {
        AcidRecord,
        Operate
    },
    watch: {
        acidTaskId() {
            if (this.acidTaskId) {
                this.getInfo()
            }
        }
    }, 
    created() {
        if (this.acidTaskId) {
            this.getInfo()
        }
    },
    methods: {
        getInfo() {
            acidTaskInfo({
                acidTaskId: this.acidTaskId
            }).then(res => {
                if(res.code == 200) {
                    this.dataInfo = res.result
                }
            })
        },
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-7{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
</style>