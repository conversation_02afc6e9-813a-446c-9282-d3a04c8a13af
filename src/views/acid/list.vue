<template>
    <div class="app-container">
        <el-card shadow="never" class="mb10 box-card form-card">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true" class="form_box">
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item prop="butcherCode">
                            <el-input
                            v-model="queryParams.butcherCode"
                            placeholder="任务单号"
                            clearable
                            />
                        </el-form-item>
                        <el-form-item prop="warehouseId">
                            <el-select v-model="queryParams.warehouseId" placeholder="排酸库">
                                <el-option v-for="(item, index) in warehouseList1" :key="index" :label="item.warehouseName" :value="item.warehouseId" />
                            </el-select>
                        </el-form-item> 
                        <el-form-item prop="acidStatus">
                            <el-select v-model="queryParams.acidStatus" placeholder="排酸状态">
                                <el-option v-for="(item, index) in acidStatusList" :key="index" :label="item.text" :value="item.value" />
                            </el-select>
                        </el-form-item> 
                        <el-form-item prop="acidTime">
                            <el-date-picker
                                v-model="acidTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="排酸开始日期"
                                end-placeholder="排酸开始日期">
                            </el-date-picker>
                        </el-form-item> 
                        <el-form-item prop="acidTime">
                            <el-date-picker
                                v-model="acidTime1"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="排酸结束日期"
                                end-placeholder="排酸结束日期">
                            </el-date-picker>
                        </el-form-item> 
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box card_radius_b">
        <el-row :gutter="10" class="mb8 form_btn">
            <el-col class="fend">
                <el-button size="mini" class="default_btn" icon="el-icon-download" @click="exportList">导出数据</el-button>
            </el-col>
        </el-row>
        <!-- 表格数据 -->
        <div :style="{height: tableHeight + 'px'}">
            <el-table
                :data="tableData"
                stripe
                style="width: 100%"
                v-loading="loading"
                border
                :max-height="tableHeight"
            >
                <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
                <el-table-column v-for="(item, index) in tableColumn" show-overflow-tooltip :key="index" :prop="item.prop" :label="item.label" :sortable="item.sortable"
                :sort-method="(a, b) => { return a[item.prop] - b[item.prop]}"
                :align="item.align" :min-width="item.width">
                    <template slot="header">
                        <span v-if="item.prop == 'inWarehouseWeight'">
                            {{item.label}}
                            <el-tooltip class="item" effect="dark" content="若入排酸库时未进行检斤操作，将不计算入库总重" placement="top-start">
                                <i class="el-icon-info"></i>
                            </el-tooltip>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="item.prop =='inWarehouseWeight'">
                            {{ scope.row.remark == '0' ? '-' : scope.row[item.prop]}}</span>
                        <span v-else>{{ scope.row[item.prop] }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="acidStartTime" label="开始时间" width='180' align='center' sortable/>
                <el-table-column prop="acidEndTime" label="结束时间" width='180' align='center' sortable/>
                <el-table-column label="操作" align="center" fixed="right">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-warning-outline" class="text_btn" size="mini" @click="handleDetails(scope.row)" type="text" >查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        />
        </el-card>
        <el-drawer
            class="drawer_box"
            title='排酸详情'
            :visible.sync="detailStatus" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            :wrapperClosable="false">
            <Details :acidTaskId='acidTaskId' @edit='editEnclosure'></Details>
        </el-drawer>
    </div>
</template>
  
<script>
import Details from './components/detail.vue'
import {
    acidTaskList,
    acidTaskExport
} from "@/api/production/index.js";
import { warehouseList } from "@/api/basics/index.js";
import {exportExcel} from '@/utils/east-mind.js'
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                butcherCode: "",
                acidStatus: "", 
                warehouseName: '',
            },
            detailStatus: false,
            tableColumn: [
                { check: true, prop: "butcherCode", label: "屠宰任务单号", width: 160, align: 'center' },
                { check: true, prop: "materialsTypeName", label: "屠宰类型", width: 110, align: 'center' },
                { check: true, prop: "warehouseName", width: 150, label: "排酸库", align: 'center' },
                { check: true, prop: "inWarehouseNum", label: "入库数量", width: 120, align: 'right', sortable: true },
                { check: true, prop: "inWarehouseWeight", label: "入库总重量(kg)", width: 160, align: 'right', sortable: true  },
                { check: true, prop: "acidStatusName", label: "排酸状态", width: 100, align: 'center' },
                // { check: true, prop: "acidStartTime", label: "开始时间", width: 180, align: 'center', sortable: true  },
                // { check: true, prop: "acidEndTime", label: "结束时间", width: 180, align: 'center', sortable: true },
                { check: true, prop: "acidHour", label: "排酸时长", width: 120, align: 'center' },
                { check: true, prop: "updateUserName", label: "创建人", width: 140, align: 'center' },
            ],
            tableData: [],
            loading: true,
            total: 0,
            dataRow: {},
            acidTaskId: '',
            acidTime: [],
            acidTime1: [],
            materialsTypeHash: {
                1: '羊',
                2: '牛'
            },
            acidStatusList: [
                { text: "已结束", value: 4 },
                { text: "进行中", value: -4 },
                { text: "不限", value: '' },
            ],
            warehouseList1: [],
            sreachShow: false,
            windowHeight: ''
        };
    },
    components: {
        Details
    },
    created() {
        this.getList()
        this.getwarehouseList()
    },
    methods: {
        //列表查询
        getList() {
            acidTaskList(this.queryParams).then((res) => {
                this.loading = false;
                if (res.code == 200) {
                    this.tableData = res.result.list.map((item) => {
                        item.materialsTypeName = this.materialsTypeHash[item.materialsType]
                        item.acidStatusName = item.acidStatus == 4 ? '已结束' : '进行中'
                        return item
                    });
                    this.total = Number(res.result.total);
                }
            })
        },
        //列表排酸库查询
        getwarehouseList() {
            warehouseList({
                pageNum: 1,
                pageSize: 10000,
                warehouseType: 1
            }).then((res) => {
                if (res.code == 200) {
                    this.warehouseList1 = res.result.list
                }
            });
        },
        reset() {
            this.$refs.queryForm.resetFields();
            this.acidTime = []
            this.acidTime1 = []
        },
        handleDetails(row) {
            this.acidTaskId = row.acidTaskId
            this.detailStatus = true
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.acidTime);
            this.handelData("startTime2", "endTime2", this.acidTime1);
            this.getList();
        },
        addCutApart() {
            this.acidTaskId = ''
            this.addCutApartModel = true
        },
        //编辑
        editEnclosure(row) {
            this.detailStatus = false
            this.acidTaskId = row.acidTaskId
            this.addCutApartModel = true
        },
        //关闭
        closeCutApart() {
            this.addCutApartModel = false;
            this.resetQuery()
        },
        exportList(){exportExcel(acidTaskExport,this.queryParams,'排酸列表')},
    }, 
};
</script>
  
<style lang="scss" scoped>
</style>
  