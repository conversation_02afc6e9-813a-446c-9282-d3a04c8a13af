<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                  <el-form-item prop="reportDate">
                    <el-date-picker
                      v-model="queryParams.reportDate"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="生产日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-row>
            </el-row>
          <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="mb8 form_btn fend">
        <el-button icon="el-icon-plus" v-if="isAdd" class="default_btn" size="mini" @click="dateFocus">新建生产日报表</el-button>
        <div class="from" v-else>
          <el-date-picker
            v-model="dateValue"
            value-format="yyyy-MM-dd"
            type="date" size="small"
            ref="saveDateInput"
            placeholder="选择日期">
          </el-date-picker>
          <el-button class="default_btn ml10" size="mini" @click="isAdd = true">取消</el-button>
          <el-button class="default_btn" size="mini" @click="addData">确定</el-button>
        </div>
        <!-- <el-button icon="el-icon-download" class="exprot_btn ml10" size="mini" @click="exportList">导出数据</el-button> -->
      </el-row>
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip type="index" align="center" width="55" label="序号"></el-table-column>
          <el-table-column  show-overflow-tooltip label="生产日期" sortable align="center" prop="reportDate"></el-table-column>
          <el-table-column  show-overflow-tooltip label="创建人" align="center" prop="createUserName"></el-table-column>
          <el-table-column  show-overflow-tooltip label="创建时间" sortable align="center" prop="createTime"></el-table-column>
          <el-table-column label="操作" width='240' align="center">
              <template slot-scope="scope">
                  <el-button class="edit_text_btn" icon="el-icon-edit" size="mini" @click="edit(scope.row)" type="text" >编辑</el-button>
                  <el-button class="delete_text_btn" icon="el-icon-delete" size="mini" @click="deleteEreport(scope.row)" type="text" >删除</el-button>
                  <el-button class="text_btn" size="mini" @click="exportList(scope.row)" type="text" >下载</el-button>
              </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 新建 -->
    <el-drawer
        class="drawer_box drawer_box1"
        :visible.sync="visibleStatus" 
        :show-close="true" 
        :append-to-body="true" 
        :destroy-on-close="true"
        size="100%"
        title="新建生产日报表"
        :wrapperClosable="false">
        <ReportForm ref="reportForm" @close='close'></ReportForm>
    </el-drawer>
  </div>
</template>
      
  <script>
import { reportProduceList, infoForAdd, reportProduceExport, reportProduceDelete } from '@/api/statistics'
import { exportExcel, parseTime } from "@/utils/east";
import ReportForm from "./reportForm.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  dicts: ["mes_supplier_type"],
  data() {
    return {
      queryParams: {
        reportDate:'',
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      loading: true,
      total: 0,
      visibleStatus: false,
      isAdd: true,
      dateValue: parseTime(new Date(),'{y}-{m}-{d}'),
    };
  },
  components:{
    ReportForm
  },
  created() {
    this.getList();
  },
  methods: {
    dateFocus() {
      this.isAdd = false
      this.$nextTick(() => {
        this.$refs.saveDateInput.$refs.reference.$refs.input.focus()
      }) 
    },
    refresh() {
      this.getList();
    },
    exportList(row){
      exportExcel(reportProduceExport,{reportProduceId: row.reportProduceId}, row.reportDate + '-生产报表')
    },
    //列表查询
    getList() {
      reportProduceList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateRange = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      // this.handelData("startTime", "endTime", this.dateSettle);
      this.getList();
    },
    addData() {
      if (!this.dateValue) {
        this.$message('请选择日期')
        return
        // this.$message('当前日期下已存在统计报表，不支持再次新增')
      }
      infoForAdd({
        reportDate: this.dateValue
      }).then(res => {
        this.visibleStatus = true
        this.$nextTick(() => {
          this.$refs.reportForm.getInfoForAdd(this.dateValue)
        })
      })
    },
    addData() {
      if (!this.dateValue) {
        this.$message('请选择日期')
        return
        // this.$message('当前日期下已存在统计报表，不支持再次新增')
      }
      infoForAdd({
        reportDate: this.dateValue
      }).then(res => {
        this.visibleStatus = true
        this.$nextTick(() => {
          this.$refs.reportForm.getInfoForAdd(this.dateValue)
          this.dateValue = new Date()
          this.isAdd = true
        })
      })
    },
    edit(row) {
      this.visibleStatus = true
      this.$nextTick(() => {
        this.$refs.reportForm.getReportProduceInfo(row.reportProduceId)
      })
    },
    deleteEreport(row) {
        this.$confirm('此操作将永久删除该报表, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
          reportProduceDelete({
                reportProduceId: row.reportProduceId
            }).then(res => {
                if (res.code == 200) {
                    this.$message.success('已删除')
                    this.getList();
                }
            })
        }).catch(() => {
            this.$message({
                type: 'info',
                message: '已取消删除'
            });          
        });
    },
    close(){
      this.visibleStatus = false;
      this.getList()
    }
  },
};
</script>
      
<style lang="scss">
.drawer_box1{
    .el-drawer__body{
        padding-left: 22px !important;
    }
}
.ml10{
  margin-left: 10px;
}
</style>
      