<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                <el-form-item prop="butcherCode">
                  <el-input v-model="queryParams.butcherCode" placeholder="屠宰任务编号"  />
                </el-form-item>
                <el-form-item prop="supplierName">
                  <el-input v-model="queryParams.supplierName" placeholder="供应商名称"  />
                </el-form-item>
                <el-form-item prop="materialsId">
                  <el-select v-model="queryParams.materialsId" placeholder="原料名称">
                    <el-option
                      v-for="(item,index) in materialsList"
                      :label="item.materialsName"
                      :value="item.materialsId"
                      :key="index"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item prop="materialsLevelId">
                  <el-select v-model="queryParams.materialsLevelId" placeholder="原料等级">
                    <el-option
                      v-for="(item,index) in levelList"
                      :label="item.levelName"
                      :value="item.materialsLevelId"
                      :key="index"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-date-picker
                    v-model="dateEnter1"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="入厂开始日期"
                    end-placeholder="入厂结束日期"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-date-picker
                    v-model="dateEnter"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="检斤开始日期"
                    end-placeholder="检斤结束日期"
                  ></el-date-picker>
                </el-form-item>
            </el-row>
          </el-row>
          <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row class="mb8 form_btn">
        <el-col :span="12" class="tabs-box">
          <el-tabs v-model="listType">
            <el-tab-pane label="结算检斤" name="1"></el-tab-pane>
            <!-- <el-tab-pane label="屠宰检斤" name="2"></el-tab-pane> -->
            <el-tab-pane label="二次检斤" name="3"></el-tab-pane>
            <el-tab-pane label="出排酸检斤" name="4"></el-tab-pane>
            <el-tab-pane label="出肉率统计" name="5"></el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="12" class="fend">
          <el-button icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table ref="myTable" :data="tableData" border v-if="listType == 1" v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip type="index" align="center" label="序号" width="55" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="createTime" align="center" sortable label="检斤日期" width="140" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="butcherCode" align="center" label="屠宰任务编号" width="160" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="materialsName" align="center" width="140" label="原料名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="levelName" align="center" width="140" label="原料等级"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightSection" width="140" align="center" label="规格(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightNum" align="right" width="140" sortable label="检斤只数"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="netWeight" align="right" width="140" sortable label="胴体重量(kg)" :sort-method="(a, b) => { return a.netWeight - b.netWeight}"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="averageWeight" align="right" width="140" sortable label="均重(kg)" :sort-method="(a, b) => { return a.averageWeight - b.averageWeight}"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightAmount" width="140" align="right" sortable label="称重金额(元)" :sort-method="(a, b) => { return a.weightAmount - b.weightAmount}"></el-table-column>
          <!-- <el-table-column  show-overflow-tooltip prop="subsidyAmount" align="right" sortable label="补贴金额"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="deductAmount" align="right" sortable label="扣款金额"></el-table-column> -->
          <!-- <el-table-column  show-overflow-tooltip prop="index" label="结算金额"></el-table-column> -->
          <el-table-column  show-overflow-tooltip align="center" prop="supplierName" width="120" label="供应商"></el-table-column>
          <el-table-column  show-overflow-tooltip  label="供应商类型" width="120" align="center">
              <template slot-scope="scope">{{ handeltext(scope.row.supplierType) }}</template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip prop="supplierContactName" width="140" align="center" label="联系人"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="supplierContactPhone" width="140" align="center" label="联系方式"></el-table-column>
        </el-table>
        <!-- 表格数据 -->
        <el-table :data="tableData" border v-if="listType == 2" v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip type="index" align="center" label="序号" width="55" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="checkInTime" align="center" width="140"  sortable label="入厂时间" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="butcherCode" align="center" label="屠宰任务编号" width="160" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="supplierName" align="center" width="140" label="供应商名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="materialsName" align="center" width="140" label="原料名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="levelName" align="center" width="140" label="原料等级"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightNum" align="right" width="140" sortable label="称重数量"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="grossWeight" width="140" :sort-method="(a, b) => { return a.grossWeight - b.grossWeight}" align="right" sortable label="毛重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="tareWeight" width="140" :sort-method="(a, b) => { return a.tareWeight - b.tareWeight}" align="right" sortable label="皮重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="netWeight" width="140" :sort-method="(a, b) => { return a.netWeight - b.netWeight}" align="right" sortable label="净重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="averageWeight" width="140" :sort-method="(a, b) => { return a.averageWeight - b.averageWeight}" align="right" sortable label="均重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="unitPrice" width="140" :sort-method="(a, b) => { return a.unitPrice - b.unitPrice}" align="right" sortable label="单价(元)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightPrice"  width="140" :sort-method="(a, b) => { return a.weightPrice - b.weightPrice}" align="right" sortable label="称重金额(元)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="updateTime" width="140" align="center" sortable label="称重时间">
            <template slot-scope="scope">{{ scope.row.updateTime }}</template>
          </el-table-column>
        </el-table>
        <!-- 表格数据 -->
        <el-table ref="myTable" :data="tableData" border v-if="listType == 3 || listType == 4" v-loading="loading"  :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip type="index" align="center" label="序号" width="55" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="checkInTime" align="center" width="140" sortable label="入厂时间" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="butcherCode" align="center" label="屠宰任务编号" width="160" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="supplierName" align="center" width="140" label="供应商名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="carcassDirection" align="center" width="140" label="胴体流向"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="materialsName" align="center" width="140" label="原料名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="weightNum" align="right" sortable width="140" label="称重数量"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="grossWeight" width="140" :sort-method="(a, b) => { return a.grossWeight - b.grossWeight}" align="right" sortable label="毛重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="tareWeight" width="140" :sort-method="(a, b) => { return a.tareWeight - b.tareWeight}" align="right" sortable label="皮重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="netWeight" width="140" :sort-method="(a, b) => { return a.netWeight - b.netWeight}" align="right" sortable label="净重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="averageWeight" width="140" :sort-method="(a, b) => { return a.averageWeight - b.averageWeight}" align="right" sortable label="均重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" width="140" sortable label="称重时间">
            <template slot-scope="scope">{{ scope.row.updateTime }}</template>
          </el-table-column>
        </el-table>
        <el-table ref="myTable" :data="tableData" border v-if="listType == 5" v-loading="loading"  :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip type="index" align="center" label="序号" width="55" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="checkInTime" align="center" width="140" sortable label="入厂时间" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="butcherCode" align="center" width="140" label="屠宰编号" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="supplierName" align="center" width="140" label="供应商名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="materialsName" align="center" label="原料名称"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="livestockCode" align="center" label="耳标编号"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="checkInNetWeight" :sort-method="(a, b) => { return a.checkInNetWeight - b.checkInNetWeight}" align="right" sortable label="入厂重量(kg)"  width="140"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="beforeButcherNetWeight" :sort-method="(a, b) => { return a.beforeButcherNetWeight - b.beforeButcherNetWeight}" align="right" sortable label="宰前重量(kg)"  width="140"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="netWeight" :sort-method="(a, b) => { return a.netWeight - b.netWeight}" align="right" sortable label="胴体重量(kg)"  width="140"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="meatYieldPercent" align="right" width="140" label="出肉率(%)"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="checkInWeightTime" sortable align="center" width="160" label="入厂称重时间"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="beforeButcherWeightTime" sortable align="center" width="160" label="宰前称重时间"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="updateTime" align="center" sortable  width="160" label="胴体称重时间"></el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getData"
      />
    </el-card>
  </div>
</template>
      
      <script>
import { materialsList, materialsLevelList } from "@/api/acquisition/index";
import {
  butcherWeight,
  butcherWeightExport,
  butcherWeightPage,
  butcherWeightPageExport,
  carcassWeightPage,
  carcassWeightPageExport,
  meatYield,
  meatYieldExport
 } from "@/api/statistics.js";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  data() {
    return {
      queryParams: {
        butcherCode: "",
        materialsId:'',
        materialsLevelId:'',
        pageNum: 1,
        pageSize: 10,
      },
      materialsList: [], //原料名称
      levelList: [], //原料等级
      tableData: [],
      loading: true,
      dateEnter: [],
      dateEnter1: [],
      total: 0,
      supplierTypeList: [
        { label: "企业", value: 1 },
        { label: "中间商", value: 2 },
        { label: "养殖户", value: 3 },
      ],
      windowHeight: 0,
      tableHeight: 0,
      tableBoxHeight: 0,
      sreachShow: false,
      listType: '1',
      carcassWeightStatus: ''
    };
  },
  computed: {
    handeltext(){
        return (value)=>{
            let name=''
            this.supplierTypeList.forEach(item=>{
                if(item.value==value){
                    name=item.label
                }
            })
            return name
        }
    }
  },
  watch: {
    listType() {
      this.getData()
    }
  },
  created() {
    this.getMaterial();
    this.getList();
    this.getLevel();
  },
  methods: {
      getData(){
        switch (this.listType) {
          case '1': 
            this.getList();
          break;
          case '2': 
            this.getButcherWeightPage();
          break;
          case '3': 
            this.carcassWeightStatus = '1,2,4'

            this.getCarcassWeightPage();
          break;
          case '4': 
            this.carcassWeightStatus = '3,5'
            this.getCarcassWeightPage();
          break;
          case '5': 
            this.getMeatYield()
          break
        }
      },
    refresh() {
      this.getData();
    },
    getMaterial() {
      materialsList({}).then((res) => {
        if (res.code == 200) {
          this.materialsList = res.result.list || [];
        }
      });
    },
    getLevel() {
      materialsLevelList({}).then((res) => {
        if (res.code == 200) {
          this.levelList = res.result.list || [];
        }
      });
    },
    //列表查询
    getList() {
      butcherWeight(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
          this.$nextTick(() => {
            this.$refs.myTable.doLayout();
          });
        }
      });
    },
    //屠宰列表查询
    getButcherWeightPage() {
      butcherWeightPage({
        ...this.queryParams,
        weightFlow: 3,
        requestRole: 2
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
          this.$nextTick(() => {
            this.$refs.myTable.doLayout();
          });
        }
      });
    },
    //屠宰列表查询
    getCarcassWeightPage() {
      carcassWeightPage({
        ...this.queryParams,
        searchValue: this.carcassWeightStatus,
        remark: this.listType == 3 ? 1 : ''
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
          this.$nextTick(() => {
            this.$refs.myTable.doLayout();
          });
        }
      });
    },
    getMeatYield() {
      meatYield({
        ...this.queryParams,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
          this.$nextTick(() => {
            this.$refs.myTable.doLayout();
          });
        }
      });
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateEnter = [];
      this.dateEnter1 = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getData();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.handelData("startTime2", "endTime2", this.dateEnter1);
      this.getData();
    },
    exportList(){
        switch (this.listType) {
          case '1': 
            exportExcel(butcherWeightExport,this.queryParams,'结算统计')
          break;
          case '2': 
            this.carcassWeightStatus = '1,2,4'
            exportExcel(butcherWeightPageExport,{
              ...this.queryParams,
              weightFlow: 2,
              requestRole: 2
            },'检斤统计')
          break;
          case '3': 
            this.carcassWeightStatus = '1,2,4'
            exportExcel(carcassWeightPageExport,{
              ...this.queryParams,
              remark: 1,
              searchValue: this.carcassWeightStatus
            },'二次检斤统计')
          break;
          case '4': 
            this.carcassWeightStatus = '3,5'
            exportExcel(carcassWeightPageExport,{
              ...this.queryParams,
              searchValue: this.carcassWeightStatus
            },'出排酸统计')
          break;
          case '5': 
            exportExcel(meatYieldExport,this.queryParams,'出肉率统计')
          break
        }
    },
  },
};
</script>
      
      <style lang="scss" scoped>
   
</style>
      