<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
      <el-row :gutter="10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                  <el-form-item>
                    <el-date-picker
                      v-model="dateSettle"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="生产开始日期"
                      end-placeholder="生产结束日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-row>
            </el-row>
            <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
            </el-row>
          </el-form>
      </el-row>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row class="mb8 form_btn">
        <el-col :span="12" class="tabs-box">
          <el-tabs v-model="queryParams.divisionTaskType" @tab-click="getList">
            <el-tab-pane label="冷线分割" name="2"></el-tab-pane>
            <el-tab-pane label="热线分割" name="1"></el-tab-pane>
            <el-tab-pane label="二次精加工" name="3"></el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="12" class="fend">
          <el-button icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip align="center" width="55" type="index" label="序号"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" label="产品编码" prop="productCode"></el-table-column>
          <el-table-column  show-overflow-tooltip label="产品名称" prop="productName"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" label="产品规格" prop="specification"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable label="生产数量" prop="inventoryNum"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable :sort-method="(a, b) => { return a.inventoryWeight - b.inventoryWeight}" label="生产重量（kg）" prop="inventoryWeight"></el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
      
      <script>
import { productStatistics ,productStatisticsExport} from "@/api/statistics";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  dicts: ["mes_supplier_type"],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        divisionTaskType: '2'
      },
      tableData: [],
      loading: true,
      dateSettle: [],
      total: 0,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    exportList(){
      let excelName = ''
      if (this.queryParams.divisionTaskType == 1) {
        excelName = '热线分割产品统计报表'
      } else if (this.queryParams.divisionTaskType == 2) {
        excelName = '冷线分割产品统计报表'
      } else if (this.queryParams.divisionTaskType == 3) {
        excelName = '二次精加工产品统计表'
      }
      exportExcel(productStatisticsExport,this.queryParams,excelName)
    },
    //列表查询
    getList() {
      productStatistics(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateSettle = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateSettle);
      this.getList();
    },
  },
};
</script>
      
      <style lang="scss" scoped>
</style>
      