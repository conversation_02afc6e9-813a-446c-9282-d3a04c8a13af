<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
              <el-form-item prop="checkInCode">
                <el-input v-model="queryParams.checkInCode" placeholder="入厂编号"  />
              </el-form-item>
              <el-form-item prop="materialsId">
                <el-select v-model="queryParams.materialsId" placeholder="原料名称">
                  <el-option v-for="(item,index) in materialsList" :label="item.materialsName" :value="item.materialsId" :key="index" />
                </el-select>
              </el-form-item>
              <el-form-item prop="checkInType">
                <el-select v-model="queryParams.checkInType" placeholder="入厂方式">
                  <el-option label="预约入厂" value="2" />
                  <el-option label="直接入厂" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="dateEnter"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="入厂开始日期"
                  end-placeholder="入厂结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-row>
          </el-row>
          <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="fend mb8 form_btn">
        <el-button icon="el-icon-download" size="mini" class="default_btn" @click="exportList">导出数据</el-button>
      </el-row>

      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" border="" stripe style="width: 100%" v-loading="loading" :max-height="tableHeight">
          <el-table-column  type="index" align="center" width="55" label="序号"></el-table-column>
          <el-table-column  show-overflow-tooltip 
            v-for="(item,index) in tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :align="item.align"
            :sortable="item.sortable"
            :min-width="item.width"
          > 
            <template slot-scope="scope">
              <span v-if="item.prop == 'supplierType'">{{ handeltext(supplierTypeList,scope.row.supplierType) }}</span>
              <span v-else-if="item.prop == 'checkInType'">{{ checkInTypetext(checkInTypeList,scope.row.checkInType) }}</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
    
    <script>
import { checkInDetail, checkInDetailExport } from "@/api/statistics.js";
import { materialsList } from "@/api/acquisition/index";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  data() {
    return {
      tableColumn: [
        { check: true, prop: "checkInTime", label: "入厂日期", width: 140, align:'center', sortable: true },
        { check: true, prop: "checkInCode", label: "入厂编号", align:'center', width: 140 },
        { check: true, prop: "checkInType", label: "入厂方式", align:'center', width: 140 },
        { check: true, prop: "materialsName", label: "原料名称", align:'center', width: 140 },
        { check: true, prop: "checkInNum", label: "入厂只数", width: 140, align:'right', sortable: true },
        { check: true, prop: "butcherNum", label: "实际屠宰只数", width: 160, align:'right', sortable: true },
        { check: true, prop: "supplierName", label: "供应商", align:'center', width: 140 },
        { check: true, prop: "supplierType", label: "供应商类型", align:'center', width: 140 },
        { check: true, prop: "supplierContactName", label: "联系人", align:'center', width: 140 },
        { check: true, prop: "supplierContactPhone", label: "联系方式", align:'center', width: 140 },
      ],
      queryParams: {
        "pageNum": 1,
        "pageSize": 10,
        "checkInCode": '', //入厂编号
        "checkInType": '', //入厂类型（1直接入厂 2预约入厂）
      },
      materialsList: [], //原料名称
      tableData: [],
      loading: true,
      dateEnter: [],
      total: 0,
      supplierTypeList: [
        { label: "企业", value: 1 },
        { label: "中间商", value: 2 },
        { label: "养殖户", value: 3 },
      ],
      checkInTypeList: [
        { label: "直接入厂", value: 1 },
        { label: "预约入厂", value: 2 },
      ],
    };
  },

  computed: {
    handeltext(){
        return (list,value)=>{
            let name=''
            list.forEach(item=>{
                if(item.value==value){
                    name=item.label
                }
            })
            return name
        }
    },
    checkInTypetext(){
        return (list,value)=>{
            let name=''
            list.forEach(item=>{
                if(item.value==value){
                    name=item.label
                }
            })
            return name
        }
    },
  },
  created() {
    this.getMaterial();
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    getMaterial() {
      materialsList({}).then((res) => {
        if (res.code == 200) {
          this.materialsList = res.result.list;
        }
      });
    },
    //列表查询
    getList() {
        checkInDetail(this.queryParams).then((res) => {
          if (res.code == 200) {
            this.tableData = res.result.list
            this.total = Number(res.result.total);
            this.loading = false;
          }
        });
    },

    //重置
    resetQuery() {
      this.dateRange = [];
      this.reset();
      this.handleQuery();
    },
    reset(){
        this.dateEnter = []
        this.resetForm("queryForm");
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.getList();
    },
    exportList(){exportExcel(checkInDetailExport,this.queryParams,'入厂统计')},
  },
};
</script>
    
    <style lang="scss" scoped>
</style>
    