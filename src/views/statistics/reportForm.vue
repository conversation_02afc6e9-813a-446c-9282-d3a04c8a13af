<template>
    <div class="app-container">
        <div class="header-tips">字体颜色为<span style="color: #0C36FF">蓝色</span>部分，可编辑修改。</div>
        <div class="header-box">
            <div class="time">
                <span>统计日期：{{formData.reportDate}}</span>
                <span>上报日期：{{formData.submitDate}}</span>
            </div>
            <div class="name">
                <label>报表名称</label>
                <el-input v-model="formData.reportName" class="input" placeholder="请输入报表名称"></el-input>
            </div>
        </div>
        <div class="total-data">
            <table class=" border-b  border-r">
                <tr class="table-header">
                    <th colspan="4">当日屠宰分割羊明细</th>
                    <th colspan="4">排酸库存量明细</th>
                    <th colspan="8">屠宰分割出成汇总</th>
                </tr>
                <tr class="sub-title">
                    <td>种类</td>
                    <td>只数</td>
                    <td>重量(kg)</td>
                    <td>均重(kg)</td>
                    <td>种类</td>
                    <td>只数</td>
                    <td>重量(kg)</td>
                    <td>均重(kg)</td>
                    <td>种类</td>
                    <td>结余领料(kg)</td>
                    <td>羊尾领料(kg)</td>
                    <td>生产线/仓库领料(kg)</td>
                    <td>生成产品总重(kg)</td>
                    <td>分割损耗(kg)</td>
                    <td>损耗率(%)</td>
                    <td>出成率(%)</td>
                </tr>
                <tr class="content">
                    <td>羊尾称重</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.sheepTailNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.sheepTailWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.sheepTailNum > 0">
                        {{
                            formData.sheepTailWeightAverage = 
                            (formData.sheepTailWeight / formData.sheepTailNum).toFixed(2) != 'NaN' ? (formData.sheepTailWeight / formData.sheepTailNum).toFixed(2) : ''
                        }}
                        </span>
                    </td>
                    <td>前日库存量</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInventoryNumYesterday" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInventoryWeightYesterday" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.acidInventoryNumYesterday > 0">
                            {{
                                formData.acidInventoryWeightAverageYesterday = 
                                (formData.acidInventoryWeightYesterday / formData.acidInventoryNumYesterday).toFixed(2) != 'NaN' ? (formData.acidInventoryWeightYesterday / formData.acidInventoryNumYesterday).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td>热线生产</td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeDirectSplitWeight" v-model="formData.hotBalanceCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeDirectSplitWeight" v-model="formData.hotSheepTailCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.hotWarehouseCollect" @input="changeHotWarehouseCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeDirectSplitWeight" v-model="formData.hotInFreezeWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeHotProducePercent" v-model="formData.hotLossWeight" class="tabel-input"></el-input>
                    </td>
                    <td>{{formData.hotLossPercent || '0.00'}}</td>
                    <td>{{formData.hotProducePercent || '0.00'}}</td>
                </tr>
                <tr class="content">
                    <td>胴体一检</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.butcherWeightNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.butcherWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.butcherWeightNum > 0">
                            {{
                                formData.butcherWeightAverage = 
                                (formData.butcherWeight / formData.butcherWeightNum).toFixed(2) != 'NaN' ? (formData.butcherWeight / formData.butcherWeightNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td>当日入库量</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.acidInNum > 0">
                            {{
                                formData.acidInWeightAverage =
                                (formData.acidInWeight / formData.acidInNum).toFixed(2) != 'NaN' ? (formData.acidInWeight / formData.acidInNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td>冷线生产</td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeAcidSplitWeight" v-model="formData.coldBalanceCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeAcidSplitWeight" v-model="formData.coldSheepTailCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.coldWarehouseCollect" @input="changeColdWarehouseCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeAcidSplitWeight" v-model="formData.coldInFreezeWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeColdLossWeight" v-model="formData.coldLossWeight" class="tabel-input"></el-input>
                    </td>
                    <td>{{formData.coldLossPercent || '0.00'}}</td>
                    <td>{{formData.coldProducePercent || '0.00'}}</td>
                </tr>
                <tr class="content">
                    <td>热胴体分割</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.directSplitNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.directSplitWeight" @input="changeDirectSplitWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.directSplitNum > 0">
                            {{
                                formData.directSplitWeightAverage = 
                                (formData.directSplitWeight / formData.directSplitNum).toFixed(2) != 'NaN' ? (formData.directSplitWeight / formData.directSplitNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td>当日库存量</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInventoryNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidInventoryWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.acidInventoryNum > 0">
                            {{
                                formData.acidInventoryWeightAverage =
                                (formData.acidInventoryWeight / formData.acidInventoryNum).toFixed(2) != 'NaN' ? (formData.acidInventoryWeight / formData.acidInventoryNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td>二次精加工生产</td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeProcessingBalanceCollect" v-model="formData.processingBalanceCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeProcessingBalanceCollect" v-model="formData.processingSheepTailCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeProcessingBalanceCollect" v-model="formData.processingWarehouseCollect" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeProcessingBalanceCollect" v-model="formData.processingInFreezeWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeProcessingLossWeight" v-model="formData.processingLossWeight" class="tabel-input"></el-input>
                    </td>
                    <td>{{formData.processingLossPercent || '0.00'}}</td>
                    <td>{{formData.processingProducePercent || '0.00'}}</td>
                </tr>
                <tr class="content">
                    <td>热胴体入急冻库</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.carcassFreezeNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.carcassFreezeWeight" class="tabel-input"></el-input>
                    </td>
                    <td>
                        <span v-if="formData.carcassFreezeNum > 0">
                            {{
                                formData.carcassFreezeWeightAverage = 
                                (formData.carcassFreezeWeight / formData.carcassFreezeNum).toFixed(2) != 'NaN' ? (formData.carcassFreezeWeight / formData.carcassFreezeNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                    <td class="border-b">排酸损耗</td>
                    <td class="mw border-b">
                        <!-- <el-input maxlength='8' v-model="formData.input" class="tabel-input"></el-input> -->
                    </td>
                    <td class="mw border-b">
                        <el-input maxlength='8' v-model="formData.acidLossWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="border-b"></td>
                    <td class="border-b">合计：</td>
                    <td class="mw border-b total-value">
                        {{formData.totalBalanceCollect =
                            (formData.hotBalanceCollect + formData.coldBalanceCollect + formData.processingBalanceCollect ) != 'NaN' ?(+formData.hotBalanceCollect + +formData.coldBalanceCollect + +formData.processingBalanceCollect).toFixed(2) : ''
                        }}
                    </td>
                    <td class="mw border-b total-value">
                        {{formData.totalSheepTailCollect =
                            (formData.hotSheepTailCollect + formData.coldSheepTailCollect + formData.processingSheepTailCollect ) != 'NaN' ?(+formData.hotSheepTailCollect + +formData.coldSheepTailCollect + +formData.processingSheepTailCollect).toFixed(2) : ''
                        }}
                    </td>
                    <td class="mw border-b total-value">
                        {{formData.totalWarehouseCollect =
                            (formData.hotWarehouseCollect + formData.coldWarehouseCollect + formData.processingWarehouseCollect ) != 'NaN' ?(+formData.hotWarehouseCollect + +formData.coldWarehouseCollect + +formData.processingWarehouseCollect).toFixed(2) : ''
                        }}
                    </td>
                    <td class="mw border-b total-value">
                        {{formData.totalInFreezeWeight =
                            (formData.hotInFreezeWeight + formData.coldInFreezeWeight + formData.processingInFreezeWeight ) != 'NaN' ?(+formData.hotInFreezeWeight + +formData.coldInFreezeWeight + +formData.processingInFreezeWeight).toFixed(2) : ''
                        }}
                    </td>
                    <td class="mw border-b total-value">
                        {{formData.totalLossWeight =
                            (formData.hotLossWeight + formData.coldLossWeight + formData.processingLossWeight ) != 'NaN' ?(+formData.hotLossWeight + +formData.coldLossWeight + +formData.processingLossWeight).toFixed(2) : ''
                        }}
                    </td>
                    <td class="border-b total-value">{{formData.totalLossPercent || '0.00'}}</td>
                    <td class="border-b total-value">{{formData.totalProducePercent || '0.00'}}</td>
                </tr>
                <tr class="content">
                    <td>排酸胴体分割</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidSplitNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' @input="changeAcidSplitWeight" v-model="formData.acidSplitWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="border-r">
                        <span v-if="formData.acidSplitNum > 0">
                            {{
                                formData.acidSplitWeightAverage = 
                                (formData.acidSplitWeight / formData.acidSplitNum).toFixed(2) != 'NaN' ? (formData.acidSplitWeight / formData.acidSplitNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                </tr>
                <tr class="content">
                    <td>排酸胴体入急冻库</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidFreezeNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.acidFreezeWeight" class="tabel-input"></el-input>
                    </td>
                    <td class="border-r">
                        <span v-if="formData.acidFreezeNum > 0">
                            {{formData.acidFreezeWeightAverage = 
                                (formData.acidFreezeWeight / formData.acidFreezeNum).toFixed(2) != 'NaN' ? (formData.acidFreezeWeight / formData.acidFreezeNum).toFixed(2) : ''
                            }}
                        </span>
                    </td>
                </tr>
                <tr class="content total-label ">
                    <td>加工羊只合计:</td>
                    <td class="mw  total-value border-n">
                        {{ 
                            formData.processingTotalNum =
                            (+formData.directSplitNum + formData.carcassFreezeNum + formData.acidSplitNum + formData.acidFreezeNum ) != 'NaN' ?(+formData.directSplitNum + +formData.carcassFreezeNum + +formData.acidSplitNum + +formData.acidFreezeNum).toFixed(0) : ''
                        }}
                    </td>
                    <td class="mw total-value border-n">
                        {{ formData.processingTotalWeight  =
                            (formData.directSplitWeight + formData.carcassFreezeWeight + formData.acidSplitWeight + formData.acidFreezeWeight ) != 'NaN' ?(+formData.directSplitWeight + +formData.carcassFreezeWeight + +formData.acidSplitWeight + +formData.acidFreezeWeight).toFixed(2) : ''
                        }}
                    </td>
                    <td class="border-n border-r"></td>
                </tr>
            </table>
        </div>
        <div class="total-data">
            <table class=" border-b  border-r">
                <tr class="table-header">
                    <th colspan="8">生成产品明细</th>
                </tr>
                <tr class="sub-title">
                    <td>产品编码</td>
                    <td>产品名称</td>
                    <td>本日生产数量</td>
                    <td>本日生产重量(kg)</td>
                    <td>产品编码</td>
                    <td>产品名称</td>
                    <td>本日生产数量</td>
                    <td>本日生产重量(kg)</td>
                </tr>
                <tr class="content" v-for="(item, index) in leftList" :key="index">
                    <td>{{item.productCode}}</td>
                    <td>{{item.productName}}</td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="item.productNum" @input="getLeftTotal" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="item.productWeight" @input="getLeftTotal" class="tabel-input"></el-input>
                    </td>
                    <td v-if="rightList[index]">{{rightList[index].productCode}}</td>
                    <td v-else></td>
                    <td v-if="rightList[index]">{{rightList[index].productName}}</td>
                    <td v-else></td>
                    <td  v-if="rightList[index]" class="mw">
                        <el-input maxlength='8' v-model="rightList[index].productNum" @input="getrightTotal" class="tabel-input"></el-input>
                    </td>
                    <td v-else></td>
                    <td v-if="rightList[index]" class="mw">
                        <el-input maxlength='8' v-model="rightList[index].productWeight" @input="getrightTotal" class="tabel-input"></el-input>
                    </td>
                    <td v-else></td>
                </tr>
                <tr class="content total-label" v-if="leftList.length > 0">
                    <td>小计:</td>
                    <td class="mw  total-value border-n">
                        
                    </td>
                    <td class="mw total-value border-n">{{formData.leftProductNum}}</td>
                    <td class="border-n total-value">{{formData.leftProductWeight}}</td>
                    <td class="border-b">{{ rightList.length > 0 ? '小计:' : '' }}</td>
                    <td class="mw  total-value border-b">
                        
                    </td>
                    <td class="mw total-value border-b">{{ rightList.length > 0 ? formData.rightProductNum : ''}}</td>
                    <td class="border-b total-value">{{ rightList.length > 0 ? formData.rightProductWeight : ''}}</td>
                </tr>
                <tr class="content total-label" v-if="leftList.length > 0">
                    <td>合计:</td>
                    <td class="mw  total-value border-n">
                    </td>
                    <td class="mw total-value border-n">{{formData.totalProductNum}}</td>
                    <td class="border-n border-r total-value">{{formData.totalProductWeight}}</td>
                </tr>
            </table>
        </div>
        <div class="total-data">
            <table class=" border-b  border-r">
                <tr class="table-header">
                    <th colspan="8">人员管理</th>
                </tr>
                <tr class="sub-title">
                    <td>应到人数</td>
                    <td>实到人数</td>
                    <td>请假人数</td>
                    <td>屠宰人数</td>
                    <td>分割人数</td>
                </tr>
                <tr class="content">
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.shouldWorkNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.actualWorkNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.leaveNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.butcherWorkNum" class="tabel-input"></el-input>
                    </td>
                    <td class="mw">
                        <el-input maxlength='8' v-model="formData.splitWorkNum" class="tabel-input"></el-input>
                    </td>
                </tr>
            </table>
        </div>
        <div class="footer_btn fcc">
            <el-button type="primary" class="grey_fill_btn" @click="colse" size="small">取 消</el-button>
            <el-button type="primary" size="small" @click="submit">提 交</el-button>
        </div>
    </div>
</template>
      
<script>
import { infoForAdd, reportProduceAdd,reportProduceInfo, reportProduceupdate } from '@/api/statistics'
import { parseTime } from '@/utils/east'
export default {
    data() {
      return {
        input: '',
        formData: {},
        leftList: [],
        rightList: [],
        reportProduceId: ''
      }
    },
    computed: {
    },
    created() {
    },

    methods: {
        getInfoForAdd(reportDate) {
            infoForAdd({
                reportDate
            }).then((res) => {
                this.formData = res.result
                this.leftList = this.formData.leftList || []
                this.rightList = this.formData.rightList || []
                this.formData.reportDate = parseTime(reportDate, '{y}-{m}-{d}')
                this.formData.submitDate = parseTime(new Date(), '{y}-{m}-{d}')
                this.getLeftTotal()
                this.getrightTotal()
                this.getTotalProduct()
            })
        },
        getReportProduceInfo(reportProduceId) {
            this.reportProduceId = reportProduceId
            reportProduceInfo({
                reportProduceId
            }).then((res) => {
                this.formData = res.result
                this.leftList = this.formData.leftList || []
                this.rightList = this.formData.rightList || []
                this.getLeftTotal()
                this.getrightTotal()
                this.getTotalProduct()
            })
        },
        changeDirectSplitWeight() {
            this.formData.hotWarehouseCollect = this.formData.directSplitWeight
            this.formData.hotLossWeight = this.isNum((+this.formData.directSplitWeight +
            +this.formData.hotBalanceCollect +
            +this.formData.hotSheepTailCollect -
            +this.formData.hotInFreezeWeight).toFixed(2))

            if ((+this.formData.directSplitWeight +
            +this.formData.hotBalanceCollect +
            +this.formData.hotSheepTailCollect) > 0) {
                this.formData.hotLossPercent = this.isNum((this.formData.hotLossWeight / 
                    (+this.formData.directSplitWeight +
                    +this.formData.hotBalanceCollect +
                    +this.formData.hotSheepTailCollect)).toFixed(2))
                this.formData.hotProducePercent = (this.formData.hotProducePercent * 100).toFixed(2)
            } else {
                this.formData.hotLossPercent = ''
            }
            
            if (this.formData.hotLossPercent > 0) {
                this.formData.hotProducePercent = this.isNum(100 - this.formData.hotLossPercent).toFixed(2)
                this.formData.hotLossPercent = (this.formData.hotLossPercent * 100).toFixed(2)
            } else {
                this.formData.hotProducePercent = ''
            }

            this.changeHotProducePercent()
            this.changetotalLossPercent()
        },
        changeHotProducePercent() {
            if ((+this.formData.directSplitWeight +
            +this.formData.hotBalanceCollect +
            +this.formData.hotSheepTailCollect) > 0) {
                this.formData.hotLossPercent = this.isNum((this.formData.hotLossWeight / 
                (+this.formData.directSplitWeight +
                +this.formData.hotBalanceCollect +
                +this.formData.hotSheepTailCollect)).toFixed(2))
                this.formData.hotProducePercent = (this.formData.hotProducePercent * 100).toFixed(2)
            } else {
                this.formData.hotLossPercent = ''
            }
            if (this.formData.hotLossPercent > 0) {
                this.formData.hotProducePercent = this.isNum(100 - this.formData.hotLossPercent).toFixed(2)
                this.formData.hotLossPercent = (this.formData.hotLossPercent * 100).toFixed(2)
            } else {
                this.formData.hotProducePercent = ''
            }
            this.changetotalLossPercent()
        },
        changeAcidSplitWeight() {
            this.formData.coldWarehouseCollect = this.formData.acidSplitWeight;
            this.formData.coldLossWeight = this.isNum((+this.formData.acidSplitWeight +
            +this.formData.coldBalanceCollect +
            +this.formData.coldSheepTailCollect -
            +this.formData.coldInFreezeWeight).toFixed(2))

            if((+this.formData.acidSplitWeight +
            +this.formData.coldBalanceCollect +
            +this.formData.coldSheepTailCollect) > 0) {
                this.formData.coldLossPercent = this.isNum((this.formData.coldLossWeight / 
                (+this.formData.acidSplitWeight +
                +this.formData.coldBalanceCollect +
                +this.formData.coldSheepTailCollect)).toFixed(2))
            } else {
                this.formData.coldLossPercent = ''
            }
            
            
            this.changeColdLossWeight()
            this.changetotalLossPercent()
        },
        changeColdLossWeight() {
            if ((+this.formData.acidSplitWeight +
            +this.formData.coldBalanceCollect +
            +this.formData.coldSheepTailCollect) > 0) {
                this.formData.coldLossPercent = this.isNum((this.formData.coldLossWeight / 
                (+this.formData.acidSplitWeight +
                +this.formData.coldBalanceCollect +
                +this.formData.coldSheepTailCollect)).toFixed(2))
                this.formData.coldLossPercent = (this.formData.coldLossPercent * 100).toFixed(2)
            } else {
                this.formData.coldLossPercent = ''
            }
            if(this.formData.coldLossPercent > 0) {
                this.formData.coldProducePercent = this.isNum(100 - this.formData.coldLossPercent).toFixed(2)
                this.formData.coldProducePercent = (this.formData.coldProducePercent * 100).toFixed(2)
            } else {
                this.formData.coldProducePercent = ''
            }
            this.changetotalLossPercent()
        },
        changeProcessingBalanceCollect() {
            this.formData.processingLossWeight = this.isNum((+this.formData.processingBalanceCollect +
            +this.formData.processingSheepTailCollect +
            +this.formData.processingWarehouseCollect -
            +this.formData.processingInFreezeWeight).toFixed(2))

            if (( +this.formData.processingSheepTailCollect +
            +this.formData.processingWarehouseCollect -
            +this.formData.processingInFreezeWeight) > 0) {
                this.formData.processingLossPercent = this.isNum((this.formData.processingLossWeight / 
                ( +this.formData.processingSheepTailCollect +
                +this.formData.processingWarehouseCollect -
                +this.formData.processingInFreezeWeight)).toFixed(2))
                this.formData.processingLossPercent = (this.formData.processingLossPercent * 100).toFixed(2)
            } else {
                this.formData.processingLossPercent = ''
            }
            
            
            this.changeProcessingLossWeight()
            this.changetotalLossPercent()
        },
        changeProcessingLossWeight() {
            if(( +this.formData.processingSheepTailCollect +
            +this.formData.processingWarehouseCollect -
            +this.formData.processingInFreezeWeight) > 0) {
                this.formData.processingLossPercent = this.isNum((this.formData.processingLossWeight / 
                ( +this.formData.processingSheepTailCollect +
                +this.formData.processingWarehouseCollect -
                +this.formData.processingInFreezeWeight)).toFixed(2))
                this.formData.processingLossPercent = (this.formData.processingLossPercent * 100).toFixed(2)
            } else {
                this.formData.processingLossPercent = ''
            }
            if (this.formData.processingLossPercent > 0) {
                this.formData.processingProducePercent = this.isNum(100 - this.formData.processingLossPercent).toFixed(2)
                this.formData.processingProducePercent = (this.formData.processingProducePercent * 100).toFixed(2)
            } else {
                this.formData.processingProducePercent = ''
            }
            this.changetotalLossPercent()
        },
        changetotalLossPercent() {
            this.formData.totalLossPercent = (+this.formData.totalBalanceCollect +
            +this.formData.totalSheepTailCollect +
            +this.formData.totalWarehouseCollect) > 0 ?this.isNum(this.formData.totalLossWeight /
            (+this.formData.totalBalanceCollect +
            +this.formData.totalSheepTailCollect +
            +this.formData.totalWarehouseCollect)).toFixed(0) : 0
            this.formData.totalProducePercent = this.isNum(1 - this.formData.totalLossPercent).toFixed(2)
            this.formData.totalProducePercent = (this.formData.totalProducePercent * 100).toFixed(2)
            this.formData.totalLossPercent = (this.formData.totalLossPercent * 100).toFixed(2)
            if (this.formData.totalProducePercent < 0) {
                this.formData.totalProducePercent = '0.00'
            }
            if (this.formData.totalLossPercent < 0) {
                this.formData.totalLossPercent = '0.00'
            }
        },
        getLeftTotal() {
            this.formData.leftProductNum = 0
            this.formData.leftProductWeight = 0
            if (this.leftList.length <= 0) { return }
            let leftProductNum = 0
            let leftProductWeight = 0
            this.leftList.forEach(item => {
                leftProductNum += +item.productNum
                leftProductWeight += +item.productWeight
            })
            this.formData.leftProductNum = leftProductNum.toFixed(0)
            this.formData.leftProductWeight = leftProductWeight.toFixed(2)
            this.getTotalProduct()
        },
        getrightTotal() {
            this.formData.rightProductNum = 0
            this.formData.rightProductWeight = '0.00'
            if (this.rightList.length <= 0) { return }
            let rightProductNum = 0
            let rightProductWeight = 0
            this.rightList.forEach(item => {
                rightProductNum += +item.productNum
                rightProductWeight += +item.productWeight
            })
            this.formData.rightProductNum = rightProductNum.toFixed(0)
            this.formData.rightProductWeight = rightProductWeight.toFixed(2)
            this.getTotalProduct()
        },
        getTotalProduct() {
            this.formData.totalProductNum = (+this.formData.leftProductNum + +this.formData.rightProductNum).toFixed(0)
            this.formData.totalProductWeight = (+this.formData.leftProductWeight + +this.formData.rightProductWeight).toFixed(2)
        },
        changeHotWarehouseCollect() {
            this.formData.directSplitWeight = this.formData.hotWarehouseCollect
            this.changeDirectSplitWeight()
        },
        changeColdWarehouseCollect() {
            this.formData.acidSplitWeight = this.formData.coldWarehouseCollect
            this.changeAcidSplitWeight()
        },
        isNum(value){
            return isNaN(value) ? '' : value
        },
        colse() {
            this.$emit('close')
        },
        submit() {
            if (!this.reportProduceId) {
                this.addForm()
            } else {
                this.updateForm()
            }
        },
        addForm() {
            reportProduceAdd({
                ...this.formData,
                leftList: this.leftList,
                rightList: this.rightList,
            }).then((res) => {
                this.$emit('close')
                this.$message.success('添加成功')
            })
        },
        updateForm() {
            reportProduceupdate({
                ...this.formData,
                leftList: this.leftList,
                rightList: this.rightList,
                reportProduceId: this.reportProduceId
            }).then((res) => {
                this.reportProduceId = ''
                this.$emit('close')
                this.$message.success('编辑成功')
            })
        },
    },
};
</script>
      
<style lang="scss" scoped>
.app-container{
    min-width: 1366px;
    padding: 0;
    padding-bottom: 80px;
}
.header-tips{
    font-size: 12px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 350;
    color: #666666;
    margin-top: 14px;
}
.header-box{
    width: 100%;
    height: 110px;
    background: #F2F3F5;
    padding: 20px 24px;
    margin-top: 16px;
    .time{
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #666666;
        span{
            display: inline-block;
            margin-right: 80px;
        }
    }
    .name{
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        margin-top: 20px;
        .input{
            margin-left: 5px;
            width: 500px;
            height: 32px;
            background: #FFFFFF;
            border-radius: 2px 2px 2px 2px;
            opacity: 1;
            border: 1px solid #EEEEEE;
        }
    }
}
.total-data{
    margin-top: 10px;
}
.table-header th{
    background: #FFFFFF;
    border-top: 1px solid #EDEDED;
    border-left: 1px solid #EDEDED;
    border-bottom: 1px solid #EDEDED;
    padding: 7px 0;
    font-size: 14px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: 700;
    color: #333333;
    line-height: 24px;
    // &:last-child{
        // border-right: 1px solid #EDEDED;
    // }
}

.border-r{
    border-right: 1px solid #EDEDED;
}
.total-data{
    margin-top: 10px;
    td{
        border-top: 1px solid #EDEDED;
        border-left: 1px solid #EDEDED;
        padding: 7px 0;
        font-size: 12px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 24px;
        padding-left: 5px;
        padding-right: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        // &:last-child{
        //     border-right: 1px solid #EDEDED;
        // }
    }
    .sub-title{
        background: #F5F7FA; 
        td{
            border: none;
            font-size: 14px;
            &:first-child{
                border-left: 1px solid #EDEDED;
            }
            &:last-child{
                border-right: 1px solid #EDEDED;
            }
        }
    }
}
// table {width: 200px;border-top: 1px solid #999;border-left: 1px solid #999;}
// table td {padding: 10px 30px;border-bottom: 1px solid #999;border-right: 1px solid #999;}
table {
    width: 100%;
    border-spacing: 0;
}
.tabel-input{
    color: #0C36FF;
    font-size: 12px;
    font-weight: 500;
    border: none;
    .el-input__inner{
        border: none;
    }
}
.content{
    td{
        width: 6.25%;
        box-sizing: border-box;
    }
}
.border-b{
    border-bottom: 1px solid #EDEDED;
}
.border-n{
    border-left: none !important;
}
.total-label{
    font-size: 14px !important;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500 !important;
}
.total-value{
    font-size: 14px !important;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500 !important;
    color: #F85300 !important;
}
.footer_btn{
    width: 100%;
}
</style>
<style lang="scss">
.tabel-input{
    color: #0C36FF;
    border: none;
    .el-input__inner{
        border: none;
        padding: 0;
        height: 15px;
        color: #0C36FF;
        font-size: 12px;
        font-weight: 700;
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
</style>