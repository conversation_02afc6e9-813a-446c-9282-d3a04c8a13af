<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                    <el-form-item label="" prop="warehouseId">
                          <el-select v-model="queryParams.warehouseId" placeholder="排酸库">
                              <el-option v-for="(item, index) in warehouseList1" :key="index" :label="item.warehouseName" :value="item.warehouseId" />
                          </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="原料名称" prop="materialsId">
                      <el-select v-model="queryParams.materialsId" >
                        <el-option
                          v-for="(item,index) in materialsList"
                          :label="item.materialsName"
                          :value="item.materialsId"
                          :key="index"
                        />
                      </el-select>
                    </el-form-item> -->
                    <el-form-item>
                      <el-date-picker
                        v-model="dateEnter"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="排酸开始日期"
                        end-placeholder="排酸结束日期"
                      ></el-date-picker>
                    </el-form-item>
              </el-row>
          </el-row>
          <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="fend mb8 form_btn">
        <el-button icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
      </el-row>

      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" stripe border v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip width="55" align="center" type="index" label="序号" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" width="140" prop="acidDate" sortable label="排酸日期" fixed="left"></el-table-column>
          <el-table-column  show-overflow-tooltip prop="warehouseName" width="140" label="排酸库" fixed="left"></el-table-column>
          <!-- <el-table-column  show-overflow-tooltip prop="materialsName" label="原料名称"></el-table-column> -->
          <el-table-column  show-overflow-tooltip align="right" width="180" prop="yesterdayNum" sortable label="上一日剩余数量"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="200" prop="yesterdayWeight" sortable label="上一日剩余重量（kg）">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark == '0' ? '-' : scope.row.yesterdayWeight}}
                  </span>
            </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="140" prop="inWarehouseNum" sortable label="入库数量"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="160" prop="inWarehouseWeight" sortable label="入库重量（kg）">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark == '0' ? '-' : scope.row.inWarehouseWeight}}
                  </span>
            </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="160" sortable prop="averageWeightBeforeAcid" label="排酸前均重（kg）">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark == '0' ? '-' : scope.row.averageWeightBeforeAcid}}
                  </span>
            </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="right" prop="outWarehouseNum" width="140" sortable label="出库数量"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="160" prop="outWarehouseWeight" sortable label="出库重量（kg）"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="140" prop="averageWeightAfterAcid" sortable label="排酸后均重(kg)"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="140" prop="lossWeight" sortable label="损耗重量(kg)">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark == '0' ? '-' : scope.row.lossWeight}}
                  </span>
            </template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="right" width="120" prop="lossWeightPercent" label="损耗率(%)">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark == '0' ? '-' : scope.row.lossWeightPercent}}
                  </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      /> -->
    </el-card>
      <div class="total">
        <p><label>入库总数：</label><span>{{ dataTotal.inWarehouseNum }}</span></p>
        <p><label>入库总重(kg)：</label><span>{{ dataTotal.remark == '0' ? '-' : dataTotal.inWarehouseWeight }}</span></p>
        <p><label>出库总数：</label><span>{{ dataTotal.outWarehouseNum }}</span></p>
        <p><label>出库总重(kg)：</label><span>{{ dataTotal.outWarehouseWeight }}</span></p>
        <p><label>排酸前均重(kg)：</label><span>{{ dataTotal.remark == '0' ? '-' : dataTotal.averageWeightBeforeAcid }}</span></p>
        <p><label>排酸后均重(kg)：</label><span>{{ dataTotal.averageWeightAfterAcid }}</span></p>
        <p><label>排酸损耗(kg)：</label><span>{{ dataTotal.remark == '0' ? '-' : dataTotal.lossWeight }}</span></p>
        <p><label>损耗率(%)：</label><span>{{ dataTotal.remark == '0' ? '-' : dataTotal.lossWeightPercent }}</span></p>
      </div>
  </div>
</template>
      
<script>
import { materialsList } from "@/api/acquisition/index";
import { acidTask, acidTaskTotal, acidTaskExport } from "@/api/statistics.js";
import { warehouseList } from "@/api/basics/index.js";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  data() {
    return {
      queryParams: {
        warehouseId: "",
        // materialsId:'',
        pageNum: 1,
        pageSize: 10
      },
      warehouseList1: [],
      materialsList: [], //原料名称
      tableData: [],
      loading: true,
      dateEnter: [],
      total: 0,
      dataTotal: {},
      windowHeight: 0,
    };
  },
  created() {
    this.getMaterial();
    this.getList();
    this.getwarehouseList()
  },
  methods: {
    refresh() {
      this.getList();
    },
    getMaterial() {
      materialsList({}).then((res) => {
        if (res.code == 200) {
          this.materialsList = res.result.list || [];
        }
      });
    },
    //列表查询
    getList() {
      acidTask(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.statisticsList;
          this.dataTotal = res.result;
          // this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    //列表排酸库查询
    getwarehouseList() {
        warehouseList({
            pageNum: 1,
            pageSize: 10000,
            warehouseType: 1,
            requestRole: 2,
        }).then((res) => {
            if (res.code == 200) {
                this.warehouseList1 = res.result.list
            }
        });
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateEnter = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.getList();
    },
    exportList(){exportExcel(acidTaskExport,this.queryParams,'排酸统计')},
  },
};
</script>
      
<style lang="scss" scoped>
  .total{
    position: absolute;
    bottom: 0;
    display: flex;
    width: 100%;
    height: 45px;
    background: #FFFFFF;
    box-shadow: 0px 0px 10px 0px rgba(206,206,206,0.302);
    padding-left: 50px;
    font-size: 14px;
    line-height: 45px;
    p{
      margin: 0;
      margin-right: 24px;
      color: #666666;
      span{
        color: #1D1D1F;
      }
    }
  }
</style>
      