<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
            <el-row class=" form_row">
                <el-row class="form_col">
                <el-form-item prop="transactionCode">
                  <el-input v-model="queryParams.transactionCode" placeholder="交易流水号"  />
                </el-form-item>
                <el-form-item prop="supplierName">
                  <el-input v-model="queryParams.supplierName" placeholder="供应商"  />
                </el-form-item>
                <el-form-item prop="supplierType">
                  <el-select v-model="queryParams.supplierType" placeholder="供应商类型" >
                    <el-option
                      v-for="dict in dict.type.mes_supplier_type"
                      :key="dict.value"
                      :label="dict.value"
                      :value="dict.label"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="支付方式" prop="finalButcherNum">
                  <el-select v-model="queryParams.finalButcherNum" >
                    <el-option
                      v-for="(item,index) in payList"
                      :label="item.label"
                      :value="item.value"
                      :key="index"
                    />
                  </el-select>
                </el-form-item> -->
                <el-form-item>
                  <el-date-picker
                    v-model="dateEnter"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="采购开始日期"
                    end-placeholder="采购结束日期"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-date-picker
                    v-model="dateSettle"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="结算开始日期"
                    end-placeholder="结算结束日期"
                  ></el-date-picker>
                </el-form-item>
              </el-row>
          </el-row>
          <el-row>
              <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <template v-if="toggleSearchDom">
                      <el-button type="text" @click="packUp">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <i
                          :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                          ></i>
                      </el-button>
                  </template>
              </el-form-item>
          </el-row>
        </el-form>
    </el-card>
    <el-card shadow="never" class="table_box">
      <el-row :gutter="10" class="fend mb8 form_btn">
        <el-button icon="el-icon-download" class="default_btn" size="mini" @click="exportList">导出数据</el-button>
      </el-row>

      <!-- 表格数据 -->
      <div :style="{height: tableHeight + 'px'}">
        <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading" :max-height="tableHeight">
          <el-table-column  show-overflow-tooltip align="center" type="index" width="55" fixed="left" label="序号"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="140" label="结算单号" fixed="left" prop="settlementCode"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="140" label="交易流水号" fixed="left" prop="transactionCode"></el-table-column>
          <el-table-column  show-overflow-tooltip min-width="120" label="供应商" prop="supplierName"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="120" label="供应商类型" > <template slot-scope="scope">{{ handeltext(supplierTypeList,scope.row.supplierType) }}</template></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="120" label="联系电话" prop="supplierContactPhone"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" sortable min-width="140" label="采购日期" prop="checkInTime"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable min-width="140" label="结算数量" prop="finalButcherNum"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable min-width="140" label="称重金额" prop="payableAmount"></el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable min-width="140" label="补扣款金额" >
              <template slot-scope="scope">{{( (parseFloat(scope.row.subsidyAmount)*100-parseFloat(scope.row.deductAmount)*100)/100).toFixed(2) }}</template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="right" sortable min-width="140" label="结算金额" prop="finalAmount"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="120" label="支付方式">
            <template slot-scope="scope">{{ scope.row.paymentType == 22 ? '我的额度' : '线下结算' }}</template>
          </el-table-column>
          <el-table-column  show-overflow-tooltip align="center" min-width="120" label="负责人" prop="updateUserName"></el-table-column>
          <el-table-column  show-overflow-tooltip align="center" prop="settlementTime" min-width="140" label="结算时间"></el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
      
      <script>
import { settlementList } from "@/api/settlementManage/index";
import { settlementExport } from "@/api/statistics.js";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  dicts: ["mes_supplier_type"],
  data() {
    return {
      queryParams: {
        supplierName:'',
        transactionCode:'',
        settlementStatus: "1",
        supplierType:'',
        pageNum: 1,
        pageSize: 10,
      },
      materialsList: [], //原料名称
      supplierTypeList: [
        { label: "企业", value: 1 },
        { label: "中间商", value: 2 },
        { label: "养殖户", value: 3 },
      ],
      payList: [
        { label: "额度支付", value: "1" },
        { label: "快捷支付", value: "2" },
        { label: "其他", value: "3" },
      ], //原料等级
      tableData: [],
      loading: true,
      dateEnter: [],
      dateSettle: [],
      total: 0,
      sreachShow: false,
    };
  },
  computed: {
    handeltext(){
        return (list,value)=>{
            let name=''
            list.forEach(item=>{
                if(item.value==value){
                    name=item.label
                }
            })
            return name
        }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    exportList(){exportExcel(settlementExport,this.queryParams,'结算单统计')},
    //列表查询
    getList() {
      settlementList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset(){
        this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateEnter = [];
      this.dateSettle = []
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.handelData("startTime2", "endTime2", this.dateSettle);
      this.getList();
    },
  },
};
</script>
      
      <style lang="scss" scoped>
</style>
      