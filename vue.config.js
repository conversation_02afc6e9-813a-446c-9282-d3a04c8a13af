'use strict'
const path = require('path')

function resolve(dir) {
    return path.join(__dirname, dir)
}

const CompressionPlugin = require('compression-webpack-plugin')

const name = process.env.VUE_APP_TITLE || '快速开发管理系统' // 网页标题

const port = process.env.port || process.env.npm_config_port || 81 // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.east-mind.com/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.eat-mind.com/admin/，则设置 baseUrl 为 /admin/。
    publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
    // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
    outputDir: 'mesapp',
    // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
    assetsDir: 'static',
    // 是否开启eslint保存检测，有效值：ture | false | 'error'
    lintOnSave: false,
    // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
    productionSourceMap: false,
    // webpack-dev-server 相关配置
    devServer: {
        host: '0.0.0.0',
        port: port,
        headers: {
            "Access-Control-Allow-Origin": "*"
        },
        proxy: {
            // detail: https://cli.vuejs.org/config/#devserver-proxy
            //'/api': {
            // target: `http://*************:7100/`,
            //  target: `http://*************:83/`,
            //  changeOrigin: true,
            //  pathRewrite: {
            //   '/api': ''
            //  }
            [process.env.VUE_APP_BASE_API]: {
                // target: `http://**************:7100/`,
                target: `http://*************:7100/`,
                // target: `http://*************:7100/`,
                changeOrigin: true,
                pathRewrite: {
                    ['^' + process.env.VUE_APP_BASE_API]: ''
                }
            },
        },
        disableHostCheck: true 
    },

    configureWebpack: {
        name: name,
        resolve: {
            alias: {
                '@': resolve('src')
            }
        },
        plugins: [
            new CompressionPlugin({
                test: /\.(js|css|html)?$/i,     // 压缩文件格式
                filename: '[path].gz[query]',   // 压缩后的文件名
                algorithm: 'gzip',              // 使用gzip压缩
                minRatio: 0.8                   // 压缩率小于1才会压缩
            })
        ],
        output: {
            library: 'east-mind-mesapp-ui',
            libraryTarget: 'window'
        }
    },

}
